import{_ as j,d as b,l as R,e as $,f as J,I as O,c as y,o as c,a as t,b as s,w as a,i as u,r as m,u as w,C as U,t as n,m as g,v as X,F as G,j as H,y as K,J as Q,E as h}from"./index-Di-G_xQb.js";const W={class:"pay-detail-container"},Z={class:"page-header"},tt={class:"header-content"},et={class:"page-title"},st={class:"header-actions"},at={class:"content-section"},nt={class:"basic-info"},ot={class:"info-item"},lt={class:"value"},it={class:"info-item"},dt={class:"value"},mt={class:"info-item"},ct={class:"value"},ut={class:"info-item"},rt={class:"value"},pt={class:"info-item"},_t={class:"value"},yt={class:"info-item"},ft={class:"value"},vt={class:"info-item"},ht={class:"value"},bt={class:"info-item"},gt={class:"value amount"},Nt={class:"info-item"},At={class:"value amount"},kt={class:"info-item"},Tt={class:"value amount pending"},Pt={class:"info-item"},Mt={class:"content-section"},wt={class:"material-detail"},Dt={class:"material-summary"},Vt={class:"summary-content"},Ct={class:"summary-columns"},It={class:"summary-column"},xt={class:"summary-item"},St={class:"summary-value"},Bt={class:"summary-column"},Yt={class:"summary-item total"},qt={class:"summary-value"},zt={class:"content-section"},Et={class:"attachment-notes"},Ft={class:"attachments-section"},Lt={key:0,class:"attachment-list"},jt={class:"attachment-name"},Rt={class:"attachment-size"},$t={class:"notes-section"},Jt={key:0,class:"notes-content"},Ot={__name:"payDetail",setup(Ut){const D=K(),V=O(),N=b("attachments"),o=R({paymentNo:"PAY20240501001",customerName:"深圳科技有限公司",paymentMethod:"银行转账",paymentTerms:"月结30天",createTime:"2024-05-01 10:30:00",statementNo:"STMT20240501001",completedDate:"",totalAmount:187500,paidAmount:1e5,pendingAmount:87500,status:"pending",remarks:"请注意付款时间，按合同约定执行。如有疑问请及时联系财务部门。"}),A=b([{id:1,materialName:"智能控制器",model:"CT-X100",brand:"SelecTech",quantity:50,unitPrice:2500,totalPrice:125e3},{id:2,materialName:"传感器模块",model:"SM-200",brand:"TechSense",quantity:100,unitPrice:500,totalPrice:5e4},{id:3,materialName:"显示屏",model:"DS-15",brand:"DisplayTech",quantity:25,unitPrice:500,totalPrice:12500}]),k=b([{id:1,name:"付款凭证.pdf",size:"2.3MB",url:"/files/payment_voucher.pdf"},{id:2,name:"合同扫描件.pdf",size:"5.1MB",url:"/files/contract_scan.pdf"}]),T=$(()=>{const l=A.value.reduce((p,i)=>p+i.totalPrice,0),e=500,f=l+e;return{materialsTotal:l,freight:e,totalAmount:f}}),r=l=>l==null?"0.00":l.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),C=l=>({pending:"warning",partial:"info",completed:"success",overdue:"danger"})[l]||"info",I=l=>({pending:"待付款",partial:"部分付款",completed:"已完成",overdue:"逾期"})[l]||"未知",x=()=>{D.push("/trade/sale/pay")},S=()=>{h.success("付款完成功能开发中...")},B=()=>{h.success("打印功能开发中...")},Y=()=>{h.success("导出功能开发中...")},q=l=>{h.success(`下载附件：${l.name}`)},z=l=>{const e={1:{paymentNo:"PAY20240501001",customerName:"北京科技有限公司",paymentMethod:"现金/电汇",paymentTerms:"月结30天",createTime:"2024-05-01 10:00:00",statementNo:"INV20240501001",completedDate:"",totalAmount:58600,paidAmount:0,pendingAmount:58600,status:"pending",remarks:"请按合同约定时间付款。"},2:{paymentNo:"PAY20240501002",customerName:"上海工贸公司",paymentMethod:"银行承兑汇票",paymentTerms:"预付款",createTime:"2024-05-01 11:30:00",statementNo:"INV20240501002",completedDate:"2024-05-15 14:20:00",totalAmount:23250,paidAmount:23250,pendingAmount:0,status:"completed",remarks:"付款已完成，感谢合作。"},3:{paymentNo:"PAY20240501003",customerName:"深圳制造集团",paymentMethod:"现金/电汇",paymentTerms:"月结",createTime:"2024-05-01 14:15:00",statementNo:"INV20240501003",completedDate:"2024-05-15 16:30:00",totalAmount:42100,paidAmount:42100,pendingAmount:0,status:"completed",remarks:"合作愉快。"},4:{paymentNo:"PAY20240401001",customerName:"广州贸易公司",paymentMethod:"银行承兑汇票",paymentTerms:"月结15天",createTime:"2024-04-01 16:45:00",statementNo:"INV20240401001",completedDate:"",totalAmount:16500,paidAmount:0,pendingAmount:16500,status:"overdue",remarks:"付款已逾期，请尽快处理。"},5:{paymentNo:"PAY20240502001",customerName:"天津实业有限公司",paymentMethod:"现金/电汇",paymentTerms:"预付款",createTime:"2024-05-02 09:00:00",statementNo:"INV20240502001",completedDate:"",totalAmount:35800,paidAmount:0,pendingAmount:35800,status:"pending",remarks:"新客户，请注意风险控制。"},6:{paymentNo:"PAY20240502002",customerName:"杭州科技发展公司",paymentMethod:"银行承兑汇票",paymentTerms:"月结60天",createTime:"2024-05-02 11:20:00",statementNo:"INV20240502002",completedDate:"2024-05-10 14:45:00",totalAmount:28900,paidAmount:28900,pendingAmount:0,status:"completed",remarks:"长期合作客户，信用良好。"}};return e[l]||e[1]};return J(()=>{const l=V.params.id,e=z(l);Object.assign(o,e)}),(l,e)=>{const f=m("el-icon"),p=m("el-button"),i=m("el-col"),v=m("el-row"),E=m("el-tag"),_=m("el-table-column"),F=m("el-table"),P=m("el-empty"),M=m("el-tab-pane"),L=m("el-tabs");return c(),y("div",W,[t("div",Z,[t("div",tt,[s(p,{onClick:x,type:"text",class:"back-button"},{default:a(()=>[s(f,null,{default:a(()=>[s(w(U))]),_:1}),e[1]||(e[1]=u(" 返回付款列表 "))]),_:1}),t("h2",et,"付款详情 - "+n(o.paymentNo),1)]),t("div",st,[o.status==="pending"?(c(),g(p,{key:0,type:"primary",onClick:S},{default:a(()=>e[2]||(e[2]=[u(" 完成付款 ")])),_:1})):X("",!0),s(p,{type:"info",onClick:B},{default:a(()=>e[3]||(e[3]=[u("打印")])),_:1}),s(p,{type:"primary",onClick:Y},{default:a(()=>e[4]||(e[4]=[u("导出")])),_:1})])]),t("div",at,[e[16]||(e[16]=t("div",{class:"section-header"},[t("h3",null,"基本信息")],-1)),t("div",nt,[s(v,{gutter:24},{default:a(()=>[s(i,{span:8},{default:a(()=>[t("div",ot,[e[5]||(e[5]=t("span",{class:"label"},"客户名称：",-1)),t("span",lt,n(o.customerName),1)])]),_:1}),s(i,{span:8},{default:a(()=>[t("div",it,[e[6]||(e[6]=t("span",{class:"label"},"付款单号：",-1)),t("span",dt,n(o.paymentNo),1)])]),_:1}),s(i,{span:8},{default:a(()=>[t("div",mt,[e[7]||(e[7]=t("span",{class:"label"},"付款方式：",-1)),t("span",ct,n(o.paymentMethod),1)])]),_:1})]),_:1}),s(v,{gutter:24},{default:a(()=>[s(i,{span:8},{default:a(()=>[t("div",ut,[e[8]||(e[8]=t("span",{class:"label"},"付款条件：",-1)),t("span",rt,n(o.paymentTerms),1)])]),_:1}),s(i,{span:8},{default:a(()=>[t("div",pt,[e[9]||(e[9]=t("span",{class:"label"},"创建时间：",-1)),t("span",_t,n(o.createTime),1)])]),_:1}),s(i,{span:8},{default:a(()=>[t("div",yt,[e[10]||(e[10]=t("span",{class:"label"},"所属对账单：",-1)),t("span",ft,n(o.statementNo||"无"),1)])]),_:1})]),_:1}),s(v,{gutter:24},{default:a(()=>[s(i,{span:8},{default:a(()=>[t("div",vt,[e[11]||(e[11]=t("span",{class:"label"},"付款完成日期：",-1)),t("span",ht,n(o.completedDate||"未完成"),1)])]),_:1}),s(i,{span:8},{default:a(()=>[t("div",bt,[e[12]||(e[12]=t("span",{class:"label"},"应付总额：",-1)),t("span",gt,"¥"+n(r(o.totalAmount)),1)])]),_:1}),s(i,{span:8},{default:a(()=>[t("div",Nt,[e[13]||(e[13]=t("span",{class:"label"},"实付总额：",-1)),t("span",At,"¥"+n(r(o.paidAmount)),1)])]),_:1})]),_:1}),s(v,{gutter:24},{default:a(()=>[s(i,{span:8},{default:a(()=>[t("div",kt,[e[14]||(e[14]=t("span",{class:"label"},"待付总额：",-1)),t("span",Tt,"¥"+n(r(o.pendingAmount)),1)])]),_:1}),s(i,{span:8},{default:a(()=>[t("div",Pt,[e[15]||(e[15]=t("span",{class:"label"},"付款状态：",-1)),s(E,{type:C(o.status),class:"value"},{default:a(()=>[u(n(I(o.status)),1)]),_:1},8,["type"])])]),_:1})]),_:1})])]),t("div",Mt,[e[19]||(e[19]=t("div",{class:"section-header"},[t("div",{class:"section-title-wrapper"},[t("h3",null,"物料信息")])],-1)),t("div",wt,[s(F,{data:A.value,border:"",style:{width:"100%"}},{default:a(()=>[s(_,{prop:"materialName",label:"物料名称","min-width":"180"}),s(_,{prop:"model",label:"型号","min-width":"120"}),s(_,{prop:"brand",label:"品牌","min-width":"100"}),s(_,{prop:"quantity",label:"数量",width:"100",align:"center"}),s(_,{prop:"unitPrice",label:"单价",width:"120",align:"right"},{default:a(d=>[u(" ¥"+n(r(d.row.unitPrice)),1)]),_:1}),s(_,{prop:"totalPrice",label:"小计",width:"120",align:"right"},{default:a(d=>[u(" ¥"+n(r(d.row.totalPrice)),1)]),_:1})]),_:1},8,["data"]),t("div",Dt,[t("div",Vt,[t("div",Ct,[t("div",It,[t("div",xt,[e[17]||(e[17]=t("span",{class:"summary-label"},"运费：",-1)),t("span",St,"¥"+n(r(T.value.freight)),1)])]),t("div",Bt,[t("div",Yt,[e[18]||(e[18]=t("span",{class:"summary-label"},"总金额：",-1)),t("span",qt,"¥"+n(r(T.value.totalAmount)),1)])])])])])])]),t("div",zt,[e[21]||(e[21]=t("div",{class:"section-header"},[t("h3",null,"附件与备注")],-1)),t("div",Et,[s(L,{modelValue:N.value,"onUpdate:modelValue":e[0]||(e[0]=d=>N.value=d),class:"info-tabs"},{default:a(()=>[s(M,{label:"附件",name:"attachments"},{default:a(()=>[t("div",Ft,[k.value.length>0?(c(),y("div",Lt,[(c(!0),y(G,null,H(k.value,d=>(c(),y("div",{key:d.id,class:"attachment-item"},[s(f,{class:"attachment-icon"},{default:a(()=>[s(w(Q))]),_:1}),t("span",jt,n(d.name),1),t("span",Rt,n(d.size),1),s(p,{type:"text",onClick:Xt=>q(d)},{default:a(()=>e[20]||(e[20]=[u("下载")])),_:2},1032,["onClick"])]))),128))])):(c(),g(P,{key:1,description:"暂无附件"}))])]),_:1}),s(M,{label:"备注",name:"notes"},{default:a(()=>[t("div",$t,[o.remarks?(c(),y("div",Jt,n(o.remarks),1)):(c(),g(P,{key:1,description:"暂无备注"}))])]),_:1})]),_:1},8,["modelValue"])])])])}}},Ht=j(Ot,[["__scopeId","data-v-1bfb2106"]]);export{Ht as default};
