import{_ as G,d as h,l as q,e as j,r as a,c as v,o as c,a as N,b as e,w as t,i as u,u as X,z as Z,F as O,j as B,t as g,m as U,v as A,A as Y,K as R,E as K,f as H,g as ee,h as te}from"./index-Di-G_xQb.js";const le={style:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},oe={style:{padding:"0 20px"}},ne={style:{padding:"10px 20px"}},ae={__name:"OrderView",setup(E){const p=h(!1),C=h({}),w=n=>n==null?"0.00":n.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),y=q([{prop:"poNo",label:"PO号",visible:!0,minWidth:180},{prop:"customer",label:"客户",visible:!0,minWidth:120},{prop:"supplier",label:"供应商",visible:!0,minWidth:150},{prop:"orderTime",label:"下单时间",visible:!0,minWidth:160},{prop:"endTime",label:"结束时间",visible:!0,minWidth:160},{prop:"orderStatus",label:"状态",visible:!0,minWidth:120},{prop:"salesOrderNo",label:"所属销售单号",visible:!0,minWidth:180},{prop:"productCount",label:"商品数量",visible:!0,minWidth:100},{prop:"totalAmount",label:"总金额",visible:!0,minWidth:120}]),b=q([{prop:"productName",label:"商品名称",visible:!0,minWidth:150},{prop:"model",label:"型号",visible:!0,minWidth:120},{prop:"brand",label:"品牌",visible:!0,minWidth:100},{prop:"category",label:"分类",visible:!0,minWidth:100},{prop:"totalQuantity",label:"总数量",visible:!0,minWidth:100},{prop:"shippedQuantity",label:"已发货数量",visible:!0,minWidth:120},{prop:"acceptedQuantity",label:"已验收数量",visible:!0,minWidth:120},{prop:"cancelledQuantity",label:"已取消数量",visible:!0,minWidth:120},{prop:"logisticsStatus",label:"物流状态",visible:!0,minWidth:100},{prop:"unitPrice",label:"单价（¥）",visible:!0,minWidth:100},{prop:"totalPrice",label:"总价（¥）",visible:!0,minWidth:100}]),f=j(()=>y.filter(n=>n.visible)),k=j(()=>b.filter(n=>n.visible)),D=h([{id:"ord1",poNo:"PO20240501001",customer:"客户A",supplier:"供应商X",orderTime:"2024-05-01 10:00:00",endTime:"2024-05-10 18:00:00",orderStatus:"pending_shipment",salesOrderNo:"SO20240501A",productCount:2,totalAmount:205e3,products:[{id:"p1-1",productName:"高性能CPU",model:"CPU-INTEL-I9",brand:"Intel",category:"处理器",totalQuantity:50,shippedQuantity:15,acceptedQuantity:15,logisticsStatus:"部分发货",cancelledQuantity:5,unitPrice:2500,totalPrice:125e3},{id:"p1-2",productName:"超高速SSD",model:"SSD-SAM-2TB",brand:"Samsung",category:"存储",totalQuantity:20,shippedQuantity:5,acceptedQuantity:3,logisticsStatus:"待发货",cancelledQuantity:2,unitPrice:4e3,totalPrice:8e4}]},{id:"ord2",poNo:"PO20240428002",customer:"客户B",supplier:"供应商Y",orderTime:"2024-04-28 14:30:00",endTime:"2024-05-05 12:00:00",orderStatus:"shipped",salesOrderNo:"SO20240428B",productCount:1,totalAmount:8e4,products:[{id:"p2-1",productName:"大容量内存条",model:"MEM-KING-32G",brand:"Kingston",category:"内存",totalQuantity:100,shippedQuantity:30,acceptedQuantity:20,logisticsStatus:"全部发货",cancelledQuantity:10,unitPrice:800,totalPrice:8e4}]},{id:"ord3",poNo:"PO20240502003",customer:"客户C",supplier:"供应商Z",orderTime:"2024-05-02 09:00:00",endTime:"2024-05-12 17:00:00",orderStatus:"pending_confirmation",salesOrderNo:"SO20240502C",productCount:1,totalAmount:5e3,products:[{id:"p3-1",productName:"标准键盘",model:"KBD-LOGI-K120",brand:"Logitech",category:"外设",totalQuantity:10,shippedQuantity:0,acceptedQuantity:0,logisticsStatus:"未发货",cancelledQuantity:0,unitPrice:500,totalPrice:5e3}]}]),W=(n,o)=>{console.log("Expanded order:",n,"All expanded rows:",o)},s=(n,o)=>{C.value[o.id]=n,console.log(`Selected products for order ${o.poNo}:`,n)},V=n=>{n==="confirm"&&console.log("Batch confirm clicked")},S=(n,o)=>{console.log("Cancelling product:",n,"from order:",o)},d=n=>{console.log("Cancel order:",n)},i=n=>({pending_payment:"待付款",pending_shipment:"待发货",pending_confirmation:"待确认",shipped:"已发货",completed:"已完成",cancelled:"已取消"})[n]||n,T=n=>({pending_payment:"warning",pending_shipment:"info",pending_confirmation:"primary",shipped:"",completed:"success",cancelled:"danger"})[n]||"default";return(n,o)=>{const _=a("el-icon"),P=a("el-button"),x=a("el-dropdown-item"),I=a("el-dropdown-menu"),m=a("el-dropdown"),$=a("el-checkbox"),F=a("el-divider"),z=a("el-drawer"),Q=a("el-table-column"),M=a("el-table"),L=a("el-tag");return c(),v("div",null,[N("div",le,[e(m,{onCommand:V},{dropdown:t(()=>[e(I,null,{default:t(()=>[e(x,{command:"confirm"},{default:t(()=>o[3]||(o[3]=[u("确认")])),_:1}),e(x,{command:"delivery"},{default:t(()=>o[4]||(o[4]=[u("送货")])),_:1})]),_:1})]),default:t(()=>[e(P,{type:"primary"},{default:t(()=>[o[2]||(o[2]=u(" 批量操作")),e(_,{class:"el-icon--right"},{default:t(()=>[e(X(Z))]),_:1})]),_:1})]),_:1}),e(P,{onClick:o[0]||(o[0]=l=>p.value=!0),type:"primary"},{default:t(()=>o[5]||(o[5]=[u(" 配置列 ")])),_:1})]),e(z,{title:"配置列",modelValue:p.value,"onUpdate:modelValue":o[1]||(o[1]=l=>p.value=l),direction:"rtl",size:"300px"},{default:t(()=>[N("div",oe,[o[6]||(o[6]=N("h4",null,"订单列",-1)),(c(!0),v(O,null,B(y,l=>(c(),v("div",{key:l.prop},[e($,{modelValue:l.visible,"onUpdate:modelValue":r=>l.visible=r},{default:t(()=>[u(g(l.label),1)]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),128)),e(F),o[7]||(o[7]=N("h4",null,"商品列",-1)),(c(!0),v(O,null,B(b,l=>(c(),v("div",{key:l.prop},[e($,{modelValue:l.visible,"onUpdate:modelValue":r=>l.visible=r},{default:t(()=>[u(g(l.label),1)]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),128))])]),_:1},8,["modelValue"]),e(M,{data:D.value,style:{width:"100%"},border:"","row-key":"id",onExpandChange:W},{default:t(()=>[e(Q,{type:"expand"},{default:t(l=>[N("div",ne,[e(M,{data:l.row.products,border:"",style:{width:"100%"},onSelectionChange:r=>s(r,l.row)},{default:t(()=>[e(Q,{type:"selection",width:"55"}),(c(!0),v(O,null,B(k.value,r=>(c(),v(O,{key:r.prop},[r.visible?(c(),U(Q,{key:0,prop:r.prop,label:r.label,"min-width":r.minWidth},Y({_:2},[r.prop==="unitPrice"||r.prop==="totalPrice"?{name:"default",fn:t(J=>[u(" ¥"+g(w(J.row[r.prop])),1)]),key:"0"}:void 0]),1032,["prop","label","min-width"])):A("",!0)],64))),128)),e(Q,{label:"操作","min-width":"100",fixed:"right"},{default:t(r=>[e(P,{size:"small",link:"",type:"danger",onClick:J=>S(r.row,l.row)},{default:t(()=>o[8]||(o[8]=[u("取消")])),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data","onSelectionChange"])])]),_:1}),(c(!0),v(O,null,B(f.value,l=>(c(),v(O,{key:l.prop},[l.visible?(c(),U(Q,{key:0,prop:l.prop,label:l.label,"min-width":l.minWidth},Y({_:2},[l.prop==="totalAmount"?{name:"default",fn:t(r=>[u(" ¥"+g(w(r.row[l.prop])),1)]),key:"0"}:l.prop==="orderStatus"?{name:"default",fn:t(r=>[e(L,{type:T(r.row.orderStatus)},{default:t(()=>[u(g(i(r.row.orderStatus)),1)]),_:2},1032,["type"])]),key:"1"}:void 0]),1032,["prop","label","min-width"])):A("",!0)],64))),128)),e(Q,{label:"操作","min-width":"100",fixed:"right"},{default:t(l=>[e(P,{size:"small",type:"danger",link:"",onClick:r=>d(l.row)},{default:t(()=>o[9]||(o[9]=[u("取消")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])}}},ie=G(ae,[["__scopeId","data-v-d6291851"]]),de={style:{"margin-top":"20px","text-align":"right"}},re={class:"dialog-footer"},ue={__name:"DeliveryModal",props:{visible:{type:Boolean,default:!1},productsToShip:{type:Array,default:()=>[]},initialCustomerInfo:{type:Object,default:()=>({name:"",contact:"",address:""})}},emits:["update:visible","confirm-shipment"],setup(E,{emit:p}){const C=E,w=p,y=h(null),b=q({deliveryDate:null,notes:""}),f=q({name:"",contact:"",address:""}),k=h([]);R(()=>C.initialCustomerInfo,d=>{d&&(f.name=d.name||"",f.contact=d.contact||"",f.address=d.address||"")},{immediate:!0,deep:!0}),R(()=>C.productsToShip,d=>{d&&d.length>0?k.value=d.map(i=>({...i,brand:i.brand||"",category:i.category||"",unitPrice:i.unitPrice!=null?i.unitPrice:null,orderId:i.orderId||"",quantityToShip:Math.min(i.pendingStockQuantity>0?1:0,i.pendingStockQuantity)})):k.value=[],y.value,b.deliveryDate=null,b.notes=""},{immediate:!0,deep:!0});const D=d=>{d.quantityToShip<0&&(d.quantityToShip=0),d.quantityToShip>d.pendingStockQuantity&&(d.quantityToShip=d.pendingStockQuantity,K.warning(`发货数量不能超过可发货数量 ${d.pendingStockQuantity}`))},W=j(()=>k.value.filter(d=>d.quantityToShip>0).length),s=j(()=>k.value.reduce((d,i)=>d+(i.quantityToShip||0),0)),V=()=>{w("update:visible",!1)},S=async()=>{if(!y.value)return;if(s.value===0){K.error("总发货数量不能为0，请至少为一个商品指定发货数量。");return}if(!b.deliveryDate){K.error("请选择送货日期。");return}if(!f.name||!f.address||!f.contact){K.error("请填写完整的客户信息。");return}const d={customerInfo:{...f},deliveryDate:b.deliveryDate,notes:b.notes,items:k.value.filter(i=>i.quantityToShip>0).map(i=>({productId:i.id,productName:i.productName,model:i.model,brand:i.brand,category:i.category,unitPrice:i.unitPrice,orderId:i.orderId,shippedQuantity:i.quantityToShip})),totalItems:W.value,totalQuantity:s.value};console.log("Confirming shipment:",d),w("confirm-shipment",d),V(),K.success("发货指令已发送")};return(d,i)=>{const T=a("el-divider"),n=a("el-descriptions-item"),o=a("el-descriptions"),_=a("el-table-column"),P=a("el-input-number"),x=a("el-table"),I=a("el-date-picker"),m=a("el-form-item"),$=a("el-col"),F=a("el-row"),z=a("el-input"),Q=a("el-form"),M=a("el-button"),L=a("el-dialog");return c(),U(L,{"model-value":E.visible,title:"生成送货单",width:"70%",onClose:V,"close-on-click-modal":!1},{footer:t(()=>[N("span",re,[e(M,{onClick:V},{default:t(()=>i[6]||(i[6]=[u("取消")])),_:1}),e(M,{type:"primary",onClick:S},{default:t(()=>i[7]||(i[7]=[u("确认")])),_:1})])]),default:t(()=>[e(Q,{ref_key:"deliveryFormRef",ref:y,model:b,"label-width":"120px"},{default:t(()=>[e(T,{"content-position":"left"},{default:t(()=>i[2]||(i[2]=[u(" 客户信息 ")])),_:1}),e(o,{column:2,border:""},{default:t(()=>[e(n,{label:"客户名称"},{default:t(()=>[u(g(f.name),1)]),_:1}),e(n,{label:"联系方式"},{default:t(()=>[u(g(f.contact),1)]),_:1}),e(n,{label:"收货地址",span:2},{default:t(()=>[u(g(f.address),1)]),_:1})]),_:1}),e(T,{"content-position":"left"},{default:t(()=>i[3]||(i[3]=[u(" 商品列表 ")])),_:1}),e(x,{data:k.value,style:{width:"100%"},border:""},{default:t(()=>[e(_,{prop:"productName",label:"商品名称","min-width":"150"}),e(_,{prop:"model",label:"型号","min-width":"120"}),e(_,{prop:"brand",label:"品牌","min-width":"100"}),e(_,{prop:"category",label:"分类","min-width":"100"}),e(_,{prop:"unitPrice",label:"单价","min-width":"100"},{default:t(l=>[u(g(l.row.unitPrice!=null?`¥${l.row.unitPrice.toFixed(2)}`:"-"),1)]),_:1}),e(_,{prop:"orderId",label:"所属订单号","min-width":"120"}),e(_,{prop:"pendingStockQuantity",label:"可发货数量","min-width":"100"},{default:t(l=>[u(g(l.row.pendingStockQuantity),1)]),_:1}),e(_,{label:"本次发货数量","min-width":"150"},{default:t(l=>[e(P,{modelValue:l.row.quantityToShip,"onUpdate:modelValue":r=>l.row.quantityToShip=r,min:0,max:l.row.pendingStockQuantity,size:"small",onChange:r=>D(l.row)},null,8,["modelValue","onUpdate:modelValue","max","onChange"])]),_:1})]),_:1},8,["data"]),N("div",de,[N("strong",null,"总发货商品种类: "+g(W.value),1),i[4]||(i[4]=u(" | ")),N("strong",null,"总发货数量: "+g(s.value),1)]),e(T,{"content-position":"left"},{default:t(()=>i[5]||(i[5]=[u(" 其他信息 ")])),_:1}),e(F,{gutter:20},{default:t(()=>[e($,{span:12},{default:t(()=>[e(m,{label:"送货日期",prop:"deliveryDate"},{default:t(()=>[e(I,{modelValue:b.deliveryDate,"onUpdate:modelValue":i[0]||(i[0]=l=>b.deliveryDate=l),type:"date",placeholder:"选择送货日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{label:"备注",prop:"notes"},{default:t(()=>[e(z,{type:"textarea",modelValue:b.notes,"onUpdate:modelValue":i[1]||(i[1]=l=>b.notes=l),placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["model-value"])}}},pe=G(ue,[["__scopeId","data-v-c381ffa5"]]),se={style:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},ce={__name:"ProductView",setup(E){const p=h(!1),C=h([]),w=h(!1),y=h([]),b=q([{prop:"productName",label:"商品名称",visible:!0,minWidth:150},{prop:"model",label:"型号",visible:!0,minWidth:120},{prop:"brand",label:"品牌",visible:!0,minWidth:100},{prop:"category",label:"分类",visible:!0,minWidth:100},{prop:"customer",label:"客户",visible:!0,minWidth:120},{prop:"totalQuantity",label:"总数量",visible:!0,minWidth:100},{prop:"pendingStockQuantity",label:"备货中数量",visible:!0,minWidth:120},{prop:"inTransitQuantity",label:"在途数量",visible:!0,minWidth:100},{prop:"acceptedQuantity",label:"已验收数量",visible:!0,minWidth:120},{prop:"cancelledQuantity",label:"已取消数量",visible:!0,minWidth:120},{prop:"unitPrice",label:"单价（¥）",visible:!0,minWidth:100},{prop:"totalPrice",label:"总价（¥）",visible:!0,minWidth:100},{prop:"poNo",label:"所属PO号",visible:!0,minWidth:150},{prop:"orderStatus",label:"所属订单状态",visible:!0,minWidth:120}]),f=j(()=>b.filter(n=>n.visible)),k=h([{id:"p1",productName:"高性能CPU",model:"CPU-INTEL-I9",brand:"Intel",category:"处理器",customer:"客户A",totalQuantity:50,pendingStockQuantity:10,inTransitQuantity:20,acceptedQuantity:15,cancelledQuantity:5,unitPrice:2500,totalPrice:125e3,poNo:"PO20240501001",orderStatus:"pending_shipment"},{id:"p2",productName:"大容量内存条",model:"MEM-KING-32G",brand:"Kingston",category:"内存",customer:"客户B",totalQuantity:100,pendingStockQuantity:0,inTransitQuantity:40,acceptedQuantity:20,cancelledQuantity:10,unitPrice:800,totalPrice:8e4,poNo:"PO20240428002",orderStatus:"shipped"}]),D=n=>{C.value=n,console.log("Selected products:",n)},W=n=>{console.log("View order details for:",n.poNo)},s=n=>{console.log("Shipping product:",n),n.pendingStockQuantity>0?(y.value=[n],w.value=!0):console.warn("Product has no pending stock to ship.")},V=n=>{if(n==="shipSelected"){if(C.value.length===0){console.warn("No products selected for bulk shipping.");return}const o=C.value.filter(_=>_.pendingStockQuantity>0);if(o.length===0){console.warn("None of the selected products have pending stock for shipping.");return}y.value=o,w.value=!0,console.log("Bulk shipping selected shippable products:",o)}},S=n=>{console.log("Shipment confirmed in ProductView:",n),n.items.forEach(o=>{const _=k.value.find(P=>P.id===o.productId);_&&(_.pendingStockQuantity-=o.shippedQuantity)})},d=n=>n==null?"0.00":n.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),i=n=>({pending_payment:"待付款",pending_shipment:"待发货",shipped:"已发货",completed:"已完成",cancelled:"已取消"})[n]||n,T=n=>({pending_payment:"warning",pending_shipment:"info",shipped:"",completed:"success",cancelled:"danger"})[n]||"default";return(n,o)=>{const _=a("arrow-down"),P=a("el-icon"),x=a("el-button"),I=a("el-dropdown-item"),m=a("el-dropdown-menu"),$=a("el-dropdown"),F=a("el-checkbox"),z=a("el-drawer"),Q=a("el-table-column"),M=a("el-tag"),L=a("el-table");return c(),v("div",null,[N("div",se,[e($,{onCommand:V},{dropdown:t(()=>[e(m,null,{default:t(()=>[e(I,{command:"shipSelected"},{default:t(()=>o[4]||(o[4]=[u("送货")])),_:1})]),_:1})]),default:t(()=>[e(x,{type:"primary"},{default:t(()=>[o[3]||(o[3]=u(" 批量操作")),e(P,{class:"el-icon--right"},{default:t(()=>[e(_)]),_:1})]),_:1})]),_:1}),e(x,{onClick:o[0]||(o[0]=l=>p.value=!0),type:"primary"},{default:t(()=>o[5]||(o[5]=[u(" 配置列 ")])),_:1})]),e(z,{title:"配置列",modelValue:p.value,"onUpdate:modelValue":o[1]||(o[1]=l=>p.value=l),direction:"rtl",size:"300px"},{default:t(()=>[(c(!0),v(O,null,B(b,l=>(c(),v("div",{key:l.prop,style:{padding:"0 20px"}},[e(F,{modelValue:l.visible,"onUpdate:modelValue":r=>l.visible=r},{default:t(()=>[u(g(l.label),1)]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),128))]),_:1},8,["modelValue"]),e(L,{data:k.value,style:{width:"100%"},border:"",onSelectionChange:D},{default:t(()=>[e(Q,{type:"selection",width:"55"}),(c(!0),v(O,null,B(f.value,l=>(c(),v(O,{key:l.prop},[l.visible?(c(),U(Q,{key:0,prop:l.prop,label:l.label,"min-width":l.minWidth},Y({_:2},[l.prop==="unitPrice"||l.prop==="totalPrice"?{name:"default",fn:t(r=>[u(" ¥"+g(d(r.row[l.prop])),1)]),key:"0"}:l.prop==="orderStatus"?{name:"default",fn:t(r=>[e(M,{type:T(r.row.orderStatus)},{default:t(()=>[u(g(i(r.row.orderStatus)),1)]),_:2},1032,["type"])]),key:"1"}:void 0]),1032,["prop","label","min-width"])):A("",!0)],64))),128)),e(Q,{label:"操作","min-width":"180",fixed:"right"},{default:t(l=>[e(x,{size:"small",type:"primary",link:"",onClick:r=>W(l.row)},{default:t(()=>o[6]||(o[6]=[u("查看订单")])),_:2},1032,["onClick"]),l.row.pendingStockQuantity>0?(c(),U(x,{key:0,size:"small",type:"primary",link:"",onClick:r=>s(l.row),style:{"margin-left":"5px"}},{default:t(()=>o[7]||(o[7]=[u(" 送货 ")])),_:2},1032,["onClick"])):A("",!0)]),_:1})]),_:1},8,["data"]),e(pe,{visible:w.value,"onUpdate:visible":o[2]||(o[2]=l=>w.value=l),"products-to-ship":y.value,onConfirmShipment:S},null,8,["visible","products-to-ship"])])}}},me=G(ce,[["__scopeId","data-v-fd706018"]]),_e={class:"filter-container"},be={style:{"margin-bottom":"20px",display:"flex","justify-content":"flex-end"}},fe={class:"view-content-container"},ye={__name:"index",setup(E){const p=q({poNo:"",status:"",productName:"",model:"",category:"",brand:"",pageNum:1,pageSize:10}),C=h([]),w=h(!1);h(0),h([]);const y=h("order"),b=()=>{w.value=!0,console.log("Fetching list with params:",JSON.parse(JSON.stringify(p)),"Current view:",y.value),setTimeout(()=>{console.log(`Data fetching for ${y.value} view would happen here.`),w.value=!1},300)},f=()=>{p.pageNum=1,b()},k=()=>{p.poNo="",p.status="",p.productName="",p.model="",p.category="",p.brand="",C.value=[],f()},D=W=>{console.log("Switched to view:",W),f()};return H(()=>{b()}),(W,s)=>{const V=a("el-input"),S=a("el-form-item"),d=a("el-option"),i=a("el-select"),T=a("el-date-picker"),n=a("el-button"),o=a("el-form"),_=a("el-radio-button"),P=a("el-radio-group"),x=a("el-card"),I=te("loading");return c(),U(x,{class:"order-list-container"},{default:t(()=>[N("div",_e,[e(o,{inline:!0,model:p,class:"demo-form-inline"},{default:t(()=>[e(S,{label:"PO编号"},{default:t(()=>[e(V,{modelValue:p.poNo,"onUpdate:modelValue":s[0]||(s[0]=m=>p.poNo=m),placeholder:"请输入PO编号",clearable:""},null,8,["modelValue"])]),_:1}),e(S,{label:"物料名称"},{default:t(()=>[e(V,{modelValue:p.productName,"onUpdate:modelValue":s[1]||(s[1]=m=>p.productName=m),placeholder:"请输入物料名称",clearable:""},null,8,["modelValue"])]),_:1}),e(S,{label:"物料型号"},{default:t(()=>[e(V,{modelValue:p.model,"onUpdate:modelValue":s[2]||(s[2]=m=>p.model=m),placeholder:"请输入物料型号",clearable:""},null,8,["modelValue"])]),_:1}),e(S,{label:"物料分类"},{default:t(()=>[e(V,{modelValue:p.category,"onUpdate:modelValue":s[3]||(s[3]=m=>p.category=m),placeholder:"请输入物料分类",clearable:""},null,8,["modelValue"])]),_:1}),e(S,{label:"品牌"},{default:t(()=>[e(V,{modelValue:p.brand,"onUpdate:modelValue":s[4]||(s[4]=m=>p.brand=m),placeholder:"请输入品牌",clearable:""},null,8,["modelValue"])]),_:1}),e(S,{label:"订单状态"},{default:t(()=>[e(i,{modelValue:p.status,"onUpdate:modelValue":s[5]||(s[5]=m=>p.status=m),placeholder:"请选择状态",clearable:""},{default:t(()=>[e(d,{label:"待付款",value:"pending_payment"}),e(d,{label:"待发货",value:"pending_shipment"}),e(d,{label:"已发货",value:"shipped"}),e(d,{label:"已完成",value:"completed"}),e(d,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),e(S,{label:"下单时间"},{default:t(()=>[e(T,{modelValue:C.value,"onUpdate:modelValue":s[6]||(s[6]=m=>C.value=m),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(S,null,{default:t(()=>[e(n,{type:"primary",onClick:f},{default:t(()=>s[8]||(s[8]=[u("查询")])),_:1}),e(n,{onClick:k},{default:t(()=>s[9]||(s[9]=[u("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),N("div",be,[e(P,{modelValue:y.value,"onUpdate:modelValue":s[7]||(s[7]=m=>y.value=m),onChange:D},{default:t(()=>[e(_,{label:"order"},{default:t(()=>s[10]||(s[10]=[u("订单视图")])),_:1}),e(_,{label:"product"},{default:t(()=>s[11]||(s[11]=[u("物料视图")])),_:1})]),_:1},8,["modelValue"])]),ee((c(),v("div",fe,[y.value==="order"?(c(),U(ie,{key:0,filters:p},null,8,["filters"])):A("",!0),y.value==="product"?(c(),U(me,{key:1,filters:p},null,8,["filters"])):A("",!0)])),[[I,w.value]])]),_:1})}}},ge=G(ye,[["__scopeId","data-v-2290ec75"]]);export{ge as default};
