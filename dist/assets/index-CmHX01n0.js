import{_ as L,d as b,l as Q,e as k,f as W,m as M,o as y,w as l,a as X,g as Z,b as o,r as u,i as d,h as ee,c as C,t as c,y as te,k as ae,E as S}from"./index-Di-G_xQb.js";const ne={class:"filter-container"},oe={class:"table-content-container"},le={key:1},ie={__name:"index",setup(re){const T=te(),D=b(!1),f=b(1),g=b(10),t=Q({paymentId:"",customerName:"",status:"",invoiceId:"",creationDate:[],dueDate:[]}),A=[{id:1,paymentId:"PAY20240501001",customerName:"北京科技有限公司",status:"pending",totalAmount:58600,paidAmount:0,remainingAmount:58600,invoiceId:"INV20240501001",creationTime:"2024-05-01 10:00:00",completionTime:null,dueDate:"2024-05-31 23:59:59",paymentMethod:"cash_wire",paymentTerms:"月结30天"},{id:2,paymentId:"PAY20240501002",customerName:"上海工贸公司",status:"completed",totalAmount:23250,paidAmount:23250,remainingAmount:0,invoiceId:"INV20240501002",creationTime:"2024-05-01 11:30:00",completionTime:"2024-05-15 14:20:00",dueDate:"2024-05-31 23:59:59",paymentMethod:"bank_acceptance",paymentTerms:"预付款"},{id:3,paymentId:"PAY20240501003",customerName:"深圳制造集团",status:"completed",totalAmount:42100,paidAmount:42100,remainingAmount:0,invoiceId:"INV20240501003",creationTime:"2024-05-01 14:15:00",completionTime:"2024-05-15 16:30:00",dueDate:"2024-05-31 23:59:59",paymentMethod:"cash_wire",paymentTerms:"月结"},{id:4,paymentId:"PAY20240401001",customerName:"广州贸易公司",status:"overdue",totalAmount:16500,paidAmount:0,remainingAmount:16500,invoiceId:"INV20240401001",creationTime:"2024-04-01 16:45:00",completionTime:null,dueDate:"2024-04-30 23:59:59",paymentMethod:"bank_acceptance",paymentTerms:"月结15天"},{id:5,paymentId:"PAY20240502001",customerName:"天津实业有限公司",status:"pending",totalAmount:35800,paidAmount:0,remainingAmount:35800,invoiceId:"INV20240502001",creationTime:"2024-05-02 09:00:00",completionTime:null,dueDate:"2024-06-01 23:59:59",paymentMethod:"cash_wire",paymentTerms:"预付款"},{id:6,paymentId:"PAY20240502002",customerName:"杭州科技发展公司",status:"completed",totalAmount:28900,paidAmount:28900,remainingAmount:0,invoiceId:"INV20240502002",creationTime:"2024-05-02 11:20:00",completionTime:"2024-05-10 14:45:00",dueDate:"2024-06-01 23:59:59",paymentMethod:"bank_acceptance",paymentTerms:"月结60天"}],P=k(()=>{let e=[...A];if(t.paymentId&&(e=e.filter(i=>i.paymentId.includes(t.paymentId))),t.customerName&&(e=e.filter(i=>i.customerName.includes(t.customerName))),t.status&&(e=e.filter(i=>i.status===t.status)),t.invoiceId&&(e=e.filter(i=>i.invoiceId.includes(t.invoiceId))),t.creationDate&&t.creationDate.length===2){const i=new Date(t.creationDate[0]),r=new Date(t.creationDate[1]);r.setHours(23,59,59),e=e.filter(_=>{const p=new Date(_.creationTime);return p>=i&&p<=r})}if(t.dueDate&&t.dueDate.length===2){const i=new Date(t.dueDate[0]),r=new Date(t.dueDate[1]);r.setHours(23,59,59),e=e.filter(_=>{const p=new Date(_.dueDate);return p>=i&&p<=r})}const a=(f.value-1)*g.value,s=a+g.value;return e.slice(a,s)}),x=k(()=>{let e=[...A];if(t.paymentId&&(e=e.filter(a=>a.paymentId.includes(t.paymentId))),t.customerName&&(e=e.filter(a=>a.customerName.includes(t.customerName))),t.status&&(e=e.filter(a=>a.status===t.status)),t.invoiceId&&(e=e.filter(a=>a.invoiceId.includes(t.invoiceId))),t.creationDate&&t.creationDate.length===2){const a=new Date(t.creationDate[0]),s=new Date(t.creationDate[1]);s.setHours(23,59,59),e=e.filter(i=>{const r=new Date(i.creationTime);return r>=a&&r<=s})}if(t.dueDate&&t.dueDate.length===2){const a=new Date(t.dueDate[0]),s=new Date(t.dueDate[1]);s.setHours(23,59,59),e=e.filter(i=>{const r=new Date(i.dueDate);return r>=a&&r<=s})}return e.length}),v=()=>{f.value=1,D.value=!0,setTimeout(()=>{D.value=!1},500)},$=()=>{t.paymentId="",t.customerName="",t.status="",t.invoiceId="",t.creationDate=[],t.dueDate=[],f.value=1,v()},z=e=>{g.value=e,f.value=1},B=e=>{f.value=e},h=e=>{if(!e)return"";const a=new Date(e),s=a.getFullYear(),i=String(a.getMonth()+1).padStart(2,"0"),r=String(a.getDate()).padStart(2,"0"),_=String(a.getHours()).padStart(2,"0"),p=String(a.getMinutes()).padStart(2,"0");return`${s}-${i}-${r} ${_}:${p}`},w=e=>`¥ ${e.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,",")}`,Y=e=>({pending:"待收款",completed:"已收款",overdue:"已逾期"})[e]||e,U=e=>({pending:"warning",completed:"success",overdue:"danger"})[e]||"",H=e=>({cash_wire:"现金/电汇",bank_acceptance:"银行承兑汇票"})[e]||e,E=e=>{T.push(`/trade/sale/pay/detail/${e.id}`)},F=e=>{T.push(`/trade/so/inv-detail/${e.invoiceId}`)},R=e=>{ae.confirm(`确认收款单号为 ${e.paymentId} 的付款吗？`,"确认收款",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{S.success(`已确认收款：${e.paymentId}`),e.status="completed",e.completionTime=new Date().toISOString().slice(0,19).replace("T"," "),e.paidAmount=e.totalAmount,e.remainingAmount=0}).catch(()=>{S.info("已取消操作")})};return W(()=>{v()}),(e,a)=>{const s=u("el-input"),i=u("el-form-item"),r=u("el-option"),_=u("el-select"),p=u("el-date-picker"),I=u("el-button"),j=u("el-form"),V=u("el-link"),m=u("el-table-column"),O=u("el-tag"),q=u("el-table"),G=u("el-pagination"),J=u("el-card"),K=ee("loading");return y(),M(J,{class:"payment-order-container"},{default:l(()=>[X("div",ne,[o(j,{inline:!0,model:t,class:"search-form"},{default:l(()=>[o(i,{label:"付款单号"},{default:l(()=>[o(s,{modelValue:t.paymentId,"onUpdate:modelValue":a[0]||(a[0]=n=>t.paymentId=n),placeholder:"请输入付款单号",clearable:""},null,8,["modelValue"])]),_:1}),o(i,{label:"客户名称"},{default:l(()=>[o(s,{modelValue:t.customerName,"onUpdate:modelValue":a[1]||(a[1]=n=>t.customerName=n),placeholder:"请输入客户名称",clearable:""},null,8,["modelValue"])]),_:1}),o(i,{label:"状态"},{default:l(()=>[o(_,{modelValue:t.status,"onUpdate:modelValue":a[2]||(a[2]=n=>t.status=n),placeholder:"请选择状态",clearable:""},{default:l(()=>[o(r,{label:"全部",value:""}),o(r,{label:"待收款",value:"pending"}),o(r,{label:"已收款",value:"completed"}),o(r,{label:"已逾期",value:"overdue"})]),_:1},8,["modelValue"])]),_:1}),o(i,{label:"所属对账单"},{default:l(()=>[o(s,{modelValue:t.invoiceId,"onUpdate:modelValue":a[3]||(a[3]=n=>t.invoiceId=n),placeholder:"请输入对账单号",clearable:""},null,8,["modelValue"])]),_:1}),o(i,{label:"创建时间"},{default:l(()=>[o(p,{modelValue:t.creationDate,"onUpdate:modelValue":a[4]||(a[4]=n=>t.creationDate=n),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:""},null,8,["modelValue"])]),_:1}),o(i,{label:"付款截止时间"},{default:l(()=>[o(p,{modelValue:t.dueDate,"onUpdate:modelValue":a[5]||(a[5]=n=>t.dueDate=n),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:""},null,8,["modelValue"])]),_:1}),o(i,null,{default:l(()=>[o(I,{type:"primary",onClick:v},{default:l(()=>a[6]||(a[6]=[d("搜索")])),_:1}),o(I,{onClick:$},{default:l(()=>a[7]||(a[7]=[d("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),Z((y(),C("div",oe,[o(q,{data:P.value,style:{width:"100%"},border:""},{default:l(()=>[o(m,{prop:"paymentId",label:"付款单号","min-width":"180"},{default:l(n=>[o(V,{type:"primary",onClick:N=>E(n.row)},{default:l(()=>[d(c(n.row.paymentId),1)]),_:2},1032,["onClick"])]),_:1}),o(m,{prop:"customerName",label:"客户名称","min-width":"150"}),o(m,{prop:"status",label:"状态","min-width":"120",align:"center"},{default:l(n=>[o(O,{type:U(n.row.status)},{default:l(()=>[d(c(Y(n.row.status)),1)]),_:2},1032,["type"])]),_:1}),o(m,{prop:"totalAmount",label:"应付总额","min-width":"150",align:"right"},{default:l(n=>[d(c(w(n.row.totalAmount)),1)]),_:1}),o(m,{prop:"paidAmount",label:"实付总额","min-width":"150",align:"right"},{default:l(n=>[d(c(w(n.row.paidAmount)),1)]),_:1}),o(m,{prop:"remainingAmount",label:"待付总额","min-width":"150",align:"right"},{default:l(n=>[d(c(w(n.row.remainingAmount)),1)]),_:1}),o(m,{prop:"invoiceId",label:"所属对账单","min-width":"180"},{default:l(n=>[o(V,{type:"primary",onClick:N=>F(n.row)},{default:l(()=>[d(c(n.row.invoiceId),1)]),_:2},1032,["onClick"])]),_:1}),o(m,{prop:"creationTime",label:"创建时间","min-width":"160"},{default:l(n=>[d(c(h(n.row.creationTime)),1)]),_:1}),o(m,{prop:"completionTime",label:"付款完成时间","min-width":"160"},{default:l(n=>[d(c(n.row.completionTime?h(n.row.completionTime):"-"),1)]),_:1}),o(m,{prop:"dueDate",label:"付款截止时间","min-width":"160"},{default:l(n=>[d(c(h(n.row.dueDate)),1)]),_:1}),o(m,{prop:"paymentMethod",label:"付款方式","min-width":"150",align:"center"},{default:l(n=>[d(c(H(n.row.paymentMethod)),1)]),_:1}),o(m,{prop:"paymentTerms",label:"付款条件","min-width":"150"},{default:l(n=>[d(c(n.row.paymentTerms),1)]),_:1}),o(m,{label:"操作","min-width":"120",align:"center",fixed:"right"},{default:l(n=>[n.row.status==="pending"?(y(),M(I,{key:0,type:"primary",link:"",size:"small",onClick:N=>R(n.row)},{default:l(()=>a[8]||(a[8]=[d("确认收款")])),_:2},1032,["onClick"])):(y(),C("span",le,"-"))]),_:1})]),_:1},8,["data"]),o(G,{class:"pagination-container","current-page":f.value,"page-sizes":[10,20,50,100],"page-size":g.value,layout:"total, sizes, prev, pager, next, jumper",total:x.value,onSizeChange:z,onCurrentChange:B},null,8,["current-page","page-size","total"])])),[[K,D.value]])]),_:1})}}},se=L(ie,[["__scopeId","data-v-e2108608"]]);export{se as default};
