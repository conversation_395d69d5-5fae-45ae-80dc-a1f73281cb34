import{_ as ae,d as p,e as $,f as te,c as g,o as h,b as e,w as t,a as c,r as u,g as A,h as le,i as d,t as v,u as se,s as ne,F as oe,j as ie,E as _,k as re,n as ue}from"./index-Di-G_xQb.js";const de={class:"agent-manage"},ce={class:"tabs-container"},me={class:"tab-content"},pe={class:"table-content-container"},ve={class:"tab-content"},_e={class:"xiaocai-layout"},fe={class:"left-panel"},be={class:"avatar-search"},ge={class:"avatar-list"},he=["onClick"],we={class:"avatar-name"},ye={class:"right-panel"},xe={class:"filter-container"},Ve={class:"table-content-container"},Te={class:"tab-content"},ke={class:"filter-container"},Ne={class:"table-content-container"},Ce={__name:"AgentManage",setup(Se){const D=p("xiaoyan"),o=p(!1),F=p([{id:1,name:"小妍",avatar:"https://via.placeholder.com/40x40?text=小妍",brand:"官方",status:"启用"},{id:2,name:"小智",avatar:"https://via.placeholder.com/40x40?text=小智",brand:"官方",status:"启用"},{id:3,name:"小助手",avatar:"https://via.placeholder.com/40x40?text=助手",brand:"官方",status:"禁用"}]),E=p([{id:1,name:"小彩0001",image:"https://via.placeholder.com/60x60?text=0001"},{id:2,name:"小彩0002",image:"https://via.placeholder.com/60x60?text=0002"},{id:3,name:"小彩0003",image:"https://via.placeholder.com/60x60?text=0003"},{id:4,name:"小彩0004",image:"https://via.placeholder.com/60x60?text=0004"}]),z=p(1),T=p(""),N=p([{id:1,name:"阿里巴巴集团",type:"设备商",groupName:"阿里技术交流群",status:"启用",bindTime:"2024-01-15 10:30:00"},{id:2,name:"中国石油",type:"供应商",groupName:"石油采购群",status:"启用",bindTime:"2024-01-20 14:20:00"},{id:3,name:"微软中国",type:"设备商",groupName:"微软供应链群",status:"禁用",bindTime:"2024-02-01 09:15:00"}]),i=p({enterpriseName:"",enterpriseType:"",groupName:""}),I=p([{id:1,name:"大麦助手",avatar:"https://via.placeholder.com/40x40?text=大麦",brand:"自定义",status:"启用",createTime:"2024-01-10 08:00:00"},{id:2,name:"采购专家",avatar:"https://via.placeholder.com/40x40?text=采购",brand:"自定义",status:"启用",createTime:"2024-01-12 10:30:00"},{id:3,name:"销售顾问",avatar:"https://via.placeholder.com/40x40?text=销售",brand:"自定义",status:"禁用",createTime:"2024-01-15 16:45:00"}]),r=p({name:"",brand:"",status:""}),L=$(()=>T.value?E.value.filter(s=>s.name.toLowerCase().includes(T.value.toLowerCase())):E.value),X=$(()=>N.value.filter(s=>(!i.value.enterpriseName||s.name.includes(i.value.enterpriseName))&&(!i.value.enterpriseType||s.type===i.value.enterpriseType)&&(!i.value.groupName||s.groupName.includes(i.value.groupName)))),j=$(()=>I.value.filter(s=>(!r.value.name||s.name.includes(r.value.name))&&(!r.value.brand||s.brand.includes(r.value.brand))&&(!r.value.status||s.status===r.value.status))),U=s=>{if(!s)return"";const l=new Date(s),n=l.getFullYear(),w=String(l.getMonth()+1).padStart(2,"0"),y=String(l.getDate()).padStart(2,"0"),m=String(l.getHours()).padStart(2,"0"),x=String(l.getMinutes()).padStart(2,"0");return`${n}-${w}-${y} ${m}:${x}`},H=()=>{},Y=s=>{z.value=s},q=s=>{o.value=!0,setTimeout(()=>{s.status=s.status==="启用"?"禁用":"启用",o.value=!1,_.success(`${s.name} 已${s.status}`)},500)},G=s=>{o.value=!0,setTimeout(()=>{s.status=s.status==="启用"?"禁用":"启用",o.value=!1,_.success(`${s.name} 已${s.status}`)},500)},J=s=>{re.confirm(`确定要解绑企业 "${s.name}" 吗？`,"确认解绑",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{o.value=!0,setTimeout(()=>{const l=N.value.findIndex(n=>n.id===s.id);l>-1&&N.value.splice(l,1),o.value=!1,_.success("解绑成功")},500)}).catch(()=>{_.info("已取消解绑")})},K=s=>{o.value=!0,setTimeout(()=>{s.status=s.status==="启用"?"禁用":"启用",o.value=!1,_.success(`${s.name} 已${s.status}`)},500)},O=()=>{o.value=!0,setTimeout(()=>{o.value=!1,_.success("搜索完成")},500)},P=()=>{i.value={enterpriseName:"",enterpriseType:"",groupName:""},_.info("搜索条件已重置")},Q=()=>{o.value=!0,setTimeout(()=>{o.value=!1,_.success("搜索完成")},500)},R=()=>{r.value={name:"",brand:"",status:""},_.info("搜索条件已重置")};return te(()=>{console.log("智能体管理页面已加载")}),(s,l)=>{const n=u("el-table-column"),w=u("el-avatar"),y=u("el-tag"),m=u("el-button"),x=u("el-table"),C=u("el-tab-pane"),W=u("el-icon"),V=u("el-input"),f=u("el-form-item"),b=u("el-option"),M=u("el-select"),B=u("el-form"),Z=u("el-tabs"),ee=u("el-card"),S=le("loading");return h(),g("div",de,[e(ee,{class:"agent-manage-container"},{default:t(()=>[c("div",ce,[e(Z,{modelValue:D.value,"onUpdate:modelValue":l[7]||(l[7]=a=>D.value=a),class:"agent-tabs"},{default:t(()=>[e(C,{label:"小妍",name:"xiaoyan"},{default:t(()=>[c("div",me,[A((h(),g("div",pe,[e(x,{data:F.value,style:{width:"100%"},border:""},{default:t(()=>[e(n,{prop:"name",label:"名称","min-width":"120"}),e(n,{prop:"avatar",label:"头像","min-width":"80",align:"center"},{default:t(a=>[e(w,{src:a.row.avatar,alt:a.row.name,size:"small"},null,8,["src","alt"])]),_:1}),e(n,{prop:"brand",label:"品牌","min-width":"100"}),e(n,{prop:"status",label:"状态","min-width":"100",align:"center"},{default:t(a=>[e(y,{type:a.row.status==="启用"?"success":"warning"},{default:t(()=>[d(v(a.row.status),1)]),_:2},1032,["type"])]),_:1}),e(n,{label:"操作","min-width":"120",align:"center"},{default:t(a=>[e(m,{type:a.row.status==="启用"?"danger":"success",link:"",size:"small",onClick:k=>q(a.row)},{default:t(()=>[d(v(a.row.status==="启用"?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"])])),[[S,o.value]])])]),_:1}),e(C,{label:"小彩",name:"xiaocai"},{default:t(()=>[c("div",ve,[c("div",_e,[c("div",fe,[c("div",be,[e(V,{modelValue:T.value,"onUpdate:modelValue":l[0]||(l[0]=a=>T.value=a),placeholder:"搜索分身名称",clearable:"",onInput:H},{prefix:t(()=>[e(W,null,{default:t(()=>[e(se(ne))]),_:1})]),_:1},8,["modelValue"])]),c("div",ge,[(h(!0),g(oe,null,ie(L.value,a=>(h(),g("div",{key:a.id,class:ue(["avatar-item",{active:z.value===a.id}]),onClick:k=>Y(a.id)},[e(w,{src:a.image,alt:a.name,size:"small"},null,8,["src","alt"]),c("span",we,v(a.name),1)],10,he))),128))])]),c("div",ye,[c("div",xe,[e(B,{inline:!0,model:i.value,class:"search-form"},{default:t(()=>[e(f,{label:"企业名称"},{default:t(()=>[e(V,{modelValue:i.value.enterpriseName,"onUpdate:modelValue":l[1]||(l[1]=a=>i.value.enterpriseName=a),placeholder:"请输入企业名称",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"企业性质"},{default:t(()=>[e(M,{modelValue:i.value.enterpriseType,"onUpdate:modelValue":l[2]||(l[2]=a=>i.value.enterpriseType=a),placeholder:"请选择企业性质",clearable:""},{default:t(()=>[e(b,{label:"全部",value:""}),e(b,{label:"设备商",value:"设备商"}),e(b,{label:"供应商",value:"供应商"})]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"企业微信群名称"},{default:t(()=>[e(V,{modelValue:i.value.groupName,"onUpdate:modelValue":l[3]||(l[3]=a=>i.value.groupName=a),placeholder:"请输入群名称",clearable:""},null,8,["modelValue"])]),_:1}),e(f,null,{default:t(()=>[e(m,{type:"primary",onClick:O},{default:t(()=>l[8]||(l[8]=[d("搜索")])),_:1}),e(m,{onClick:P},{default:t(()=>l[9]||(l[9]=[d("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),A((h(),g("div",Ve,[e(x,{data:X.value,style:{width:"100%"},border:""},{default:t(()=>[e(n,{prop:"name",label:"企业名称","min-width":"150"}),e(n,{prop:"type",label:"企业性质","min-width":"100"}),e(n,{prop:"groupName",label:"企业微信群名称","min-width":"150"}),e(n,{prop:"status",label:"状态","min-width":"100",align:"center"},{default:t(a=>[e(y,{type:a.row.status==="启用"?"success":"warning"},{default:t(()=>[d(v(a.row.status),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"bindTime",label:"绑定时间","min-width":"160"},{default:t(a=>[d(v(U(a.row.bindTime)),1)]),_:1}),e(n,{label:"操作","min-width":"160",align:"center"},{default:t(a=>[e(m,{type:a.row.status==="启用"?"danger":"success",link:"",size:"small",onClick:k=>G(a.row)},{default:t(()=>[d(v(a.row.status==="启用"?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),e(m,{type:"warning",link:"",size:"small",onClick:k=>J(a.row)},{default:t(()=>l[10]||(l[10]=[d(" 解绑 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])),[[S,o.value]])])])])]),_:1}),e(C,{label:"大麦",name:"damai"},{default:t(()=>[c("div",Te,[c("div",ke,[e(B,{inline:!0,model:r.value,class:"search-form"},{default:t(()=>[e(f,{label:"名称"},{default:t(()=>[e(V,{modelValue:r.value.name,"onUpdate:modelValue":l[4]||(l[4]=a=>r.value.name=a),placeholder:"请输入名称",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"品牌"},{default:t(()=>[e(V,{modelValue:r.value.brand,"onUpdate:modelValue":l[5]||(l[5]=a=>r.value.brand=a),placeholder:"请输入品牌",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"状态"},{default:t(()=>[e(M,{modelValue:r.value.status,"onUpdate:modelValue":l[6]||(l[6]=a=>r.value.status=a),placeholder:"请选择状态",clearable:""},{default:t(()=>[e(b,{label:"全部",value:""}),e(b,{label:"启用",value:"启用"}),e(b,{label:"禁用",value:"禁用"})]),_:1},8,["modelValue"])]),_:1}),e(f,null,{default:t(()=>[e(m,{type:"primary",onClick:Q},{default:t(()=>l[11]||(l[11]=[d("搜索")])),_:1}),e(m,{onClick:R},{default:t(()=>l[12]||(l[12]=[d("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),A((h(),g("div",Ne,[e(x,{data:j.value,style:{width:"100%"},border:""},{default:t(()=>[e(n,{prop:"name",label:"名称","min-width":"120"}),e(n,{prop:"avatar",label:"头像","min-width":"80",align:"center"},{default:t(a=>[e(w,{src:a.row.avatar,alt:a.row.name,size:"small"},null,8,["src","alt"])]),_:1}),e(n,{prop:"brand",label:"品牌","min-width":"100"}),e(n,{prop:"status",label:"状态","min-width":"100",align:"center"},{default:t(a=>[e(y,{type:a.row.status==="启用"?"success":"warning"},{default:t(()=>[d(v(a.row.status),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"createTime",label:"创建时间","min-width":"160"},{default:t(a=>[d(v(U(a.row.createTime)),1)]),_:1}),e(n,{label:"操作","min-width":"120",align:"center"},{default:t(a=>[e(m,{type:a.row.status==="启用"?"danger":"success",link:"",size:"small",onClick:k=>K(a.row)},{default:t(()=>[d(v(a.row.status==="启用"?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"])])),[[S,o.value]])])]),_:1})]),_:1},8,["modelValue"])])]),_:1})])}}},Ae=ae(Ce,[["__scopeId","data-v-d8f9a81d"]]);export{Ae as default};
