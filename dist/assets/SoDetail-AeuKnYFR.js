import{_ as Qe,e as N,l as $,d as R,c as g,o as c,a as t,b as s,w as l,i as f,r as u,u as Q,C as Ae,t as i,m as V,v as q,D as Ee,p as Oe,G as $e,H as je,F as ie,j as ze,n as j,x as He,E as k,y as Je,k as ne}from"./index-Di-G_xQb.js";const Ge={class:"so-detail-container"},Ke={class:"page-header"},Xe={class:"header-content"},We={class:"page-title"},Ye={class:"header-actions"},Ze={class:"content-section"},et={class:"process-container"},tt={class:"content-section"},st={class:"basic-info"},lt={class:"info-item"},at={class:"value"},it={class:"info-item"},nt={class:"info-item"},ot={class:"value"},rt={class:"info-item"},dt={class:"value"},ct={class:"info-item"},ut={class:"value"},pt={class:"info-item"},mt={class:"value"},_t={class:"info-item"},ft={class:"value"},vt={class:"info-item"},yt={class:"value"},gt={class:"info-item"},ht={class:"value amount"},bt={class:"content-section"},wt={class:"material-detail"},Pt={class:"material-search"},xt={class:"material-actions"},St={key:0,class:"batch-actions"},kt={key:1,class:"statistics-actions"},Tt={class:"selected-statistics"},Ct={key:1},Vt={key:1},Mt={key:0},Nt={key:1,class:"text-muted"},Rt={key:1},Dt={key:0},Bt={key:1},qt={key:1},It={key:0},Ut={key:1,class:"text-muted"},Ft={class:"material-summary"},Lt={class:"summary-content"},Qt={class:"summary-columns"},At={class:"summary-column"},Et={class:"summary-item"},Ot={class:"summary-value"},$t={class:"summary-item"},jt={class:"summary-value"},zt={class:"summary-item"},Ht={class:"summary-value"},Jt={class:"summary-column"},Gt={class:"summary-item"},Kt={class:"summary-value"},Xt={class:"summary-item"},Wt={class:"summary-value"},Yt={class:"summary-item"},Zt={class:"summary-value"},es={class:"summary-columns"},ts={class:"summary-column"},ss={class:"summary-item total"},ls={class:"content-section"},as={class:"delivery-invoice-info"},is={class:"delivery-info"},ns={class:"invoice-info"},os={class:"price-comparison-container"},rs={style:{"margin-bottom":"16px",color:"#333","text-align":"center"}},ds={class:"current-price-section"},cs={class:"price-item"},us={class:"price-value"},ps={class:"price-item"},ms={class:"price-value"},_s={class:"price-item"},fs={class:"price-item"},vs={class:"price-value"},ys={class:"price-item"},gs={class:"price-value"},hs={class:"price-item"},bs={class:"price-value preview-value"},ws={class:"price-item"},Ps={class:"price-item"},xs={key:1,class:"no-preview"},Ss={style:{"margin-left":"10px",color:"#666"}},ks={key:0,style:{color:"#f56c6c","margin-left":"50px","margin-top":"10px",width:"400px"}},Ts={class:"dialog-footer"},Cs={__name:"SoDetail",setup(Vs){const oe=Je(),re=N(()=>({draft:0,pending_confirmation:0,pending_supplier_confirmation:1,in_progress:2,completed:3,cancelled:0})[m.status]||0),K=R("delivery"),X=R([]),A=R(""),z=R([]),v=R([]),I=R(!1),y=$({type:"salePrice",value:0}),W=[{id:"supplier_001",name:"SelecTech科技有限公司"},{id:"supplier_002",name:"传感技术(深圳)有限公司"},{id:"supplier_003",name:"显示科技股份有限公司"},{id:"supplier_004",name:"智能设备制造有限公司"},{id:"supplier_005",name:"精密仪器(上海)有限公司"}],Y=N(()=>S.value.every(a=>a.supplierId&&a.supplierPrice>=0)),de=N(()=>{if(!A.value.trim())return S.value;const a=A.value.toLowerCase().trim();return S.value.filter(e=>e.supplierName.toLowerCase().includes(a))}),U=$({draft:{time:"2024-05-01 09:30:00"},submitted:{time:"2024-05-01 10:30:00"},confirmed:{time:"2024-05-01 14:20:00"},completed:{time:""}}),m=$({orderNo:"SO20240501001",status:"pending_confirmation",createTime:"2024-05-01 10:30:00",buyer:"张三",contactPhone:"13800138000",paymentMethod:"银行转账",paymentTerms:"款到发货",customerFreight:500,totalAmount:187500}),S=R([{id:1,materialName:"智能控制器",model:"CT-X100",brand:"SelecTech",category:"控制设备",supplierId:"supplier_001",supplierName:"SelecTech科技有限公司",quantity:50,shippedQuantity:30,receivedQuantity:25,cancelledQuantity:0,logisticsStatus:"部分发货",financialStatus:"已付款",supplierPrice:2e3,freight:0,unitPrice:2500,totalPrice:125e3,expectedDeliveryDate:"2024-05-15"},{id:2,materialName:"传感器模块",model:"SM-200",brand:"TechSense",category:"传感设备",supplierId:"supplier_002",supplierName:"传感技术(深圳)有限公司",quantity:100,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,logisticsStatus:"待发货",financialStatus:"待付款",supplierPrice:400,freight:0,unitPrice:500,totalPrice:5e4,expectedDeliveryDate:"2024-05-20"},{id:3,materialName:"显示屏",model:"DS-15",brand:"DisplayTech",category:"显示设备",supplierId:"supplier_003",supplierName:"显示科技股份有限公司",quantity:25,shippedQuantity:25,receivedQuantity:25,cancelledQuantity:0,logisticsStatus:"已签收",financialStatus:"已付款",supplierPrice:400,freight:0,unitPrice:500,totalPrice:12500,expectedDeliveryDate:"2024-05-10"}]),F=$({contactName:"李经理",phone:"13900139000",region:"广东省 广州市 天河区",address:"天河路123号科技大厦15楼",remark:"请在工作日送货，联系保安室"}),C=N(()=>{const a=S.value.reduce((b,w)=>b+w.quantity,0),e=S.value.reduce((b,w)=>b+w.quantity*(w.unitPrice||0),0),o=500,r=S.value.reduce((b,w)=>b+w.quantity*(w.supplierPrice||0),0),d=S.value.reduce((b,w)=>b+(w.freight||0),0),x=e+o,p=r+d,T=p>0?((x-p)/p*100).toFixed(1):0;return{totalQuantity:a,salesTotal:e,customerFreight:o,supplierTotalPrice:r,supplierFreightTotal:d,totalRevenue:x,totalCost:p,grossProfitRate:T}}),M=N(()=>v.value.reduce((a,e)=>a+e.quantity*(e.unitPrice||0),0)),D=N(()=>v.value.reduce((a,e)=>a+e.quantity*(e.supplierPrice||0),0)),E=N(()=>v.value.length===0?0:v.value.reduce((e,o)=>{const r=o.supplierPrice>0?(o.unitPrice-o.supplierPrice)/o.supplierPrice*100:0;return e+r},0)/v.value.length),_=a=>a==null?"0.00":a.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),ce=a=>({pending_confirmation:"info",pending_supplier_confirmation:"warning",in_progress:"primary",completed:"success",cancelled:"danger"})[a]||"info",ue=a=>({pending_confirmation:"待确认",pending_supplier_confirmation:"待供应商确认",in_progress:"执行中",completed:"已完成",cancelled:"已取消"})[a]||"未知",pe=a=>({待发货:"info",部分发货:"warning",已发货:"primary",运输中:"primary",已签收:"success"})[a]||"info",me=a=>({待付款:"danger",部分付款:"warning",已付款:"success",已退款:"info"})[a]||"info",H=a=>{const e=parseFloat(a);return e>=20?"profit-high":e>=10?"profit-medium":"profit-low"},_e=a=>["pending_supplier_confirmation","in_progress","completed"].includes(a.status),fe=()=>{oe.go(-1)},ve=()=>{if(!Y.value){k.warning("请为所有物料选择供应商并设置报价");return}const a=ye();a.length>0?ge(a):Z()},ye=()=>{const a=[];return S.value.forEach((e,o)=>{const r=z.value[o];r&&e.unitPrice!==r.unitPrice&&a.push({materialName:e.materialName,model:e.model,brand:e.brand,originalPrice:r.unitPrice,modifiedPrice:e.unitPrice})}),a},ge=a=>{ne({title:"销售价修改确认",dangerouslyUseHTMLString:!0,message:(o=>`
      <div style="margin-bottom: 16px;">以下物料的销售价已被修改，本订单将交由客户进行最终确认，是否继续？</div>
      <table style="width: 100%; border-collapse: collapse; border: 1px solid #dcdfe6; font-size: 14px;">
        <thead>
          <tr style="background-color: #f5f7fa;">
            <th style="padding: 12px 8px; border: 1px solid #dcdfe6; text-align: left; font-weight: 600;">物料名称</th>
            <th style="padding: 12px 8px; border: 1px solid #dcdfe6; text-align: left; font-weight: 600;">型号</th>
            <th style="padding: 12px 8px; border: 1px solid #dcdfe6; text-align: left; font-weight: 600;">品牌</th>
            <th style="padding: 12px 8px; border: 1px solid #dcdfe6; text-align: right; font-weight: 600;">原销售价</th>
            <th style="padding: 12px 8px; border: 1px solid #dcdfe6; text-align: right; font-weight: 600;">修改后销售价</th>
          </tr>
        </thead>
        <tbody>
          ${o.map(d=>`
      <tr>
        <td style="padding: 8px; border: 1px solid #dcdfe6;">${d.materialName}</td>
        <td style="padding: 8px; border: 1px solid #dcdfe6;">${d.model}</td>
        <td style="padding: 8px; border: 1px solid #dcdfe6;">${d.brand}</td>
        <td style="padding: 8px; border: 1px solid #dcdfe6; text-align: right;">¥${_(d.originalPrice)}</td>
        <td style="padding: 8px; border: 1px solid #dcdfe6; text-align: right;">¥${_(d.modifiedPrice)}</td>
      </tr>
    `).join("")}
        </tbody>
      </table>
    `)(a),showCancelButton:!0,confirmButtonText:"继续确认",cancelButtonText:"取消",type:"warning",customStyle:{width:"800px"}}).then(()=>{Z()}).catch(()=>{})},Z=()=>{k.success("确认订单成功！"),m.status="pending_supplier_confirmation",U.submitted.time=new Date().toLocaleString("zh-CN")},he=()=>{k.success("合同下载中...")},be=()=>{k.success("打印功能开发中...")},we=()=>{k.success("导出功能开发中...")},Pe=(a,e)=>{const o=W.find(r=>r.id===a);o&&(e.supplierName=o.name,k.success(`已选择供应商：${o.name}`))},J=()=>{S.value.forEach(a=>{a.totalPrice=a.quantity*(a.unitPrice||0)})},xe=()=>{},Se=()=>{ne.confirm("确定要重置所有物料的修改吗？此操作将恢复所有物料的供应商、报价、运费和销售价到初始状态。","确认重置",{confirmButtonText:"确定重置",cancelButtonText:"取消",type:"warning"}).then(()=>{S.value=JSON.parse(JSON.stringify(z.value)),k.success("已重置所有物料到初始状态")}).catch(()=>{})},ke=a=>{v.value=a},Te=()=>{if(v.value.length===0){k.warning("请先选择要调整的物料");return}y.type="salePrice",y.value=0,I.value=!0},Ce=()=>{I.value=!1,y.type="salePrice",y.value=0},ee=()=>{const{type:a,value:e}=y;return a==="salePrice"?e:a==="profitRate"?v.value.reduce((o,r)=>{const d=(r.supplierPrice||0)*(1+e/100);return o+d*r.quantity},0):M.value},te=()=>{if(v.value.length===0)return 0;const{type:a,value:e}=y;if(a==="salePrice"){const o=e,r=M.value;if(r===0)return 0;let d=0;return v.value.forEach(x=>{const T=x.unitPrice*x.quantity/r,w=o*T/x.quantity,B=x.supplierPrice>0?(w-x.supplierPrice)/x.supplierPrice*100:0;d+=B}),d/v.value.length}else if(a==="profitRate")return e;return E.value},G=()=>{const a=M.value-D.value;return ee()-D.value-a},Ve=()=>{const{type:a,value:e}=y;if(e<=0){k.warning("请输入有效的调整值");return}if(a==="salePrice"){const o=e,r=M.value;if(r===0){k.warning("当前销售总价为0，无法进行等比例调整");return}v.value.forEach(d=>{const p=d.unitPrice*d.quantity/r,T=o*p;d.unitPrice=T/d.quantity,d.totalPrice=d.unitPrice*d.quantity})}else a==="profitRate"&&v.value.forEach(o=>{o.unitPrice=(o.supplierPrice||0)*(1+e/100),o.totalPrice=o.unitPrice*o.quantity});I.value=!1,k.success(`已调整 ${v.value.length} 项物料的销售价`)};return X.value=W,z.value=JSON.parse(JSON.stringify(S.value)),(a,e)=>{const o=u("el-icon"),r=u("el-button"),d=u("el-step"),x=u("el-steps"),p=u("el-col"),T=u("el-tag"),b=u("el-row"),w=u("el-input"),B=u("el-statistic"),h=u("el-table-column"),Me=u("el-option"),Ne=u("el-select"),O=u("el-input-number"),Re=u("el-table"),L=u("el-descriptions-item"),De=u("el-descriptions"),se=u("el-tab-pane"),Be=u("el-empty"),qe=u("el-tabs"),Ie=u("el-divider"),le=u("el-radio"),Ue=u("el-radio-group"),ae=u("el-form-item"),Fe=u("el-form"),Le=u("el-dialog");return c(),g("div",Ge,[t("div",Ke,[t("div",Xe,[s(r,{onClick:fe,type:"text",class:"back-button"},{default:l(()=>[s(o,null,{default:l(()=>[s(Q(Ae))]),_:1}),e[5]||(e[5]=f(" 返回订单列表 "))]),_:1}),t("h2",We,"订单详情 - "+i(m.orderNo),1)]),t("div",Ye,[m.status==="pending_confirmation"?(c(),V(r,{key:0,type:"primary",disabled:!Y.value,onClick:ve},{default:l(()=>e[6]||(e[6]=[f(" 确认订单 ")])),_:1},8,["disabled"])):q("",!0),_e(m)?(c(),V(r,{key:1,type:"success",onClick:he},{default:l(()=>e[7]||(e[7]=[f("下载合同")])),_:1})):q("",!0),s(r,{type:"info",onClick:be},{default:l(()=>e[8]||(e[8]=[f("打印")])),_:1}),s(r,{type:"primary",onClick:we},{default:l(()=>e[9]||(e[9]=[f("导出")])),_:1})])]),t("div",Ze,[e[10]||(e[10]=t("div",{class:"section-header"},[t("h3",null,"订单流程")],-1)),t("div",et,[s(x,{active:re.value,"finish-status":"success","align-center":""},{default:l(()=>[s(d,{title:"创建草稿",description:U.draft.time},{icon:l(()=>[s(o,null,{default:l(()=>[s(Q(Ee))]),_:1})]),_:1},8,["description"]),s(d,{title:"提交订单",description:U.submitted.time},{icon:l(()=>[s(o,null,{default:l(()=>[s(Q(Oe))]),_:1})]),_:1},8,["description"]),s(d,{title:"订单确认",description:U.confirmed.time},{icon:l(()=>[s(o,null,{default:l(()=>[s(Q($e))]),_:1})]),_:1},8,["description"]),s(d,{title:"完成订单",description:U.completed.time},{icon:l(()=>[s(o,null,{default:l(()=>[s(Q(je))]),_:1})]),_:1},8,["description"])]),_:1},8,["active"])])]),t("div",tt,[e[20]||(e[20]=t("div",{class:"section-header"},[t("h3",null,"基本信息")],-1)),t("div",st,[s(b,{gutter:24},{default:l(()=>[s(p,{span:8},{default:l(()=>[t("div",lt,[e[11]||(e[11]=t("span",{class:"label"},"订单号：",-1)),t("span",at,i(m.orderNo),1)])]),_:1}),s(p,{span:8},{default:l(()=>[t("div",it,[e[12]||(e[12]=t("span",{class:"label"},"订单状态：",-1)),s(T,{type:ce(m.status),class:"value"},{default:l(()=>[f(i(ue(m.status)),1)]),_:1},8,["type"])])]),_:1}),s(p,{span:8},{default:l(()=>[t("div",nt,[e[13]||(e[13]=t("span",{class:"label"},"下单时间：",-1)),t("span",ot,i(m.createTime),1)])]),_:1})]),_:1}),s(b,{gutter:24},{default:l(()=>[s(p,{span:8},{default:l(()=>[t("div",rt,[e[14]||(e[14]=t("span",{class:"label"},"采购员：",-1)),t("span",dt,i(m.buyer),1)])]),_:1}),s(p,{span:8},{default:l(()=>[t("div",ct,[e[15]||(e[15]=t("span",{class:"label"},"联系电话：",-1)),t("span",ut,i(m.contactPhone),1)])]),_:1}),s(p,{span:8},{default:l(()=>[t("div",pt,[e[16]||(e[16]=t("span",{class:"label"},"付款方式：",-1)),t("span",mt,i(m.paymentMethod),1)])]),_:1})]),_:1}),s(b,{gutter:24},{default:l(()=>[s(p,{span:8},{default:l(()=>[t("div",_t,[e[17]||(e[17]=t("span",{class:"label"},"付款条件：",-1)),t("span",ft,i(m.paymentTerms),1)])]),_:1}),s(p,{span:8},{default:l(()=>[t("div",vt,[e[18]||(e[18]=t("span",{class:"label"},"运费：",-1)),t("span",yt,"¥"+i(_(m.customerFreight)),1)])]),_:1}),s(p,{span:8},{default:l(()=>[t("div",gt,[e[19]||(e[19]=t("span",{class:"label"},"总金额：",-1)),t("span",ht,"¥"+i(_(m.totalAmount)),1)])]),_:1})]),_:1})])]),t("div",bt,[e[30]||(e[30]=t("div",{class:"section-header"},[t("div",{class:"section-title-wrapper"},[t("h3",null,"物料明细")])],-1)),t("div",wt,[t("div",Pt,[s(w,{modelValue:A.value,"onUpdate:modelValue":e[0]||(e[0]=n=>A.value=n),placeholder:"按供应商名称搜索...","prefix-icon":"Search",clearable:"",style:{width:"300px"},onInput:xe},null,8,["modelValue"])]),t("div",xt,[m.status==="pending_confirmation"?(c(),g("div",St,[s(r,{type:"danger",onClick:Te,disabled:v.value.length===0},{default:l(()=>[f(" 调整销售价 ("+i(v.value.length)+") ",1)]),_:1},8,["disabled"])])):q("",!0),m.status==="pending_confirmation"?(c(),g("div",kt,[s(r,{type:"danger",onClick:Se},{default:l(()=>e[21]||(e[21]=[f("全部重置")])),_:1})])):q("",!0)]),t("div",Tt,[s(b,{gutter:20},{default:l(()=>[s(p,{span:6},{default:l(()=>[s(B,{title:"选中物料",value:v.value.length,suffix:"项"},null,8,["value"])]),_:1}),s(p,{span:6},{default:l(()=>[s(B,{title:"选中销售总价",value:M.value,precision:2,prefix:"¥"},null,8,["value"])]),_:1}),s(p,{span:6},{default:l(()=>[s(B,{title:"选中供应商总价",value:D.value,precision:2,prefix:"¥"},null,8,["value"])]),_:1}),s(p,{span:6},{default:l(()=>[s(B,{title:"选中平均利润率",value:E.value,precision:2,suffix:"%"},null,8,["value"])]),_:1})]),_:1})]),s(Re,{data:de.value,border:"",style:{width:"100%"},onSelectionChange:ke},{default:l(()=>[m.status==="pending_confirmation"?(c(),V(h,{key:0,type:"selection",width:"55"})):q("",!0),s(h,{prop:"materialName",label:"物料名称","min-width":"180"}),s(h,{prop:"model",label:"型号","min-width":"120"}),s(h,{prop:"brand",label:"品牌","min-width":"100"}),s(h,{prop:"category",label:"分类","min-width":"100"}),s(h,{prop:"supplierName",label:"供应商名称","min-width":"200"},{default:l(n=>[m.status==="pending_confirmation"?(c(),V(Ne,{key:0,modelValue:n.row.supplierId,"onUpdate:modelValue":P=>n.row.supplierId=P,filterable:"",placeholder:"请选择供应商",onChange:P=>Pe(P,n.row),style:{width:"100%"}},{default:l(()=>[(c(!0),g(ie,null,ze(X.value,P=>(c(),V(Me,{key:P.id,label:P.name,value:P.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])):(c(),g("span",Ct,i(n.row.supplierName),1))]),_:1}),s(h,{prop:"quantity",label:"需求数量",width:"100",align:"center"}),s(h,{prop:"shippedQuantity",label:"已发货数量",width:"100",align:"center"}),s(h,{prop:"receivedQuantity",label:"已收货数量",width:"100",align:"center"}),s(h,{prop:"logisticsStatus",label:"物流状态","min-width":"120"},{default:l(n=>[s(T,{type:pe(n.row.logisticsStatus)},{default:l(()=>[f(i(n.row.logisticsStatus),1)]),_:2},1032,["type"])]),_:1}),s(h,{prop:"financialStatus",label:"财务状态","min-width":"120"},{default:l(n=>[s(T,{type:me(n.row.financialStatus)},{default:l(()=>[f(i(n.row.financialStatus),1)]),_:2},1032,["type"])]),_:1}),s(h,{prop:"supplierPrice",label:"供应商报价",width:"150",align:"right"},{default:l(n=>[m.status==="pending_confirmation"?(c(),V(O,{key:0,modelValue:n.row.supplierPrice,"onUpdate:modelValue":P=>n.row.supplierPrice=P,min:0,precision:2,controls:!1,style:{width:"100%"},onChange:J},null,8,["modelValue","onUpdate:modelValue"])):(c(),g("span",Vt,[n.row.supplierPrice?(c(),g("span",Mt,"¥"+i(_(n.row.supplierPrice)),1)):(c(),g("span",Nt,"-"))]))]),_:1}),s(h,{prop:"freight",label:"运费",width:"120",align:"right"},{default:l(n=>[m.status==="pending_confirmation"?(c(),V(O,{key:0,modelValue:n.row.freight,"onUpdate:modelValue":P=>n.row.freight=P,min:0,precision:2,controls:!1,style:{width:"100%"},onChange:J},null,8,["modelValue","onUpdate:modelValue"])):(c(),g("span",Rt,[n.row.freight?(c(),g("span",Dt,"¥"+i(_(n.row.freight)),1)):(c(),g("span",Bt,"¥0.00"))]))]),_:1}),s(h,{prop:"unitPrice",label:"销售价",width:"120",align:"right"},{default:l(n=>[m.status==="pending_confirmation"?(c(),V(O,{key:0,modelValue:n.row.unitPrice,"onUpdate:modelValue":P=>n.row.unitPrice=P,min:0,precision:2,controls:!1,style:{width:"100%"},onChange:J},null,8,["modelValue","onUpdate:modelValue"])):(c(),g("span",qt,"¥"+i(_(n.row.unitPrice)),1))]),_:1}),s(h,{prop:"totalPrice",label:"小计",width:"120",align:"right"},{default:l(n=>[n.row.totalPrice?(c(),g("span",It,"¥"+i(_(n.row.totalPrice)),1)):(c(),g("span",Ut,"-"))]),_:1}),s(h,{prop:"expectedDeliveryDate",label:"预计到货日期","min-width":"120"})]),_:1},8,["data"]),t("div",Ft,[t("div",Lt,[t("div",Qt,[t("div",At,[t("div",Et,[e[22]||(e[22]=t("span",{class:"summary-label"},"销售总价：",-1)),t("span",Ot,"¥"+i(_(C.value.salesTotal)),1)]),t("div",$t,[e[23]||(e[23]=t("span",{class:"summary-label"},"客户运费：",-1)),t("span",jt,"¥"+i(_(C.value.customerFreight)),1)]),t("div",zt,[e[24]||(e[24]=t("span",{class:"summary-label"},"订单总收入：",-1)),t("span",Ht,"¥"+i(_(C.value.totalRevenue)),1)])]),t("div",Jt,[t("div",Gt,[e[25]||(e[25]=t("span",{class:"summary-label"},"供应商物料总价：",-1)),t("span",Kt,"¥"+i(_(C.value.supplierTotalPrice)),1)]),t("div",Xt,[e[26]||(e[26]=t("span",{class:"summary-label"},"供应商总运费：",-1)),t("span",Wt,"¥"+i(_(C.value.supplierFreightTotal)),1)]),t("div",Yt,[e[27]||(e[27]=t("span",{class:"summary-label"},"订单总支出：",-1)),t("span",Zt,"¥"+i(_(C.value.totalCost)),1)])])]),t("div",es,[e[29]||(e[29]=t("div",{class:"summary-column"},null,-1)),t("div",ts,[t("div",ss,[e[28]||(e[28]=t("span",{class:"summary-label"},"利润率：",-1)),t("span",{class:j(["summary-value",H(C.value.grossProfitRate)])},i(C.value.grossProfitRate)+"% ",3)])])])])])])]),t("div",ls,[e[31]||(e[31]=t("div",{class:"section-header"},[t("h3",null,"收货与开票信息")],-1)),t("div",as,[s(qe,{modelValue:K.value,"onUpdate:modelValue":e[1]||(e[1]=n=>K.value=n),class:"info-tabs"},{default:l(()=>[s(se,{label:"收货信息",name:"delivery"},{default:l(()=>[t("div",is,[s(De,{column:2,border:""},{default:l(()=>[s(L,{label:"联系人"},{default:l(()=>[f(i(F.contactName),1)]),_:1}),s(L,{label:"手机号"},{default:l(()=>[f(i(F.phone),1)]),_:1}),s(L,{label:"地区",span:2},{default:l(()=>[f(i(F.region),1)]),_:1}),s(L,{label:"详细地址",span:2},{default:l(()=>[f(i(F.address),1)]),_:1}),s(L,{label:"备注",span:2},{default:l(()=>[f(i(F.remark||"无"),1)]),_:1})]),_:1})])]),_:1}),s(se,{label:"开票信息",name:"invoice"},{default:l(()=>[t("div",ns,[s(Be,{description:"开票信息功能开发中..."})])]),_:1})]),_:1},8,["modelValue"])])]),s(Le,{modelValue:I.value,"onUpdate:modelValue":e[4]||(e[4]=n=>I.value=n),title:"批量调整销售价",width:"800px"},{footer:l(()=>[t("span",Ts,[s(r,{onClick:Ce},{default:l(()=>e[44]||(e[44]=[f("取消")])),_:1}),s(r,{type:"primary",onClick:Ve},{default:l(()=>e[45]||(e[45]=[f("确定")])),_:1})])]),default:l(()=>[t("div",os,[t("h4",rs,"批量调整对比 ("+i(v.value.length)+" 项物料)",1),s(b,{gutter:24},{default:l(()=>[s(p,{span:12},{default:l(()=>[t("div",ds,[e[36]||(e[36]=t("h5",{class:"section-title"},"当前汇总",-1)),t("div",cs,[e[32]||(e[32]=t("span",{class:"price-label"},"供应商报价总计:",-1)),t("span",us,"¥"+i(_(D.value)),1)]),t("div",ps,[e[33]||(e[33]=t("span",{class:"price-label"},"销售价总计:",-1)),t("span",ms,"¥"+i(_(M.value)),1)]),t("div",_s,[e[34]||(e[34]=t("span",{class:"price-label"},"平均利润率:",-1)),t("span",{class:j(["price-value",H(E.value)])},i(E.value.toFixed(2))+"% ",3)]),t("div",fs,[e[35]||(e[35]=t("span",{class:"price-label"},"总利润:",-1)),t("span",vs,"¥"+i(_(M.value-D.value)),1)])])]),_:1}),s(p,{span:12},{default:l(()=>[t("div",{class:j(["preview-price-section",{"preview-active":y.value!==0}])},[e[41]||(e[41]=t("h5",{class:"section-title"},"调整后预览",-1)),y.value!==0?(c(),g(ie,{key:0},[t("div",ys,[e[37]||(e[37]=t("span",{class:"price-label"},"供应商报价总计:",-1)),t("span",gs,"¥"+i(_(D.value)),1)]),t("div",hs,[e[38]||(e[38]=t("span",{class:"price-label"},"销售价总计:",-1)),t("span",bs,"¥"+i(_(ee())),1)]),t("div",ws,[e[39]||(e[39]=t("span",{class:"price-label"},"平均利润率:",-1)),t("span",{class:j(["price-value preview-value",H(te())])},i(te().toFixed(2))+"% ",3)]),t("div",Ps,[e[40]||(e[40]=t("span",{class:"price-label"},"利润变化:",-1)),t("span",{class:"price-value preview-value",style:He({color:G()>=0?"#67c23a":"#f56c6c"})},i(G()>=0?"+":"")+"¥"+i(_(G())),5)])],64)):(c(),g("div",xs,"请设置调整值查看预览"))],2)]),_:1})]),_:1})]),s(Ie),s(Fe,{model:y,"label-width":"120px"},{default:l(()=>[s(ae,{label:"调整方式"},{default:l(()=>[s(Ue,{modelValue:y.type,"onUpdate:modelValue":e[2]||(e[2]=n=>y.type=n)},{default:l(()=>[s(le,{label:"salePrice"},{default:l(()=>e[42]||(e[42]=[f("销售总价")])),_:1}),s(le,{label:"profitRate"},{default:l(()=>e[43]||(e[43]=[f("按利润率")])),_:1})]),_:1},8,["modelValue"])]),_:1}),s(ae,{label:y.type==="salePrice"?"销售总价":"利润率"},{default:l(()=>[s(O,{modelValue:y.value,"onUpdate:modelValue":e[3]||(e[3]=n=>y.value=n),min:0,precision:2},null,8,["modelValue"]),t("span",Ss,i(y.type==="profitRate"?"%":"元"),1)]),_:1},8,["label"])]),_:1},8,["model"]),y.type==="salePrice"?(c(),g("div",ks,"⚠️ 按销售总价批量调整时，销售总价的变化将等比例分摊到每个选中的物料上。")):q("",!0)]),_:1},8,["modelValue"])])}}},Ns=Qe(Cs,[["__scopeId","data-v-9f257788"]]);export{Ns as default};
