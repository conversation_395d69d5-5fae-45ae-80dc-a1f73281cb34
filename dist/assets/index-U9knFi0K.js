import{_ as Q,d as C,l as X,e as V,f as Z,m as D,o as _,w as o,a as ee,g as te,b as i,r as s,i as c,h as ae,c as ne,t as v,v as k,y as ie,E as h}from"./index-Di-G_xQb.js";const oe={class:"filter-container"},le={class:"table-content-container"},re={__name:"index",setup(se){const M=ie(),S=C(!1),g=C(1),w=C(10),a=X({invoiceId:"",periodRange:[],creationDate:[],status:""}),N=[{id:1,invoiceId:"INV20240501001",periodStart:"2024-04-01",periodEnd:"2024-04-30",creationTime:"2024-05-01 10:00:00",totalAmount:58600,status:"pending",invoiceStatus:"not_invoiced",items:[{id:101,materialCode:"MT001",materialName:"钢板A型",model:"STA-001",specification:"1000x2000x2mm",brand:"宝钢",unit:"块",unitPrice:200,quantity:20,amount:4e3,deliveryNo:"DN20240410001",receiveDate:"2024-04-10",disputed:!1,remarks:""},{id:102,materialCode:"MT002",materialName:"铝合金型材",model:"AL-002",specification:"50x50x5mm",brand:"明泰铝业",unit:"根",unitPrice:120,quantity:50,amount:6e3,deliveryNo:"DN20240415002",receiveDate:"2024-04-15",disputed:!1,remarks:""}]},{id:2,invoiceId:"INV20240501002",periodStart:"2024-04-01",periodEnd:"2024-04-30",creationTime:"2024-05-01 11:30:00",totalAmount:23250,status:"locked",invoiceStatus:"invoicing",items:[{id:201,materialCode:"MT006",materialName:"螺丝螺母套装",model:"SC-006",specification:"M8",brand:"力杰",unit:"套",unitPrice:15,quantity:50,amount:750,deliveryNo:"DN20240405006",receiveDate:"2024-04-05",disputed:!1,remarks:""}]},{id:3,invoiceId:"INV20240501003",periodStart:"2024-04-01",periodEnd:"2024-04-30",creationTime:"2024-05-01 14:15:00",totalAmount:42100,status:"generating",invoiceStatus:"not_invoiced",items:[{id:301,materialCode:"MT009",materialName:"伺服电机",model:"SVM-009",specification:"2kW",brand:"安川",unit:"台",unitPrice:2200,quantity:5,amount:11e3,deliveryNo:"DN20240408009",receiveDate:"2024-04-08",disputed:!1,remarks:""}]},{id:4,invoiceId:"INV20240501004",periodStart:"2024-04-01",periodEnd:"2024-04-30",creationTime:"2024-05-01 16:45:00",totalAmount:16500,status:"settled",invoiceStatus:"invoiced",items:[{id:401,materialCode:"MT013",materialName:"触摸屏",model:"HMI-013",specification:"10英寸",brand:"威纶通",unit:"台",unitPrice:1200,quantity:10,amount:12e3,deliveryNo:"DN20240407013",receiveDate:"2024-04-07",disputed:!1,remarks:""}]}],x=V(()=>{let e=[...N];if(a.invoiceId&&(e=e.filter(l=>l.invoiceId.includes(a.invoiceId))),a.periodRange&&a.periodRange.length===2){const l=new Date(a.periodRange[0]),r=new Date(a.periodRange[1]);e=e.filter(d=>{const f=new Date(d.periodStart),p=new Date(d.periodEnd);return f>=l&&p<=r})}if(a.creationDate&&a.creationDate.length===2){const l=new Date(a.creationDate[0]),r=new Date(a.creationDate[1]);r.setHours(23,59,59),e=e.filter(d=>{const f=new Date(d.creationTime);return f>=l&&f<=r})}a.status&&(e=e.filter(l=>l.status===a.status));const t=(g.value-1)*w.value,u=t+w.value;return e.slice(t,u)}),R=V(()=>{let e=[...N];if(a.invoiceId&&(e=e.filter(t=>t.invoiceId.includes(a.invoiceId))),a.periodRange&&a.periodRange.length===2){const t=new Date(a.periodRange[0]),u=new Date(a.periodRange[1]);e=e.filter(l=>{const r=new Date(l.periodStart),d=new Date(l.periodEnd);return r>=t&&d<=u})}if(a.creationDate&&a.creationDate.length===2){const t=new Date(a.creationDate[0]),u=new Date(a.creationDate[1]);u.setHours(23,59,59),e=e.filter(l=>{const r=new Date(l.creationTime);return r>=t&&r<=u})}return a.status&&(e=e.filter(t=>t.status===a.status)),e.length}),b=()=>{g.value=1,S.value=!0,setTimeout(()=>{S.value=!1},500)},E=()=>{a.invoiceId="",a.periodRange=[],a.creationDate=[],a.status="",g.value=1,b()},$=e=>{w.value=e,g.value=1},z=e=>{g.value=e},I=e=>{if(!e)return"";const t=new Date(e),u=t.getFullYear(),l=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0");return`${u}-${l}-${r}`},A=e=>`¥ ${e.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,",")}`,P=e=>({generating:"生成中",pending:"待对账",locked:"已锁定",settled:"已结算"})[e]||e,q=e=>({generating:"info",pending:"warning",locked:"success",settled:"primary"})[e]||"",B=e=>({invoicing:"开票中",not_invoiced:"未开票",invoiced:"已开票"})[e]||e,U=e=>({invoicing:"warning",not_invoiced:"info",invoiced:"success"})[e]||"",F=e=>{M.push(`/trade/so/inv-detail/${e.id}`)},H=e=>{h.info(`查看付款详情：${e.invoiceId}`)},j=e=>{h.info(`上传发票：${e.invoiceId}`)},L=e=>{h.success(`下载发票：${e.invoiceId}`)};return Z(()=>{b()}),(e,t)=>{const u=s("el-input"),l=s("el-form-item"),r=s("el-date-picker"),d=s("el-option"),f=s("el-select"),p=s("el-button"),W=s("el-form"),Y=s("el-link"),m=s("el-table-column"),T=s("el-tag"),G=s("el-table"),J=s("el-pagination"),K=s("el-card"),O=ae("loading");return _(),D(K,{class:"invoice-reconciliation-container"},{default:o(()=>[ee("div",oe,[i(W,{inline:!0,model:a,class:"search-form"},{default:o(()=>[i(l,{label:"对账单号"},{default:o(()=>[i(u,{modelValue:a.invoiceId,"onUpdate:modelValue":t[0]||(t[0]=n=>a.invoiceId=n),placeholder:"请输入对账单号",clearable:""},null,8,["modelValue"])]),_:1}),i(l,{label:"对账周期"},{default:o(()=>[i(r,{modelValue:a.periodRange,"onUpdate:modelValue":t[1]||(t[1]=n=>a.periodRange=n),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:""},null,8,["modelValue"])]),_:1}),i(l,{label:"创建时间"},{default:o(()=>[i(r,{modelValue:a.creationDate,"onUpdate:modelValue":t[2]||(t[2]=n=>a.creationDate=n),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:""},null,8,["modelValue"])]),_:1}),i(l,{label:"状态"},{default:o(()=>[i(f,{modelValue:a.status,"onUpdate:modelValue":t[3]||(t[3]=n=>a.status=n),placeholder:"请选择状态",clearable:""},{default:o(()=>[i(d,{label:"全部",value:""}),i(d,{label:"生成中",value:"generating"}),i(d,{label:"待对账",value:"pending"}),i(d,{label:"已锁定",value:"locked"}),i(d,{label:"已结算",value:"settled"})]),_:1},8,["modelValue"])]),_:1}),i(l,null,{default:o(()=>[i(p,{type:"primary",onClick:b},{default:o(()=>t[4]||(t[4]=[c("搜索")])),_:1}),i(p,{onClick:E},{default:o(()=>t[5]||(t[5]=[c("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),te((_(),ne("div",le,[i(G,{data:x.value,style:{width:"100%"},border:""},{default:o(()=>[i(m,{prop:"invoiceId",label:"对账单号","min-width":"180"},{default:o(n=>[i(Y,{type:"primary",onClick:y=>F(n.row)},{default:o(()=>[c(v(n.row.invoiceId),1)]),_:2},1032,["onClick"])]),_:1}),i(m,{prop:"periodStart",label:"对账周期","min-width":"200"},{default:o(n=>[c(v(I(n.row.periodStart))+" 至 "+v(I(n.row.periodEnd)),1)]),_:1}),i(m,{prop:"creationTime",label:"创建时间","min-width":"160"},{default:o(n=>[c(v(I(n.row.creationTime)),1)]),_:1}),i(m,{prop:"totalAmount",label:"总金额","min-width":"150",align:"right"},{default:o(n=>[c(v(A(n.row.totalAmount)),1)]),_:1}),i(m,{prop:"status",label:"状态","min-width":"120",align:"center"},{default:o(n=>[i(T,{type:q(n.row.status)},{default:o(()=>[c(v(P(n.row.status)),1)]),_:2},1032,["type"])]),_:1}),i(m,{prop:"invoiceStatus",label:"开票状态","min-width":"120",align:"center"},{default:o(n=>[i(T,{type:U(n.row.invoiceStatus)},{default:o(()=>[c(v(B(n.row.invoiceStatus)),1)]),_:2},1032,["type"])]),_:1}),i(m,{label:"操作","min-width":"200",align:"center",fixed:"right"},{default:o(n=>[n.row.status==="locked"||n.row.status==="settled"?(_(),D(p,{key:0,type:"primary",link:"",size:"small",onClick:y=>H(n.row)},{default:o(()=>t[6]||(t[6]=[c("付款详情")])),_:2},1032,["onClick"])):k("",!0),n.row.invoiceStatus==="invoicing"?(_(),D(p,{key:1,type:"primary",link:"",size:"small",onClick:y=>j(n.row)},{default:o(()=>t[7]||(t[7]=[c("上传发票")])),_:2},1032,["onClick"])):k("",!0),n.row.invoiceStatus==="invoicing"?(_(),D(p,{key:2,type:"primary",link:"",size:"small",onClick:y=>e.handleExport(n.row)},{default:o(()=>t[8]||(t[8]=[c("导出开票信息")])),_:2},1032,["onClick"])):k("",!0),n.row.invoiceStatus==="invoiced"?(_(),D(p,{key:3,type:"primary",link:"",size:"small",onClick:y=>L(n.row)},{default:o(()=>t[9]||(t[9]=[c("下载发票")])),_:2},1032,["onClick"])):k("",!0)]),_:1})]),_:1},8,["data"]),i(J,{class:"pagination-container","current-page":g.value,"page-sizes":[10,20,50,100],"page-size":w.value,layout:"total, sizes, prev, pager, next, jumper",total:R.value,onSizeChange:$,onCurrentChange:z},null,8,["current-page","page-size","total"])])),[[O,S.value]])]),_:1})}}},ce=Q(re,[["__scopeId","data-v-2dcbc9e3"]]);export{ce as default};
