import{_ as Ct,d as b,l as Q,e as Y,f as wt,c as I,o as f,b as t,a as o,w as l,r as s,i as r,t as p,g as we,m as w,L as Vt,M as kt,F as X,j as Z,N as xt,v as L,h as It,E as m,k as H,B as W}from"./index-Di-G_xQb.js";const Lt={class:"supplier-pool-container"},Tt={class:"header-content"},Dt={class:"action-buttons"},Bt={class:"stats-section"},At={class:"stat-content"},Ut={class:"stat-number"},Nt={class:"stat-content"},Mt={class:"stat-number"},$t={class:"stat-content"},zt={class:"stat-number"},Rt={class:"stat-content"},Et={class:"stat-number"},Ft={class:"filter-header"},Pt={class:"basic-search"},Ot={class:"advanced-search"},jt={class:"advanced-actions"},Yt={class:"toolbar"},Kt={class:"toolbar-left"},qt={key:0,class:"selected-count"},Gt={class:"toolbar-center"},Xt={class:"toolbar-right"},Zt={key:0,class:"table-container"},Ht={class:"company-info"},Jt={class:"company-details"},Qt={class:"company-name"},Wt={class:"company-id"},el={key:1,class:"card-container"},tl={class:"card-header"},ll={class:"card-actions"},al={class:"card-content"},nl={class:"company-name"},ol={class:"company-info"},sl={class:"card-tags"},il={class:"card-metrics"},rl={class:"metric"},ul={class:"metric"},dl={class:"card-footer"},cl={class:"capital"},pl={class:"establishment"},ml={class:"pagination-container"},fl={__name:"Supplier",setup(vl){const J=b(!1),U=b("table"),P=b(!1),N=b(!1),c=b([]),M=b(!1),k=b(!1),$=b(!1),z=b(!1),R=b(!1),V=b(null),i=Q({keyword:"",region:"",industry:"",verificationStatus:"",minCapital:null,maxCapital:null,establishmentDateRange:[],employeeScale:"",factoryLevel:"",ratingRange:[0,5],supplierType:""}),h=Q({currentPage:1,pageSize:20}),T=Q({prop:"",order:""}),g=b([{supplierId:"SUP001",companyName:"深圳智能制造科技有限公司",companyLogo:"",region:"广东省深圳市",industry:"智能制造",registrationNumber:"91440300MA5DA1234X",unifiedSocialCreditCode:"91440300MA5DA1234X",establishmentDate:new Date("2018-03-15").getTime(),registeredCapital:5e7,companyType:"有限责任公司",legalRepresentative:"张三",businessScope:"智能设备研发、生产、销售；工业自动化系统集成",registrationAddress:"深圳市南山区科技园南区",phone:"0755-12345678",email:"<EMAIL>",emailList:["<EMAIL>","<EMAIL>"],website:"https://www.smartmfg.com",companyAddress:"深圳市南山区科技园南区智能制造大厦15楼",factoryLevel:"A",levelScore:95,serviceYears:6,responseScore:4.8,ratingScore:4.7,complianceScore:98,buyerIntent:85,categoryName:"智能制造设备",homepageLink:"https://www.smartmfg.com",searchKeywords:["智能制造","自动化","工业4.0"],companySlogan:"智造未来，科技领先",promotionalImages:[],mainBusiness:"智能制造设备研发与生产",businessLicense:"",legalRepIdFront:"",legalRepIdBack:"",factoryOwnershipCert:"",factoryArea:15e3,employeeCount:280,annualRevenue:12e7,qualityCertifications:["ISO9001","ISO14001","CE"],customerCaseStudies:[],factoryVideos:[],supplierType:"manufacturer",mainBrands:["SmartTech","AutoMaster"],agencyBrands:[],verificationStatus:"verified",selected:!1},{supplierId:"SUP002",companyName:"上海精密仪器有限公司",companyLogo:"",region:"上海市",industry:"精密仪器",registrationNumber:"91310000MA1FL5678Y",unifiedSocialCreditCode:"91310000MA1FL5678Y",establishmentDate:new Date("2015-08-20").getTime(),registeredCapital:3e7,companyType:"有限责任公司",legalRepresentative:"李四",businessScope:"精密仪器设备制造、销售、维修服务",registrationAddress:"上海市浦东新区张江高科技园区",phone:"021-87654321",email:"<EMAIL>",emailList:["<EMAIL>"],website:"https://www.precision-sh.com",companyAddress:"上海市浦东新区张江高科技园区创新大道100号",factoryLevel:"B",levelScore:88,serviceYears:9,responseScore:4.5,ratingScore:4.3,complianceScore:92,buyerIntent:78,categoryName:"精密测量仪器",homepageLink:"https://www.precision-sh.com",searchKeywords:["精密仪器","测量设备","检测"],companySlogan:"精益求精，测量未来",promotionalImages:[],mainBusiness:"精密测量仪器制造",businessLicense:"",legalRepIdFront:"",legalRepIdBack:"",factoryOwnershipCert:"",factoryArea:8e3,employeeCount:150,annualRevenue:8e7,qualityCertifications:["ISO9001","CNAS"],customerCaseStudies:[],factoryVideos:[],supplierType:"manufacturer",mainBrands:["PrecisionTech"],agencyBrands:["Mitutoyo","Zeiss"],verificationStatus:"pending",selected:!1}]),Ve=b([{value:"北京市",label:"北京市"},{value:"上海市",label:"上海市"},{value:"广东省深圳市",label:"深圳市"},{value:"广东省广州市",label:"广州市"},{value:"浙江省杭州市",label:"杭州市"},{value:"江苏省苏州市",label:"苏州市"}]),ke=b([{value:"智能制造",label:"智能制造"},{value:"精密仪器",label:"精密仪器"},{value:"电子元器件",label:"电子元器件"},{value:"机械设备",label:"机械设备"},{value:"新能源",label:"新能源"},{value:"生物医药",label:"生物医药"}]),xe=b([{value:"verified",label:"已验证"},{value:"pending",label:"待验证"},{value:"rejected",label:"验证失败"},{value:"unverified",label:"未验证"}]),K=Y(()=>{const n=g.value.length,e=g.value.filter(u=>u.verificationStatus==="verified").length,_=g.value.filter(u=>u.verificationStatus==="pending").length,d=g.value.filter(u=>u.ratingScore>=4).length;return{total:n,verified:e,pending:_,active:d}}),ee=Y(()=>{let n=[...g.value];if(i.keyword){const e=i.keyword.toLowerCase();n=n.filter(_=>_.companyName.toLowerCase().includes(e)||_.unifiedSocialCreditCode.includes(e)||_.legalRepresentative.toLowerCase().includes(e))}return i.region&&(n=n.filter(e=>e.region===i.region)),i.industry&&(n=n.filter(e=>e.industry===i.industry)),i.verificationStatus&&(n=n.filter(e=>e.verificationStatus===i.verificationStatus)),i.minCapital!==null&&(n=n.filter(e=>e.registeredCapital>=i.minCapital)),i.maxCapital!==null&&(n=n.filter(e=>e.registeredCapital<=i.maxCapital)),i.factoryLevel&&(n=n.filter(e=>e.factoryLevel===i.factoryLevel)),i.supplierType&&(n=n.filter(e=>e.supplierType===i.supplierType)),n=n.filter(e=>e.ratingScore>=i.ratingRange[0]&&e.ratingScore<=i.ratingRange[1]),T.prop&&T.order&&n.sort((e,_)=>{const d=e[T.prop],u=_[T.prop],q=T.order==="ascending"?1:-1;return d<u?-1*q:d>u?1*q:0}),n}),D=Y(()=>{const n=(h.currentPage-1)*h.pageSize,e=n+h.pageSize;return ee.value.slice(n,e)}),Ie=Y(()=>{const n=c.value.length,e=D.value.length;return n>0&&n<e}),te=Y(()=>g.value.filter(n=>c.value.includes(n.supplierId))),Le=()=>{P.value=!P.value},O=()=>{J.value=!0,h.currentPage=1,setTimeout(()=>{J.value=!1,m.success("搜索完成")},300)},Te=()=>{Object.assign(i,{keyword:"",region:"",industry:"",verificationStatus:"",minCapital:null,maxCapital:null,establishmentDateRange:[],employeeScale:"",factoryLevel:"",ratingRange:[0,5],supplierType:""}),O()},De=n=>{n?c.value=D.value.map(e=>e.supplierId):c.value=[],D.value.forEach(e=>{e.selected=n})},Be=n=>{c.value=n.map(e=>e.supplierId),N.value=n.length===D.value.length},Ae=n=>{if(n.selected)c.value.includes(n.supplierId)||c.value.push(n.supplierId);else{const e=c.value.indexOf(n.supplierId);e>-1&&c.value.splice(e,1)}N.value=c.value.length===D.value.length},Ue=({prop:n,order:e})=>{T.prop=n,T.order=e},Ne=n=>{h.pageSize=n,h.currentPage=1},Me=n=>{h.currentPage=n},$e=()=>{V.value=null,R.value=!1,k.value=!0},le=n=>{V.value={...n},R.value=!0,k.value=!0},ae=n=>{V.value=n,M.value=!0},ze=n=>{if(R.value){const e=g.value.findIndex(_=>_.supplierId===n.supplierId);e>-1&&(g.value[e]={...n},m.success("供应商信息更新成功"))}else{const e={...n,supplierId:`SUP${Date.now()}`,selected:!1};g.value.push(e),m.success("供应商添加成功")}k.value=!1},Re=n=>{const e=g.value.findIndex(_=>_.supplierId===n.supplierId);e>-1&&(g.value[e]={...n},m.success("供应商信息更新成功"))},ne=n=>{m.info(`正在为 ${n.companyName} 发起询价...`)},Ee=()=>{if(c.value.length<2){m.warning("请至少选择2个供应商进行对比");return}$.value=!0},Fe=(n,e)=>{switch(n){case"verify":Pe(e);break;case"favorite":Oe(e);break;case"export":je(e);break;case"delete":Ye(e);break}},Pe=n=>{H.confirm(`确认验证供应商 ${n.companyName}？`,"确认验证",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{n.verificationStatus="verified",m.success("供应商验证成功")}).catch(()=>{m.info("已取消验证")})},Oe=n=>{m.success(`已将 ${n.companyName} 添加到收藏`)},je=n=>{m.info(`正在导出 ${n.companyName} 的信息...`)},Ye=n=>{H.confirm(`确认删除供应商 ${n.companyName}？此操作不可恢复。`,"确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(()=>{const e=g.value.findIndex(_=>_.supplierId===n.supplierId);e>-1&&(g.value.splice(e,1),m.success("供应商删除成功"))}).catch(()=>{m.info("已取消删除")})},Ke=n=>{if(c.value.length===0){m.warning("请先选择要操作的供应商");return}switch(n){case"verify":qe();break;case"export":Ge();break;case"delete":Xe();break}},qe=()=>{H.confirm(`确认批量验证选中的 ${c.value.length} 个供应商？`,"批量验证",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{te.value.forEach(n=>{n.verificationStatus="verified"}),m.success(`已成功验证 ${c.value.length} 个供应商`),c.value=[],N.value=!1}).catch(()=>{m.info("已取消批量验证")})},Ge=()=>{m.info(`正在导出选中的 ${c.value.length} 个供应商信息...`)},Xe=()=>{H.confirm(`确认删除选中的 ${c.value.length} 个供应商？此操作不可恢复。`,"批量删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(()=>{c.value.forEach(n=>{const e=g.value.findIndex(_=>_.supplierId===n);e>-1&&g.value.splice(e,1)}),m.success(`已成功删除 ${c.value.length} 个供应商`),c.value=[],N.value=!1}).catch(()=>{m.info("已取消批量删除")})},Ze=()=>{z.value=!0},He=()=>{m.info("正在导出所有供应商数据...")},Je=n=>{g.value.push(...n),m.success(`成功导入 ${n.length} 个供应商`),z.value=!1},Qe=()=>{M.value=!1,V.value=null},We=()=>{k.value=!1,V.value=null,R.value=!1},et=()=>{$.value=!1},oe=n=>({manufacturer:"success",agent:"warning",trader:"info",service:"primary"})[n]||"info",se=n=>({manufacturer:"制造商",agent:"代理商",trader:"贸易商",service:"服务商"})[n]||n,ie=n=>({verified:"success",pending:"warning",rejected:"danger",unverified:"info"})[n]||"info",re=n=>({verified:"已验证",pending:"待验证",rejected:"验证失败",unverified:"未验证"})[n]||n,ue=n=>({A:"success",B:"primary",C:"warning",D:"danger"})[n]||"info",de=n=>n?new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY",minimumFractionDigits:0}).format(n):"0",ce=n=>n?new Date(n).toLocaleDateString("zh-CN"):"";return wt(()=>{O()}),(n,e)=>{var Ce;const _=s("Plus"),d=s("el-icon"),u=s("el-button"),q=s("Upload"),tt=s("Download"),x=s("el-card"),lt=s("OfficeBuilding"),v=s("el-col"),at=s("CircleCheck"),nt=s("Clock"),ot=s("Star"),j=s("el-row"),pe=s("Search"),st=s("el-input"),y=s("el-option"),E=s("el-select"),it=s("el-divider"),me=s("el-input-number"),B=s("el-form-item"),rt=s("el-date-picker"),ut=s("el-slider"),fe=s("el-checkbox"),dt=s("List"),ct=s("Grid"),pt=s("el-button-group"),ve=s("ArrowDown"),A=s("el-dropdown-item"),_e=s("el-dropdown-menu"),ge=s("el-dropdown"),mt=s("Scale"),C=s("el-table-column"),ye=s("el-avatar"),F=s("el-tag"),be=s("el-rate"),ft=s("View"),Se=s("Edit"),he=s("ChatDotRound"),vt=s("el-table"),_t=s("el-pagination"),gt=s("SupplierDetail"),G=s("el-dialog"),yt=s("SupplierForm"),bt=s("SupplierComparison"),St=s("BulkImport"),ht=It("loading");return f(),I("div",Lt,[t(x,{class:"header-card",shadow:"never"},{default:l(()=>[o("div",Tt,[e[28]||(e[28]=o("div",{class:"title-section"},[o("h2",null,"供应商池管理"),o("p",{class:"subtitle"},"管理和维护供应商信息池，支持供应商发现、比较和验证流程")],-1)),o("div",Dt,[t(u,{type:"primary",onClick:$e},{default:l(()=>[t(d,null,{default:l(()=>[t(_)]),_:1}),e[25]||(e[25]=r(" 添加供应商 "))]),_:1}),t(u,{onClick:Ze},{default:l(()=>[t(d,null,{default:l(()=>[t(q)]),_:1}),e[26]||(e[26]=r(" 批量导入 "))]),_:1}),t(u,{onClick:He},{default:l(()=>[t(d,null,{default:l(()=>[t(tt)]),_:1}),e[27]||(e[27]=r(" 导出数据 "))]),_:1})])])]),_:1}),o("div",Bt,[t(j,{gutter:20},{default:l(()=>[t(v,{span:6},{default:l(()=>[t(x,{class:"stat-card"},{default:l(()=>[o("div",At,[o("div",Ut,p(K.value.total),1),e[29]||(e[29]=o("div",{class:"stat-label"},"总供应商数",-1))]),t(d,{class:"stat-icon"},{default:l(()=>[t(lt)]),_:1})]),_:1})]),_:1}),t(v,{span:6},{default:l(()=>[t(x,{class:"stat-card"},{default:l(()=>[o("div",Nt,[o("div",Mt,p(K.value.verified),1),e[30]||(e[30]=o("div",{class:"stat-label"},"已验证",-1))]),t(d,{class:"stat-icon verified"},{default:l(()=>[t(at)]),_:1})]),_:1})]),_:1}),t(v,{span:6},{default:l(()=>[t(x,{class:"stat-card"},{default:l(()=>[o("div",$t,[o("div",zt,p(K.value.pending),1),e[31]||(e[31]=o("div",{class:"stat-label"},"待验证",-1))]),t(d,{class:"stat-icon pending"},{default:l(()=>[t(nt)]),_:1})]),_:1})]),_:1}),t(v,{span:6},{default:l(()=>[t(x,{class:"stat-card"},{default:l(()=>[o("div",Rt,[o("div",Et,p(K.value.active),1),e[32]||(e[32]=o("div",{class:"stat-label"},"活跃供应商",-1))]),t(d,{class:"stat-icon active"},{default:l(()=>[t(ot)]),_:1})]),_:1})]),_:1})]),_:1})]),t(x,{class:"filter-card",shadow:"never"},{default:l(()=>[o("div",Ft,[e[33]||(e[33]=o("h3",null,"搜索与筛选",-1)),t(u,{type:"text",onClick:Le,class:"toggle-advanced"},{default:l(()=>[r(p(P.value?"收起高级搜索":"展开高级搜索")+" ",1),t(d,null,{default:l(()=>[(f(),w(Vt(P.value?"ArrowUp":"ArrowDown")))]),_:1})]),_:1})]),o("div",Pt,[t(j,{gutter:20},{default:l(()=>[t(v,{span:8},{default:l(()=>[t(st,{modelValue:i.keyword,"onUpdate:modelValue":e[0]||(e[0]=a=>i.keyword=a),placeholder:"搜索公司名称、统一社会信用代码、联系人...",clearable:"",onKeyup:kt(O,["enter"])},{prefix:l(()=>[t(d,null,{default:l(()=>[t(pe)]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(v,{span:4},{default:l(()=>[t(E,{modelValue:i.region,"onUpdate:modelValue":e[1]||(e[1]=a=>i.region=a),placeholder:"选择地区",clearable:""},{default:l(()=>[(f(!0),I(X,null,Z(Ve.value,a=>(f(),w(y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,{span:4},{default:l(()=>[t(E,{modelValue:i.industry,"onUpdate:modelValue":e[2]||(e[2]=a=>i.industry=a),placeholder:"选择行业",clearable:""},{default:l(()=>[(f(!0),I(X,null,Z(ke.value,a=>(f(),w(y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,{span:4},{default:l(()=>[t(E,{modelValue:i.verificationStatus,"onUpdate:modelValue":e[3]||(e[3]=a=>i.verificationStatus=a),placeholder:"验证状态",clearable:""},{default:l(()=>[(f(!0),I(X,null,Z(xe.value,a=>(f(),w(y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,{span:4},{default:l(()=>[t(u,{type:"primary",onClick:O},{default:l(()=>[t(d,null,{default:l(()=>[t(pe)]),_:1}),e[34]||(e[34]=r(" 搜索 "))]),_:1})]),_:1})]),_:1})]),we(o("div",Ot,[t(it),t(j,{gutter:20},{default:l(()=>[t(v,{span:6},{default:l(()=>[t(B,{label:"注册资本范围"},{default:l(()=>[t(me,{modelValue:i.minCapital,"onUpdate:modelValue":e[4]||(e[4]=a=>i.minCapital=a),placeholder:"最小值",min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),t(v,{span:6},{default:l(()=>[t(B,{label:"至"},{default:l(()=>[t(me,{modelValue:i.maxCapital,"onUpdate:modelValue":e[5]||(e[5]=a=>i.maxCapital=a),placeholder:"最大值",min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),t(v,{span:6},{default:l(()=>[t(B,{label:"成立时间"},{default:l(()=>[t(rt,{modelValue:i.establishmentDateRange,"onUpdate:modelValue":e[6]||(e[6]=a=>i.establishmentDateRange=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),t(v,{span:6},{default:l(()=>[t(B,{label:"员工规模"},{default:l(()=>[t(E,{modelValue:i.employeeScale,"onUpdate:modelValue":e[7]||(e[7]=a=>i.employeeScale=a),placeholder:"选择员工规模",clearable:""},{default:l(()=>[t(y,{label:"1-50人",value:"1-50"}),t(y,{label:"51-200人",value:"51-200"}),t(y,{label:"201-500人",value:"201-500"}),t(y,{label:"500人以上",value:"500+"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(j,{gutter:20},{default:l(()=>[t(v,{span:6},{default:l(()=>[t(B,{label:"工厂等级"},{default:l(()=>[t(E,{modelValue:i.factoryLevel,"onUpdate:modelValue":e[8]||(e[8]=a=>i.factoryLevel=a),placeholder:"选择工厂等级",clearable:""},{default:l(()=>[t(y,{label:"A级",value:"A"}),t(y,{label:"B级",value:"B"}),t(y,{label:"C级",value:"C"}),t(y,{label:"D级",value:"D"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(v,{span:6},{default:l(()=>[t(B,{label:"评分范围"},{default:l(()=>[t(ut,{modelValue:i.ratingRange,"onUpdate:modelValue":e[9]||(e[9]=a=>i.ratingRange=a),range:"",min:0,max:5,step:.1,"show-stops":"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),t(v,{span:6},{default:l(()=>[t(B,{label:"供应商类型"},{default:l(()=>[t(E,{modelValue:i.supplierType,"onUpdate:modelValue":e[10]||(e[10]=a=>i.supplierType=a),placeholder:"选择供应商类型",clearable:""},{default:l(()=>[t(y,{label:"制造商",value:"manufacturer"}),t(y,{label:"代理商",value:"agent"}),t(y,{label:"贸易商",value:"trader"}),t(y,{label:"服务商",value:"service"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(v,{span:6},{default:l(()=>[o("div",jt,[t(u,{onClick:Te},{default:l(()=>e[35]||(e[35]=[r("重置")])),_:1}),t(u,{type:"primary",onClick:O},{default:l(()=>e[36]||(e[36]=[r("应用筛选")])),_:1})])]),_:1})]),_:1})],512),[[xt,P.value]])]),_:1}),t(x,{class:"content-card",shadow:"never"},{default:l(()=>[o("div",Yt,[o("div",Kt,[t(fe,{modelValue:N.value,"onUpdate:modelValue":e[11]||(e[11]=a=>N.value=a),onChange:De,indeterminate:Ie.value},{default:l(()=>e[37]||(e[37]=[r(" 全选 ")])),_:1},8,["modelValue","indeterminate"]),c.value.length>0?(f(),I("span",qt," 已选择 "+p(c.value.length)+" 项 ",1)):L("",!0)]),o("div",Gt,[t(pt,null,{default:l(()=>[t(u,{type:U.value==="table"?"primary":"",onClick:e[12]||(e[12]=a=>U.value="table")},{default:l(()=>[t(d,null,{default:l(()=>[t(dt)]),_:1}),e[38]||(e[38]=r(" 列表视图 "))]),_:1},8,["type"]),t(u,{type:U.value==="card"?"primary":"",onClick:e[13]||(e[13]=a=>U.value="card")},{default:l(()=>[t(d,null,{default:l(()=>[t(ct)]),_:1}),e[39]||(e[39]=r(" 卡片视图 "))]),_:1},8,["type"])]),_:1})]),o("div",Xt,[c.value.length>0?(f(),w(ge,{key:0,onCommand:Ke},{dropdown:l(()=>[t(_e,null,{default:l(()=>[t(A,{command:"verify"},{default:l(()=>e[41]||(e[41]=[r("批量验证")])),_:1}),t(A,{command:"export"},{default:l(()=>e[42]||(e[42]=[r("导出选中")])),_:1}),t(A,{command:"delete",divided:""},{default:l(()=>e[43]||(e[43]=[r("批量删除")])),_:1})]),_:1})]),default:l(()=>[t(u,{type:"primary"},{default:l(()=>[e[40]||(e[40]=r(" 批量操作 ")),t(d,null,{default:l(()=>[t(ve)]),_:1})]),_:1})]),_:1})):L("",!0),t(u,{onClick:Ee,disabled:c.value.length<2},{default:l(()=>[t(d,null,{default:l(()=>[t(mt)]),_:1}),e[44]||(e[44]=r(" 对比供应商 "))]),_:1},8,["disabled"])])]),U.value==="table"?(f(),I("div",Zt,[we((f(),w(vt,{ref:"supplierTable",data:D.value,onSelectionChange:Be,onSortChange:Ue,stripe:"",style:{width:"100%"}},{default:l(()=>[t(C,{type:"selection",width:"55"}),t(C,{prop:"companyName",label:"公司名称","min-width":"200",sortable:"custom"},{default:l(({row:a})=>[o("div",Ht,[t(ye,{src:a.companyLogo,size:32,class:"company-avatar"},{default:l(()=>[r(p(a.companyName.charAt(0)),1)]),_:2},1032,["src"]),o("div",Jt,[o("div",Qt,p(a.companyName),1),o("div",Wt,"ID: "+p(a.supplierId),1)])])]),_:1}),t(C,{prop:"region",label:"地区",width:"120"}),t(C,{prop:"industry",label:"行业",width:"120"}),t(C,{prop:"supplierType",label:"类型",width:"100"},{default:l(({row:a})=>[t(F,{type:oe(a.supplierType)},{default:l(()=>[r(p(se(a.supplierType)),1)]),_:2},1032,["type"])]),_:1}),t(C,{prop:"verificationStatus",label:"验证状态",width:"120"},{default:l(({row:a})=>[t(F,{type:ie(a.verificationStatus)},{default:l(()=>[r(p(re(a.verificationStatus)),1)]),_:2},1032,["type"])]),_:1}),t(C,{prop:"ratingScore",label:"评分",width:"100",sortable:"custom"},{default:l(({row:a})=>[t(be,{modelValue:a.ratingScore,"onUpdate:modelValue":S=>a.ratingScore=S,disabled:"","show-score":"","text-color":"#ff9900","score-template":"{value}"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),t(C,{prop:"factoryLevel",label:"工厂等级",width:"100"},{default:l(({row:a})=>[t(F,{type:ue(a.factoryLevel)},{default:l(()=>[r(p(a.factoryLevel)+"级 ",1)]),_:2},1032,["type"])]),_:1}),t(C,{prop:"registeredCapital",label:"注册资本",width:"120",sortable:"custom"},{default:l(({row:a})=>[r(p(de(a.registeredCapital)),1)]),_:1}),t(C,{prop:"establishmentDate",label:"成立时间",width:"120",sortable:"custom"},{default:l(({row:a})=>[r(p(ce(a.establishmentDate)),1)]),_:1}),t(C,{label:"操作",width:"200",fixed:"right"},{default:l(({row:a})=>[t(u,{type:"text",onClick:S=>ae(a)},{default:l(()=>[t(d,null,{default:l(()=>[t(ft)]),_:1}),e[45]||(e[45]=r(" 查看 "))]),_:2},1032,["onClick"]),t(u,{type:"text",onClick:S=>le(a)},{default:l(()=>[t(d,null,{default:l(()=>[t(Se)]),_:1}),e[46]||(e[46]=r(" 编辑 "))]),_:2},1032,["onClick"]),t(u,{type:"text",onClick:S=>ne(a)},{default:l(()=>[t(d,null,{default:l(()=>[t(he)]),_:1}),e[47]||(e[47]=r(" 询价 "))]),_:2},1032,["onClick"]),t(ge,{onCommand:S=>Fe(S,a)},{dropdown:l(()=>[t(_e,null,{default:l(()=>[t(A,{command:"verify"},{default:l(()=>e[49]||(e[49]=[r("验证供应商")])),_:1}),t(A,{command:"favorite"},{default:l(()=>e[50]||(e[50]=[r("添加收藏")])),_:1}),t(A,{command:"export"},{default:l(()=>e[51]||(e[51]=[r("导出信息")])),_:1}),t(A,{command:"delete",divided:""},{default:l(()=>e[52]||(e[52]=[r("删除")])),_:1})]),_:1})]),default:l(()=>[t(u,{type:"text"},{default:l(()=>[e[48]||(e[48]=r(" 更多 ")),t(d,null,{default:l(()=>[t(ve)]),_:1})]),_:1})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[ht,J.value]])])):L("",!0),U.value==="card"?(f(),I("div",el,[t(j,{gutter:20},{default:l(()=>[(f(!0),I(X,null,Z(D.value,a=>(f(),w(v,{key:a.supplierId,span:8,class:"supplier-card-col"},{default:l(()=>[t(x,{class:"supplier-card",shadow:"hover",onClick:S=>ae(a)},{default:l(()=>[o("div",tl,[t(fe,{modelValue:a.selected,"onUpdate:modelValue":S=>a.selected=S,onChange:S=>Ae(a),onClick:e[14]||(e[14]=W(()=>{},["stop"]))},null,8,["modelValue","onUpdate:modelValue","onChange"]),t(ye,{src:a.companyLogo,size:40,class:"company-avatar"},{default:l(()=>[r(p(a.companyName.charAt(0)),1)]),_:2},1032,["src"]),o("div",ll,[t(u,{type:"text",onClick:W(S=>le(a),["stop"])},{default:l(()=>[t(d,null,{default:l(()=>[t(Se)]),_:1})]),_:2},1032,["onClick"]),t(u,{type:"text",onClick:W(S=>ne(a),["stop"])},{default:l(()=>[t(d,null,{default:l(()=>[t(he)]),_:1})]),_:2},1032,["onClick"])])]),o("div",al,[o("h4",nl,p(a.companyName),1),o("p",ol,p(a.region)+" · "+p(a.industry),1),o("div",sl,[t(F,{type:oe(a.supplierType),size:"small"},{default:l(()=>[r(p(se(a.supplierType)),1)]),_:2},1032,["type"]),t(F,{type:ie(a.verificationStatus),size:"small"},{default:l(()=>[r(p(re(a.verificationStatus)),1)]),_:2},1032,["type"])]),o("div",il,[o("div",rl,[e[53]||(e[53]=o("span",{class:"metric-label"},"评分:",-1)),t(be,{modelValue:a.ratingScore,"onUpdate:modelValue":S=>a.ratingScore=S,disabled:"",size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),o("div",ul,[e[54]||(e[54]=o("span",{class:"metric-label"},"等级:",-1)),t(F,{type:ue(a.factoryLevel),size:"small"},{default:l(()=>[r(p(a.factoryLevel)+"级 ",1)]),_:2},1032,["type"])])]),o("div",dl,[o("span",cl,"注册资本: "+p(de(a.registeredCapital)),1),o("span",pl,"成立: "+p(ce(a.establishmentDate)),1)])])]),_:2},1032,["onClick"])]),_:2},1024))),128))]),_:1})])):L("",!0),o("div",ml,[t(_t,{"current-page":h.currentPage,"onUpdate:currentPage":e[15]||(e[15]=a=>h.currentPage=a),"page-size":h.pageSize,"onUpdate:pageSize":e[16]||(e[16]=a=>h.pageSize=a),"page-sizes":[10,20,50,100],total:ee.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ne,onCurrentChange:Me},null,8,["current-page","page-size","total"])])]),_:1}),t(G,{modelValue:M.value,"onUpdate:modelValue":e[18]||(e[18]=a=>M.value=a),title:((Ce=V.value)==null?void 0:Ce.companyName)||"供应商详情",width:"80%","before-close":Qe},{default:l(()=>[M.value&&V.value?(f(),w(gt,{key:0,supplier:V.value,onUpdate:Re,onClose:e[17]||(e[17]=a=>M.value=!1)},null,8,["supplier"])):L("",!0)]),_:1},8,["modelValue","title"]),t(G,{modelValue:k.value,"onUpdate:modelValue":e[20]||(e[20]=a=>k.value=a),title:R.value?"编辑供应商":"添加供应商",width:"70%","before-close":We},{default:l(()=>[k.value?(f(),w(yt,{key:0,supplier:V.value,"is-editing":R.value,onSave:ze,onCancel:e[19]||(e[19]=a=>k.value=!1)},null,8,["supplier","is-editing"])):L("",!0)]),_:1},8,["modelValue","title"]),t(G,{modelValue:$.value,"onUpdate:modelValue":e[22]||(e[22]=a=>$.value=a),title:"供应商对比",width:"90%","before-close":et},{default:l(()=>[$.value?(f(),w(bt,{key:0,suppliers:te.value,onClose:e[21]||(e[21]=a=>$.value=!1)},null,8,["suppliers"])):L("",!0)]),_:1},8,["modelValue"]),t(G,{modelValue:z.value,"onUpdate:modelValue":e[24]||(e[24]=a=>z.value=a),title:"批量导入供应商",width:"60%"},{default:l(()=>[z.value?(f(),w(St,{key:0,onSuccess:Je,onClose:e[23]||(e[23]=a=>z.value=!1)})):L("",!0)]),_:1},8,["modelValue"])])}}},gl=Ct(fl,[["__scopeId","data-v-6e9870d0"]]);export{gl as default};
