import{_ as we,l as J,d as x,e as V,f as Se,m as Te,o as h,w as i,a as t,g as Ce,b as l,r as m,i as _,u as O,p as xe,q as Ve,h as qe,c as C,t as r,n as q,v as W,F as X,x as Re,E as w,k as Y}from"./index-Di-G_xQb.js";const ke={class:"filter-container"},Ne={class:"button-container"},ze={class:"left-buttons"},Fe={class:"right-buttons"},Le={class:"statistics-container"},Ue={class:"table-container"},je={class:"pagination-container"},Be={class:"price-comparison-container"},Ae={key:0},Ee={class:"current-price-section"},De={class:"price-item"},Ie={class:"price-value"},Me={class:"price-item"},Qe={class:"price-value"},$e={class:"price-item"},Ke={class:"price-item"},Ge={class:"price-value"},He={class:"price-item"},Je={class:"price-value"},Oe={class:"price-item"},We={class:"price-value preview-value"},Xe={class:"price-item"},Ye={class:"price-item"},Ze={class:"price-value preview-value"},et={key:1,class:"no-preview"},tt={key:1},lt={style:{"margin-bottom":"16px",color:"#333","text-align":"center"}},at={class:"current-price-section"},it={class:"price-item"},st={class:"price-value"},ot={class:"price-item"},nt={class:"price-value"},rt={class:"price-item"},ut={class:"price-item"},ct={class:"price-value"},pt={class:"price-item"},dt={class:"price-value"},vt={class:"price-item"},mt={class:"price-value preview-value"},ft={class:"price-item"},gt={class:"price-item"},_t={key:1,class:"no-preview"},bt={style:{"margin-left":"10px",color:"#666"}},Pt={key:0,style:{color:"#f56c6c","margin-left":"50px","margin-top":"10px",width:"400px"}},yt={class:"dialog-footer"},ht={__name:"Adjust",setup(wt){const o=J({customer:"",model:"",brand:"",category:"",rfqNo:"",pageNum:1,pageSize:20}),U=x(!1),I=x(0),j=x([]),p=x([]),R=x(!1),f=x(null),n=J({type:"salePrice",value:0,scope:"current"}),b=x(""),M=[{id:1,customer:"深圳科技有限公司",productName:"电阻器 1K欧姆",model:"RES-1K-001",brand:"村田",category:"电阻",quantity:1e3,supplierPrice:.15,deliveryTime:"7天",salePrice:.2,rfqNo:"RFQ-2025-001",rfqTime:"2025-05-25 10:30:00",updateTime:"2025-05-26 10:30:00"},{id:2,customer:"上海电子股份公司",productName:"电容器 100uF",model:"CAP-100UF-002",brand:"TDK",category:"电容",quantity:500,supplierPrice:.8,deliveryTime:"10天",salePrice:1.1,rfqNo:"RFQ-2025-002",rfqTime:"2025-05-26 14:20:00",updateTime:"2025-05-26 10:30:00"},{id:3,customer:"北京智能制造有限公司",productName:"MCU芯片 STM32F103",model:"STM32F103C8T6",brand:"ST",category:"芯片",quantity:200,supplierPrice:12.5,deliveryTime:"15天",salePrice:16,rfqNo:"RFQ-2025-003",rfqTime:"2025-05-27 09:15:00",updateTime:"2025-05-26 10:30:00"}],Z=V(()=>p.value.length),y=V(()=>p.value.reduce((a,e)=>a+e.supplierPrice*e.quantity,0)),S=V(()=>p.value.reduce((a,e)=>a+e.salePrice*e.quantity,0));V(()=>p.value.reduce((a,e)=>a+e.salePrice*e.quantity,0)),V(()=>y.value===0?0:(S.value-y.value)/y.value*100);const L=V(()=>p.value.length===0?0:p.value.reduce((e,u)=>e+u.profitRate,0)/p.value.length),ee=V(()=>p.value.length>0),k=a=>{a.profitRate=a.supplierPrice>0?(a.salePrice-a.supplierPrice)/a.supplierPrice*100:0,a.totalPrice=a.salePrice*a.quantity},N=a=>a<10?"profit-low":a<20?"profit-normal":"profit-high",te=()=>n.type==="salePrice"?b.value==="batch"?"销售总价":"销售价":"利润率",B=a=>{const{type:e,value:u}=n;return e==="salePrice"?u:e==="profitRate"?a.supplierPrice*(1+u/100):a.salePrice},Q=a=>{const e=B(a);return a.supplierPrice>0?(e-a.supplierPrice)/a.supplierPrice*100:0},le=a=>B(a)*a.quantity,$=()=>{const{type:a,value:e}=n;return a==="salePrice"?e:a==="profitRate"?p.value.reduce((u,c)=>{const d=c.supplierPrice*(1+e/100);return u+d*c.quantity},0):S.value},K=()=>{if(p.value.length===0)return 0;const{type:a,value:e}=n;if(a==="salePrice"){const u=e,c=S.value;if(c===0)return 0;let d=0;return p.value.forEach(g=>{const T=g.salePrice*g.quantity/c,F=u*T/g.quantity,v=g.supplierPrice>0?(F-g.supplierPrice)/g.supplierPrice*100:0;d+=v}),d/p.value.length}else if(a==="profitRate")return e;return L.value},A=()=>{const a=S.value-y.value;return $()-y.value-a},z=()=>{U.value=!0,o.pageNum=1,setTimeout(()=>{j.value=M.map(a=>{const e={...a};return k(e),e}),I.value=M.length,U.value=!1},300)},ae=()=>{o.customer="",o.model="",o.brand="",o.category="",o.rfqNo="",z()},ie=a=>{p.value=a},se=a=>{k(a),w.success("销售价已更新")},oe=()=>{w.info("正在下载模板文件...")},ne=()=>{Y.confirm("确定要重置所有调整吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{j.value.forEach(a=>{k(a)}),w.success("已重置")})},re=()=>{if(p.value.length===0){w.warning("请选择要确认的数据");return}Y.confirm(`确定要确认选中的 ${p.value.length} 条数据吗？`,"确认调整",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(()=>{w.success("报价调整已确认")})},ue=a=>{f.value=a,b.value="single",n.scope="current",n.type="salePrice",n.value=0,R.value=!0},ce=()=>{if(p.value.length===0){w.warning("请先选择要调整的数据");return}f.value=null,b.value="batch",n.scope="selected",n.type="salePrice",n.value=0,R.value=!0},pe=()=>{R.value=!1,b.value="",f.value=null},de=()=>{const{type:a,value:e}=n;let u=[];if(b.value==="single"?u=[f.value]:b.value==="batch"&&(u=p.value),u.length===0){w.warning("没有可调整的数据");return}if(b.value==="batch"&&a==="salePrice"){const c=e,d=S.value;if(d===0){w.warning("当前销售总价为0，无法进行等比例调整");return}u.forEach(g=>{const T=g.salePrice*g.quantity/d,P=c*T;g.salePrice=P/g.quantity,k(g)})}else u.forEach(c=>{a==="salePrice"?c.salePrice=e:a==="profitRate"&&(c.salePrice=c.supplierPrice*(1+e/100)),k(c)});R.value=!1,b.value="",w.success(`已调整 ${u.length} 条数据`)},ve=a=>{o.pageSize=a,z()},me=a=>{o.pageNum=a,z()};return Se(()=>{z()}),(a,e)=>{const u=m("el-input"),c=m("el-form-item"),d=m("el-button"),g=m("el-form"),E=m("el-button-group"),T=m("el-statistic"),P=m("el-col"),F=m("el-row"),v=m("el-table-column"),G=m("el-input-number"),fe=m("el-table"),ge=m("el-pagination"),_e=m("el-divider"),H=m("el-radio"),be=m("el-radio-group"),Pe=m("el-dialog"),ye=m("el-card"),he=qe("loading");return h(),Te(ye,{class:"rfq-adjust-container"},{default:i(()=>[t("div",ke,[l(g,{inline:!0,model:o,class:"demo-form-inline"},{default:i(()=>[l(c,{label:"客户"},{default:i(()=>[l(u,{modelValue:o.customer,"onUpdate:modelValue":e[0]||(e[0]=s=>o.customer=s),placeholder:"请输入客户名称",clearable:""},null,8,["modelValue"])]),_:1}),l(c,{label:"物料型号"},{default:i(()=>[l(u,{modelValue:o.model,"onUpdate:modelValue":e[1]||(e[1]=s=>o.model=s),placeholder:"请输入物料型号",clearable:""},null,8,["modelValue"])]),_:1}),l(c,{label:"品牌"},{default:i(()=>[l(u,{modelValue:o.brand,"onUpdate:modelValue":e[2]||(e[2]=s=>o.brand=s),placeholder:"请输入品牌",clearable:""},null,8,["modelValue"])]),_:1}),l(c,{label:"物料分类"},{default:i(()=>[l(u,{modelValue:o.category,"onUpdate:modelValue":e[3]||(e[3]=s=>o.category=s),placeholder:"请输入物料分类",clearable:""},null,8,["modelValue"])]),_:1}),l(c,{label:"询价单号"},{default:i(()=>[l(u,{modelValue:o.rfqNo,"onUpdate:modelValue":e[4]||(e[4]=s=>o.rfqNo=s),placeholder:"请输入询价单号",clearable:""},null,8,["modelValue"])]),_:1}),l(c,null,{default:i(()=>[l(d,{type:"primary",onClick:z},{default:i(()=>e[10]||(e[10]=[_("查询")])),_:1}),l(d,{onClick:ae},{default:i(()=>e[11]||(e[11]=[_("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),t("div",Ne,[t("div",ze,[l(E,null,{default:i(()=>[l(d,{type:"primary",icon:O(xe)},{default:i(()=>e[12]||(e[12]=[_("导入报价单")])),_:1},8,["icon"]),l(d,{type:"primary",onClick:oe,icon:O(Ve)},null,8,["icon"])]),_:1}),l(d,{type:"primary",onClick:ce},{default:i(()=>e[13]||(e[13]=[_("批量调整")])),_:1})]),t("div",Fe,[l(d,{onClick:ne},{default:i(()=>e[14]||(e[14]=[_("重置")])),_:1}),l(d,{type:"primary",onClick:re,disabled:!ee.value},{default:i(()=>e[15]||(e[15]=[_("确定")])),_:1},8,["disabled"])])]),t("div",Le,[l(F,{gutter:20},{default:i(()=>[l(P,{span:6},{default:i(()=>[l(T,{title:"选中条数",value:Z.value},null,8,["value"])]),_:1}),l(P,{span:6},{default:i(()=>[l(T,{title:"供应商报价总计",value:y.value,precision:2,prefix:"¥"},null,8,["value"])]),_:1}),l(P,{span:6},{default:i(()=>[l(T,{title:"销售价总计",value:S.value,precision:2,prefix:"¥"},null,8,["value"])]),_:1}),l(P,{span:6},{default:i(()=>[l(T,{title:"平均利润率",value:L.value,precision:2,suffix:"%"},null,8,["value"])]),_:1})]),_:1})]),Ce((h(),C("div",Ue,[l(fe,{data:j.value,border:"",onSelectionChange:ie,style:{width:"100%"}},{default:i(()=>[l(v,{type:"selection",width:"55",fixed:"left"}),l(v,{prop:"customer",label:"客户",width:"120"}),l(v,{prop:"productName",label:"物料名称",width:"150","show-overflow-tooltip":""}),l(v,{prop:"model",label:"型号",width:"120"}),l(v,{prop:"brand",label:"品牌",width:"100"}),l(v,{prop:"category",label:"物料分类",width:"120"}),l(v,{prop:"quantity",label:"数量",width:"80",align:"right"}),l(v,{prop:"supplierPrice",label:"供应商报价（¥）",width:"140",align:"right"},{default:i(s=>[_(r(s.row.supplierPrice.toLocaleString()),1)]),_:1}),l(v,{prop:"deliveryTime",label:"交期",width:"100"}),l(v,{prop:"salePrice",label:"销售价（¥）",width:"130",align:"right"},{default:i(s=>[l(G,{modelValue:s.row.salePrice,"onUpdate:modelValue":D=>s.row.salePrice=D,min:0,precision:2,size:"small",onChange:D=>se(s.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(v,{prop:"profitRate",label:"利润率",width:"100",align:"right"},{default:i(s=>[t("span",{class:q(N(s.row.profitRate))},r(s.row.profitRate.toFixed(2))+"% ",3)]),_:1}),l(v,{prop:"totalPrice",label:"总价",width:"120",align:"right"},{default:i(s=>[_(" ¥"+r(s.row.totalPrice.toLocaleString()),1)]),_:1}),l(v,{prop:"rfqNo",label:"询价单号",width:"150"}),l(v,{prop:"rfqTime",label:"询价时间",width:"160"}),l(v,{prop:"updateTime",label:"更新时间",width:"160"}),l(v,{label:"操作",width:"150",fixed:"right"},{default:i(s=>[l(d,{link:"",type:"primary",size:"small",onClick:D=>ue(s.row)},{default:i(()=>e[16]||(e[16]=[_(" 调整 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),t("div",je,[l(ge,{"current-page":o.pageNum,"onUpdate:currentPage":e[5]||(e[5]=s=>o.pageNum=s),"page-size":o.pageSize,"onUpdate:pageSize":e[6]||(e[6]=s=>o.pageSize=s),"page-sizes":[10,20,50,100],total:I.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ve,onCurrentChange:me},null,8,["current-page","page-size","total"])])])),[[he,U.value]]),l(Pe,{modelValue:R.value,"onUpdate:modelValue":e[9]||(e[9]=s=>R.value=s),title:b.value==="single"?"单行价格调整":"批量价格调整",width:"800px"},{footer:i(()=>[t("span",yt,[l(d,{onClick:pe},{default:i(()=>e[39]||(e[39]=[_("取消")])),_:1}),l(d,{type:"primary",onClick:de},{default:i(()=>e[40]||(e[40]=[_("确定")])),_:1})])]),default:i(()=>[t("div",Be,[b.value==="single"?(h(),C("div",Ae,[e[27]||(e[27]=t("h4",{style:{"margin-bottom":"16px",color:"#333","text-align":"center"}},"价格调整对比",-1)),l(F,{gutter:24},{default:i(()=>[l(P,{span:12},{default:i(()=>[t("div",Ee,[e[21]||(e[21]=t("h5",{class:"section-title"},"当前价格",-1)),t("div",De,[e[17]||(e[17]=t("span",{class:"price-label"},"供应商报价:",-1)),t("span",Ie,"¥"+r(f.value.supplierPrice.toLocaleString()),1)]),t("div",Me,[e[18]||(e[18]=t("span",{class:"price-label"},"销售价:",-1)),t("span",Qe,"¥"+r(f.value.salePrice.toLocaleString()),1)]),t("div",$e,[e[19]||(e[19]=t("span",{class:"price-label"},"利润率:",-1)),t("span",{class:q(["price-value",N(f.value.profitRate)])},r(f.value.profitRate.toFixed(2))+"% ",3)]),t("div",Ke,[e[20]||(e[20]=t("span",{class:"price-label"},"总价:",-1)),t("span",Ge,"¥"+r((f.value.salePrice*f.value.quantity).toLocaleString()),1)])])]),_:1}),l(P,{span:12},{default:i(()=>[t("div",{class:q(["preview-price-section",{"preview-active":n.value!==0}])},[e[26]||(e[26]=t("h5",{class:"section-title"},"调整后预览",-1)),n.value!==0?(h(),C(X,{key:0},[t("div",He,[e[22]||(e[22]=t("span",{class:"price-label"},"供应商报价:",-1)),t("span",Je,"¥"+r(f.value.supplierPrice.toLocaleString()),1)]),t("div",Oe,[e[23]||(e[23]=t("span",{class:"price-label"},"销售价:",-1)),t("span",We,"¥"+r(B(f.value).toLocaleString()),1)]),t("div",Xe,[e[24]||(e[24]=t("span",{class:"price-label"},"利润率:",-1)),t("span",{class:q(["price-value preview-value",N(Q(f.value))])},r(Q(f.value).toFixed(2))+"% ",3)]),t("div",Ye,[e[25]||(e[25]=t("span",{class:"price-label"},"总价:",-1)),t("span",Ze,"¥"+r(le(f.value).toLocaleString()),1)])],64)):(h(),C("div",et," 请设置调整值查看预览 "))],2)]),_:1})]),_:1})])):b.value==="batch"?(h(),C("div",tt,[t("h4",lt,"批量调整对比 ("+r(p.value.length)+" 条记录)",1),l(F,{gutter:24},{default:i(()=>[l(P,{span:12},{default:i(()=>[t("div",at,[e[32]||(e[32]=t("h5",{class:"section-title"},"当前汇总",-1)),t("div",it,[e[28]||(e[28]=t("span",{class:"price-label"},"供应商报价总计:",-1)),t("span",st,"¥"+r(y.value.toLocaleString()),1)]),t("div",ot,[e[29]||(e[29]=t("span",{class:"price-label"},"销售价总计:",-1)),t("span",nt,"¥"+r(S.value.toLocaleString()),1)]),t("div",rt,[e[30]||(e[30]=t("span",{class:"price-label"},"平均利润率:",-1)),t("span",{class:q(["price-value",N(L.value)])},r(L.value.toFixed(2))+"% ",3)]),t("div",ut,[e[31]||(e[31]=t("span",{class:"price-label"},"总利润:",-1)),t("span",ct,"¥"+r((S.value-y.value).toLocaleString()),1)])])]),_:1}),l(P,{span:12},{default:i(()=>[t("div",{class:q(["preview-price-section",{"preview-active":n.value!==0}])},[e[37]||(e[37]=t("h5",{class:"section-title"},"调整后预览",-1)),n.value!==0?(h(),C(X,{key:0},[t("div",pt,[e[33]||(e[33]=t("span",{class:"price-label"},"供应商报价总计:",-1)),t("span",dt,"¥"+r(y.value.toLocaleString()),1)]),t("div",vt,[e[34]||(e[34]=t("span",{class:"price-label"},"销售价总计:",-1)),t("span",mt,"¥"+r($().toLocaleString()),1)]),t("div",ft,[e[35]||(e[35]=t("span",{class:"price-label"},"平均利润率:",-1)),t("span",{class:q(["price-value preview-value",N(K())])},r(K().toFixed(2))+"% ",3)]),t("div",gt,[e[36]||(e[36]=t("span",{class:"price-label"},"利润变化:",-1)),t("span",{class:"price-value preview-value",style:Re({color:A()>=0?"#67c23a":"#f56c6c"})},r(A()>=0?"+":"")+"¥"+r(A().toLocaleString()),5)])],64)):(h(),C("div",_t," 请设置调整值查看预览 "))],2)]),_:1})]),_:1})])):W("",!0)]),l(_e),l(g,{model:n,"label-width":"120px"},{default:i(()=>[l(c,{label:"调整方式"},{default:i(()=>[l(be,{modelValue:n.type,"onUpdate:modelValue":e[7]||(e[7]=s=>n.type=s)},{default:i(()=>[l(H,{label:"salePrice"},{default:i(()=>[_(r(n.type==="batch"?"销售总价":"销售价"),1)]),_:1}),l(H,{label:"profitRate"},{default:i(()=>e[38]||(e[38]=[_("按利润率")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l(c,{label:te()},{default:i(()=>[l(G,{modelValue:n.value,"onUpdate:modelValue":e[8]||(e[8]=s=>n.value=s),min:0,precision:2},null,8,["modelValue"]),t("span",bt,r(n.type==="profitRate"?"%":"元"),1)]),_:1},8,["label"])]),_:1},8,["model"]),n.type==="salePrice"&&b.value==="batch"?(h(),C("div",Pt," ⚠️ 按销售总价批量调整时，销售总价的变化将等比例分摊到每个选中的物料上。 ")):W("",!0)]),_:1},8,["modelValue","title"])]),_:1})}}},Tt=we(ht,[["__scopeId","data-v-36c659b3"]]);export{Tt as default};
