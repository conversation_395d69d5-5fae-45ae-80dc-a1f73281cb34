import{_ as le,d as i,l as M,f as ae,m as N,o as u,w as a,a as y,g as te,b as e,r as s,c as f,j as w,F as T,i as p,h as ne,t as _,v as oe,E as z}from"./index-Di-G_xQb.js";const re={class:"filter-container"},se={class:"table-container"},ie={class:"table-header-container"},ue={class:"table-actions"},de={class:"material-table-container"},pe={key:0},ce={class:"pagination-container"},me={__name:"index",setup(ve){const C=i(!1),b=i([]),k=i([]),D=i(0),g=i(!1),d=M({pageNum:1,pageSize:10}),r=M({deliveryNo:"",customer:"",supplier:"",status:""}),E=i([{value:"customer1",label:"北京科技有限公司"},{value:"customer2",label:"上海制造企业"},{value:"customer3",label:"深圳电子公司"},{value:"customer4",label:"广州贸易公司"}]),U=i([{value:"supplier1",label:"华为技术有限公司"},{value:"supplier2",label:"小米科技有限公司"},{value:"supplier3",label:"联想集团有限公司"},{value:"supplier4",label:"海康威视股份有限公司"}]),Q=i([{value:"pending",label:"待发货"},{value:"shipped",label:"已发货"},{value:"delivered",label:"已送达"},{value:"received",label:"已收货"},{value:"cancelled",label:"已取消"}]),h=i([{id:1,deliveryNo:"DN202401001",customer:"北京科技有限公司",supplier:"华为技术有限公司",status:"delivered",totalQuantity:150,logisticsCount:2,createTime:"2024-01-15 09:30:00",receiveTime:"2024-01-17 14:20:00",materials:[{materialName:"华为路由器",model:"AR2220",brand:"华为",quantity:50,purchaseOrder:"PO202401001",salesOrder:"SO202401001"},{materialName:"交换机",model:"S5720-28P-SI",brand:"华为",quantity:100,purchaseOrder:"PO202401002",salesOrder:"SO202401001"}]},{id:2,deliveryNo:"DN202401002",customer:"上海制造企业",supplier:"小米科技有限公司",status:"shipped",totalQuantity:200,logisticsCount:1,createTime:"2024-01-16 10:15:00",receiveTime:null,materials:[{materialName:"小米手机",model:"Mi 14",brand:"小米",quantity:100,purchaseOrder:"PO202401003",salesOrder:"SO202401002"},{materialName:"小米平板",model:"Pad 6",brand:"小米",quantity:100,purchaseOrder:"PO202401004",salesOrder:"SO202401002"}]},{id:3,deliveryNo:"DN202401003",customer:"深圳电子公司",supplier:"联想集团有限公司",status:"pending",totalQuantity:75,logisticsCount:3,createTime:"2024-01-17 11:45:00",receiveTime:null,materials:[{materialName:"ThinkPad笔记本",model:"X1 Carbon",brand:"联想",quantity:25,purchaseOrder:"PO202401005",salesOrder:"SO202401003"},{materialName:"联想显示器",model:"L24q-30",brand:"联想",quantity:50,purchaseOrder:"PO202401006",salesOrder:"SO202401003"}]}]);D.value=h.value.length;const O=()=>{C.value=!0,console.log("Fetching delivery list with params:",JSON.parse(JSON.stringify(d))),setTimeout(()=>{console.log("Data fetching completed"),C.value=!1},300)},P=()=>{d.pageNum=1,O()},$=()=>{r.deliveryNo="",r.customer="",r.supplier="",r.status="",k.value=[],P()},B=(n,t)=>{console.log("展开行变化:",n,t),b.value=t.map(S=>S.id),g.value=b.value.length===h.value.length},F=n=>{d.pageSize=n,O()},Y=n=>{d.pageNum=n,O()},A=n=>({pending:"warning",shipped:"primary",delivered:"success",received:"success",cancelled:"danger"})[n]||"info",L=n=>({pending:"待发货",shipped:"已发货",delivered:"已送达",received:"已收货",cancelled:"已取消"})[n]||"未知",j=n=>{z.success(`跳转到送货单详情: ${n}`)},I=n=>{z.success(`跳转到采购单详情: ${n}`)},J=n=>{z.success(`跳转到订单详情: ${n}`)};ae(()=>{O()});const R=()=>{g.value=!g.value,g.value?b.value=h.value.map(n=>n.id):b.value=[]};return(n,t)=>{const S=s("el-input"),c=s("el-form-item"),x=s("el-option"),V=s("el-select"),X=s("el-date-picker"),m=s("el-button"),G=s("el-form"),o=s("el-table-column"),q=s("el-table"),H=s("el-tag"),K=s("el-pagination"),W=s("el-card"),Z=ne("loading");return u(),N(W,{class:"delivery-note-container"},{default:a(()=>[y("div",re,[e(G,{inline:!0,model:r,class:"demo-form-inline"},{default:a(()=>[e(c,{label:"送货单号"},{default:a(()=>[e(S,{modelValue:r.deliveryNo,"onUpdate:modelValue":t[0]||(t[0]=l=>r.deliveryNo=l),placeholder:"请输入送货单号",clearable:""},null,8,["modelValue"])]),_:1}),e(c,{label:"客户"},{default:a(()=>[e(V,{modelValue:r.customer,"onUpdate:modelValue":t[1]||(t[1]=l=>r.customer=l),placeholder:"请选择客户",clearable:"",filterable:""},{default:a(()=>[(u(!0),f(T,null,w(E.value,l=>(u(),N(x,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"供应商"},{default:a(()=>[e(V,{modelValue:r.supplier,"onUpdate:modelValue":t[2]||(t[2]=l=>r.supplier=l),placeholder:"请选择供应商",clearable:"",filterable:""},{default:a(()=>[(u(!0),f(T,null,w(U.value,l=>(u(),N(x,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"状态"},{default:a(()=>[e(V,{modelValue:r.status,"onUpdate:modelValue":t[3]||(t[3]=l=>r.status=l),placeholder:"请选择状态",clearable:""},{default:a(()=>[(u(!0),f(T,null,w(Q.value,l=>(u(),N(x,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"创建时间"},{default:a(()=>[e(X,{modelValue:k.value,"onUpdate:modelValue":t[4]||(t[4]=l=>k.value=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(c,null,{default:a(()=>[e(m,{type:"primary",onClick:P},{default:a(()=>t[7]||(t[7]=[p("查询")])),_:1}),e(m,{onClick:$},{default:a(()=>t[8]||(t[8]=[p("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),te((u(),f("div",se,[y("div",ie,[y("div",ue,[e(m,{type:"primary",onClick:R},{default:a(()=>[p(_(g.value?"全部折叠":"全部展开"),1)]),_:1})])]),e(q,{data:h.value,"row-key":"id","expand-row-keys":b.value,onExpandChange:B,class:"delivery-table",stripe:"",border:""},{default:a(()=>[e(o,{type:"expand",width:"50",fixed:"left"},{default:a(({row:l})=>[y("div",de,[e(q,{data:l.materials,class:"material-table",size:"small",border:""},{default:a(()=>[e(o,{prop:"materialName",label:"物料名称","min-width":"120"}),e(o,{prop:"model",label:"型号","min-width":"100"}),e(o,{prop:"brand",label:"品牌","min-width":"100"}),e(o,{prop:"quantity",label:"数量",width:"80",align:"center"}),e(o,{prop:"purchaseOrder",label:"所属采购单","min-width":"120"},{default:a(({row:v})=>[e(m,{type:"text",onClick:ee=>I(v.purchaseOrder)},{default:a(()=>[p(_(v.purchaseOrder),1)]),_:2},1032,["onClick"])]),_:2},1024),e(o,{prop:"salesOrder",label:"所属订单","min-width":"120"},{default:a(({row:v})=>[e(m,{type:"text",onClick:ee=>J(v.salesOrder)},{default:a(()=>[p(_(v.salesOrder),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data"])])]),_:1}),e(o,{prop:"deliveryNo",label:"送货单号","min-width":"140"},{default:a(({row:l})=>[e(m,{type:"text",onClick:v=>j(l.deliveryNo)},{default:a(()=>[p(_(l.deliveryNo),1)]),_:2},1032,["onClick"])]),_:1}),e(o,{prop:"customer",label:"客户","min-width":"120"}),e(o,{prop:"supplier",label:"供应商","min-width":"120"}),e(o,{prop:"status",label:"状态",width:"100",align:"center"},{default:a(({row:l})=>[e(H,{type:A(l.status)},{default:a(()=>[p(_(L(l.status)),1)]),_:2},1032,["type"])]),_:1}),e(o,{prop:"totalQuantity",label:"物料总数量",width:"120",align:"center"}),e(o,{prop:"logisticsCount",label:"物流单数",width:"100",align:"center"}),e(o,{prop:"createTime",label:"创建时间",width:"160"}),e(o,{prop:"receiveTime",label:"收货时间",width:"160"},{default:a(({row:l})=>[l.receiveTime?(u(),f("span",pe,_(l.receiveTime),1)):oe("",!0)]),_:1})]),_:1},8,["data","expand-row-keys"]),y("div",ce,[e(K,{"current-page":d.pageNum,"onUpdate:currentPage":t[5]||(t[5]=l=>d.pageNum=l),"page-size":d.pageSize,"onUpdate:pageSize":t[6]||(t[6]=l=>d.pageSize=l),"page-sizes":[10,20,50,100],total:D.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:F,onCurrentChange:Y},null,8,["current-page","page-size","total"])])])),[[Z,C.value]])]),_:1})}}},be=le(me,[["__scopeId","data-v-ea7d1277"]]);export{be as default};
