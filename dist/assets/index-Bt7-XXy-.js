import{_ as J,y as pe,d as g,l as I,e as G,r as o,c as _,o as c,a as Q,b as e,w as t,i as m,t as P,u as te,z as ae,F as N,j as A,m as T,v as O,A as H,B as ce,f as me,g as ge,h as ye}from"./index-Di-G_xQb.js";const fe={style:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},be={style:{padding:"0 20px"}},_e={style:{padding:"10px 20px"}},ve={class:"pagination-container",style:{"margin-top":"15px"}},Se={__name:"OrderView",setup(X){const n=pe(),C=g(!1),v=g(null),y=g(!1),q=g([]),w=g(1),V=g(10),M=g(0),W=i=>i==null?"0.00":i.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),r=i=>i+"%",S=I([{prop:"orderNo",label:"销售订单号",visible:!0,minWidth:180},{prop:"customerName",label:"客户名称",visible:!0,minWidth:150},{prop:"status",label:"订单状态",visible:!0,minWidth:120},{prop:"productCount",label:"物料数量",visible:!0,minWidth:100},{prop:"totalCost",label:"总成本",visible:!0,minWidth:120},{prop:"totalAmount",label:"总金额",visible:!0,minWidth:120},{prop:"profitMargin",label:"利润率",visible:!0,minWidth:100},{prop:"createTime",label:"下单时间",visible:!0,minWidth:150}]),f=I([{prop:"productName",label:"物料名称",visible:!0,minWidth:150},{prop:"model",label:"型号",visible:!0,minWidth:120},{prop:"brand",label:"品牌",visible:!0,minWidth:100},{prop:"category",label:"分类",visible:!0,minWidth:100},{prop:"quantity",label:"数量",visible:!0,minWidth:80},{prop:"supplier",label:"供应商",visible:!0,minWidth:150},{prop:"shippedQuantity",label:"已发货数量",visible:!0,minWidth:120},{prop:"receivedQuantity",label:"已收货数量",visible:!0,minWidth:120},{prop:"cancelledQuantity",label:"已取消数量",visible:!0,minWidth:120},{prop:"supplierQuote",label:"供应商报价",visible:!0,minWidth:120},{prop:"unitPrice",label:"销售价",visible:!0,minWidth:100},{prop:"totalPrice",label:"总价",visible:!0,minWidth:100},{prop:"logisticsStatus",label:"物流状态",visible:!0,minWidth:120},{prop:"financialStatus",label:"财务状态",visible:!0,minWidth:120}]),h=G(()=>S.filter(i=>i.visible)),z=G(()=>f.filter(i=>i.visible)),k=g([{id:"sale1",orderNo:"SO20240501001",customerName:"广州家电有限公司",status:"pending_confirmation",productCount:3,totalAmount:187500,totalCost:15e4,profitMargin:20,paymentProgress:30,createTime:"2024-05-01 10:30:00",products:[{id:"sp1-1",productName:"智能控制器",model:"CT-X100",brand:"SelecTech",category:"控制设备",quantity:50,supplier:"东莞电子科技有限公司",supplierQuote:2e3,shippingCost:200,shippedQuantity:5,receivedQuantity:10,cancelledQuantity:0,unitPrice:2500,totalPrice:125e3,logisticsStatus:"部分发货",financialStatus:"部分付款"},{id:"sp1-2",productName:"传感器模块",model:"SM-500",brand:"SelecTech",category:"传感设备",quantity:25,supplier:"深圳传感技术有限公司",supplierQuote:2200,shippingCost:150,shippedQuantity:2,receivedQuantity:5,cancelledQuantity:0,unitPrice:2500,totalPrice:62500,logisticsStatus:"部分发货",financialStatus:"部分付款"}]},{id:"sale2",orderNo:"SO20240428002",customerName:"北京智能家居科技有限公司",status:"pending_supplier_confirmation",productCount:1,totalAmount:8e4,totalCost:65e3,profitMargin:18.8,paymentProgress:100,createTime:"2024-04-28 14:20:00",products:[{id:"sp2-1",productName:"中央处理单元",model:"CPU-S2000",brand:"SelecTech",category:"处理设备",quantity:20,supplier:"上海芯片制造有限公司",supplierQuote:3200,shippingCost:250,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,unitPrice:4e3,totalPrice:8e4,logisticsStatus:"备货中",financialStatus:"已付款"}]},{id:"sale3",orderNo:"SO20240420003",customerName:"上海机械设备有限公司",status:"in_progress",productCount:2,totalAmount:135e3,totalCost:108e3,profitMargin:20,paymentProgress:50,createTime:"2024-04-20 09:15:00",products:[{id:"sp3-1",productName:"工业控制器",model:"IC-800",brand:"SelecTech",category:"控制设备",quantity:15,supplier:"东莞电子科技有限公司",supplierQuote:4800,shippingCost:300,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,unitPrice:6e3,totalPrice:9e4,logisticsStatus:"运输中",financialStatus:"已付款"},{id:"sp3-2",productName:"工业传感器",model:"IS-300",brand:"SelecTech",category:"传感设备",quantity:15,supplier:"深圳传感技术有限公司",supplierQuote:2400,shippingCost:180,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,unitPrice:3e3,totalPrice:45e3,logisticsStatus:"运输中",financialStatus:"已付款"}]},{id:"sale4",orderNo:"SO20240410004",customerName:"深圳智能制造有限公司",status:"completed",productCount:1,totalAmount:55e3,totalCost:44e3,profitMargin:20,paymentProgress:100,createTime:"2024-04-10 16:45:00",products:[{id:"sp4-1",productName:"自动化控制系统",model:"ACS-500",brand:"SelecTech",category:"控制系统",quantity:5,supplier:"东莞电子科技有限公司",supplierQuote:8500,shippingCost:300,shippedQuantity:5,receivedQuantity:5,cancelledQuantity:0,unitPrice:11e3,totalPrice:55e3,logisticsStatus:"已签收",financialStatus:"已付款"}]},{id:"sale5",orderNo:"SO20240405005",customerName:"广州机电工程有限公司",status:"cancelled",productCount:1,totalAmount:42e3,totalCost:33600,profitMargin:20,paymentProgress:0,createTime:"2024-04-05 11:30:00",products:[{id:"sp5-1",productName:"工业传感器阵列",model:"ISA-200",brand:"SelecTech",category:"传感设备",quantity:6,supplier:"深圳传感技术有限公司",supplierQuote:5400,shippingCost:200,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:6,unitPrice:7e3,totalPrice:42e3,logisticsStatus:"已取消",financialStatus:"已退款"}]},{id:"sale6",orderNo:"SO20240402006",customerName:"天津电子科技有限公司",status:"cancelled",productCount:2,totalAmount:12e4,totalCost:96e3,profitMargin:20,paymentProgress:30,createTime:"2024-04-02 13:20:00",products:[{id:"sp6-1",productName:"数据处理单元",model:"DPU-A100",brand:"SelecTech",category:"处理设备",quantity:10,supplier:"上海芯片制造有限公司",supplierQuote:6200,shippingCost:200,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,unitPrice:8e3,totalPrice:8e4,logisticsStatus:"待发货",financialStatus:"退款中"},{id:"sp6-2",productName:"信号放大器",model:"SA-50",brand:"SelecTech",category:"信号设备",quantity:20,supplier:"深圳传感技术有限公司",supplierQuote:1600,shippingCost:100,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,unitPrice:2e3,totalPrice:4e4,logisticsStatus:"待发货",financialStatus:"退款中"}]},{id:"sale7",orderNo:"SO20240328007",customerName:"重庆智能设备有限公司",status:"pending_confirmation",productCount:1,totalAmount:5e4,totalCost:4e4,profitMargin:20,paymentProgress:80,createTime:"2024-03-28 15:10:00",products:[{id:"sp7-1",productName:"高性能控制器",model:"HPC-800",brand:"SelecTech",category:"控制设备",quantity:5,supplier:"东莞电子科技有限公司",supplierQuote:7800,shippingCost:200,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,unitPrice:1e4,totalPrice:5e4,logisticsStatus:"待发货",financialStatus:"待处理"}]},{id:"sale8",orderNo:"SO20240320008",customerName:"北京自动化设备有限公司",status:"completed",productCount:3,totalAmount:18e4,totalCost:144e3,profitMargin:20,paymentProgress:100,createTime:"2024-03-20 08:45:00",products:[{id:"sp8-1",productName:"自动化生产线控制系统",model:"APLCS-2000",brand:"SelecTech",category:"控制系统",quantity:1,supplier:"东莞电子科技有限公司",supplierQuote:8e4,shippingCost:500,shippedQuantity:1,receivedQuantity:1,cancelledQuantity:0,unitPrice:1e5,totalPrice:1e5,logisticsStatus:"已归档",financialStatus:"已归档"},{id:"sp8-2",productName:"环境传感器组",model:"ESG-100",brand:"SelecTech",category:"传感设备",quantity:10,supplier:"深圳传感技术有限公司",supplierQuote:2400,shippingCost:150,shippedQuantity:10,receivedQuantity:10,cancelledQuantity:0,unitPrice:3e3,totalPrice:3e4,logisticsStatus:"已归档",financialStatus:"已归档"},{id:"sp8-3",productName:"数据采集终端",model:"DAT-5000",brand:"SelecTech",category:"采集设备",quantity:5,supplier:"上海芯片制造有限公司",supplierQuote:8e3,shippingCost:250,shippedQuantity:5,receivedQuantity:5,cancelledQuantity:0,unitPrice:1e4,totalPrice:5e4,logisticsStatus:"已归档",financialStatus:"已归档"}]}]),u=(i,a)=>{if(console.log("Expanded order:",i,"All expanded rows:",a),v.value){const b=k.value.filter(Y=>v.value.store.states.expandedRows.value.includes(Y));y.value=b.length===k.value.length}},d=()=>{v.value&&(y.value=!y.value,k.value.forEach(i=>{v.value.toggleRowExpansion(i,y.value)}))},x=i=>{q.value=i,console.log("Selected orders:",i)},U=i=>{i==="confirm"?console.log("Batch confirm clicked"):i==="ship"&&console.log("Batch ship clicked")},D=i=>{console.log("View order details:",i),n.push({name:"SODetail",params:{id:i.id}})},$=i=>{console.log("Confirm order:",i)},p=i=>["pending_supplier_confirmation","in_progress","completed"].includes(i.status),R=i=>{console.log("Download contract for order:",i)},j=i=>{V.value=i,console.log("Page size changed to:",i)},E=i=>{w.value=i,console.log("Current page changed to:",i)},F=i=>({pending_confirmation:"待确认",pending_supplier_confirmation:"待供应商确认",in_progress:"执行中",completed:"已完成",cancelled:"已取消"})[i]||i,L=i=>({pending_confirmation:"info",pending_supplier_confirmation:"warning",in_progress:"primary",completed:"success",cancelled:"danger"})[i]||"";return(i,a)=>{const b=o("el-button"),Y=o("el-icon"),ie=o("el-dropdown-item"),le=o("el-dropdown-menu"),ne=o("el-dropdown"),K=o("el-checkbox"),oe=o("el-divider"),re=o("el-drawer"),B=o("el-table-column"),Z=o("el-table"),ue=o("el-progress"),se=o("el-tag"),de=o("el-pagination");return c(),_("div",null,[Q("div",fe,[Q("div",null,[e(b,{onClick:d,type:"primary",style:{"margin-right":"10px"}},{default:t(()=>[m(P(y.value?"全部收起":"全部展开"),1)]),_:1}),e(ne,{onCommand:U},{dropdown:t(()=>[e(le,null,{default:t(()=>[e(ie,{command:"confirm"},{default:t(()=>a[5]||(a[5]=[m("确认")])),_:1})]),_:1})]),default:t(()=>[e(b,{type:"primary"},{default:t(()=>[a[4]||(a[4]=m(" 批量操作")),e(Y,{class:"el-icon--right"},{default:t(()=>[e(te(ae))]),_:1})]),_:1})]),_:1})]),e(b,{onClick:a[0]||(a[0]=l=>C.value=!0),type:"primary"},{default:t(()=>a[6]||(a[6]=[m("配置列")])),_:1})]),e(re,{title:"配置列",modelValue:C.value,"onUpdate:modelValue":a[1]||(a[1]=l=>C.value=l),direction:"rtl",size:"300px"},{default:t(()=>[Q("div",be,[a[7]||(a[7]=Q("h4",null,"订单列",-1)),(c(!0),_(N,null,A(S,l=>(c(),_("div",{key:l.prop},[e(K,{modelValue:l.visible,"onUpdate:modelValue":s=>l.visible=s},{default:t(()=>[m(P(l.label),1)]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),128)),e(oe),a[8]||(a[8]=Q("h4",null,"物料列",-1)),(c(!0),_(N,null,A(f,l=>(c(),_("div",{key:l.prop},[e(K,{modelValue:l.visible,"onUpdate:modelValue":s=>l.visible=s},{default:t(()=>[m(P(l.label),1)]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),128))])]),_:1},8,["modelValue"]),e(Z,{ref_key:"orderTableRef",ref:v,data:k.value,style:{width:"100%"},border:"","row-key":"id",onExpandChange:u,onSelectionChange:x},{default:t(()=>[e(B,{type:"selection",width:"55"}),e(B,{type:"expand"},{default:t(l=>[Q("div",_e,[e(Z,{data:l.row.products,border:"",style:{width:"100%"}},{default:t(()=>[(c(!0),_(N,null,A(z.value,s=>(c(),_(N,{key:s.prop},[s.visible?(c(),T(B,{key:0,prop:s.prop,label:s.label,"min-width":s.minWidth},H({_:2},[s.prop==="unitPrice"||s.prop==="totalPrice"||s.prop==="supplierQuote"?{name:"default",fn:t(ee=>[m(" ¥"+P(W(ee.row[s.prop])),1)]),key:"0"}:void 0]),1032,["prop","label","min-width"])):O("",!0)],64))),128))]),_:2},1032,["data"])])]),_:1}),(c(!0),_(N,null,A(h.value,l=>(c(),_(N,{key:l.prop},[l.visible?(c(),T(B,{key:0,prop:l.prop,label:l.label,"min-width":l.minWidth},H({_:2},[l.prop==="orderNo"?{name:"default",fn:t(s=>[e(b,{link:"",type:"primary",onClick:ce(ee=>D(s.row),["stop"])},{default:t(()=>[m(P(s.row.orderNo),1)]),_:2},1032,["onClick"])]),key:"0"}:l.prop==="totalAmount"||l.prop==="totalCost"?{name:"default",fn:t(s=>[m(" ¥"+P(W(s.row[l.prop])),1)]),key:"1"}:l.prop==="profitMargin"?{name:"default",fn:t(s=>[m(P(s.row.profitMargin)+"% ",1)]),key:"2"}:l.prop==="paymentProgress"?{name:"default",fn:t(s=>[e(ue,{percentage:s.row.paymentProgress,format:r},null,8,["percentage"])]),key:"3"}:l.prop==="status"?{name:"default",fn:t(s=>[e(se,{type:L(s.row.status)},{default:t(()=>[m(P(F(s.row.status)),1)]),_:2},1032,["type"])]),key:"4"}:void 0]),1032,["prop","label","min-width"])):O("",!0)],64))),128)),e(B,{label:"操作","min-width":"150",fixed:"right"},{default:t(l=>[l.row.status==="pending_confirmation"?(c(),T(b,{key:0,size:"small",link:"",type:"primary",onClick:s=>$(l.row)},{default:t(()=>a[9]||(a[9]=[m("确认")])),_:2},1032,["onClick"])):O("",!0),p(l.row)?(c(),T(b,{key:1,size:"small",link:"",type:"primary",onClick:s=>R(l.row)},{default:t(()=>a[10]||(a[10]=[m("下载合同")])),_:2},1032,["onClick"])):O("",!0)]),_:1})]),_:1},8,["data"]),Q("div",ve,[e(de,{"current-page":w.value,"onUpdate:currentPage":a[2]||(a[2]=l=>w.value=l),"page-size":V.value,"onUpdate:pageSize":a[3]||(a[3]=l=>V.value=l),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:M.value,onSizeChange:j,onCurrentChange:E},null,8,["current-page","page-size","total"])])])}}},he=J(Se,[["__scopeId","data-v-259588f7"]]),Qe={style:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},Pe={style:{padding:"0 20px"}},Ce={class:"pagination-container",style:{"margin-top":"15px"}},we={__name:"ProductView",setup(X){const n=g(!1),C=g([]),v=g(1),y=g(10),q=g(0),w=u=>u==null?"0.00":u.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),V=I([{prop:"productName",label:"物料名称",visible:!0,minWidth:150},{prop:"model",label:"型号",visible:!0,minWidth:120},{prop:"brand",label:"品牌",visible:!0,minWidth:100},{prop:"category",label:"分类",visible:!0,minWidth:100},{prop:"quantity",label:"数量",visible:!0,minWidth:80},{prop:"supplier",label:"供应商",visible:!0,minWidth:150},{prop:"preparingQuantity",label:"备货中数量",visible:!1,minWidth:120},{prop:"inTransitQuantity",label:"在途数量",visible:!1,minWidth:100},{prop:"shippedQuantity",label:"已发货数量",visible:!0,minWidth:120},{prop:"acceptedQuantity",label:"已收货数量",visible:!0,minWidth:120},{prop:"cancelledQuantity",label:"已取消数量",visible:!0,minWidth:120},{prop:"unitPrice",label:"单价",visible:!0,minWidth:100},{prop:"totalPrice",label:"总价",visible:!0,minWidth:100},{prop:"orderNo",label:"所属销售订单",visible:!0,minWidth:150},{prop:"orderStatus",label:"订单状态",visible:!0,minWidth:120},{prop:"logisticsStatus",label:"物流状态",visible:!0,minWidth:120},{prop:"financialStatus",label:"财务状态",visible:!0,minWidth:120},{prop:"customer",label:"客户",visible:!0,minWidth:150}]),M=G(()=>V.filter(u=>u.visible)),W=g([{id:"p1",productName:"智能控制器",model:"CT-X100",quantity:50,preparingQuantity:30,inTransitQuantity:10,acceptedQuantity:10,cancelledQuantity:0,unitPrice:2500,totalPrice:125e3,orderNo:"PO20240501001",orderStatus:"pending_confirmation",logisticsStatus:"待发货",financialStatus:"待付款",customer:"广州家电有限公司"},{id:"p2",productName:"传感器模块",model:"SM-500",quantity:25,preparingQuantity:15,inTransitQuantity:5,acceptedQuantity:5,cancelledQuantity:0,unitPrice:2500,totalPrice:62500,orderNo:"PO20240501001",orderStatus:"pending_confirmation",logisticsStatus:"部分发货",financialStatus:"部分付款",customer:"广州家电有限公司"},{id:"p3",productName:"中央处理单元",model:"CPU-S2000",quantity:20,preparingQuantity:20,inTransitQuantity:0,acceptedQuantity:0,cancelledQuantity:0,unitPrice:4e3,totalPrice:8e4,orderNo:"PO20240428002",orderStatus:"confirming",logisticsStatus:"备货中",financialStatus:"已付款",customer:"北京智能家居科技有限公司"},{id:"p4",productName:"工业控制器",model:"IC-800",quantity:15,preparingQuantity:0,inTransitQuantity:15,acceptedQuantity:0,cancelledQuantity:0,unitPrice:6e3,totalPrice:9e4,orderNo:"PO20240420003",orderStatus:"in_progress",logisticsStatus:"运输中",financialStatus:"已付款",customer:"上海机械设备有限公司"},{id:"p5",productName:"工业传感器",model:"IS-300",quantity:15,preparingQuantity:0,inTransitQuantity:15,acceptedQuantity:0,cancelledQuantity:0,unitPrice:3e3,totalPrice:45e3,orderNo:"PO20240420003",orderStatus:"in_progress",logisticsStatus:"运输中",financialStatus:"已付款",customer:"上海机械设备有限公司"},{id:"p6",productName:"自动化控制系统",model:"ACS-500",quantity:5,preparingQuantity:0,inTransitQuantity:0,acceptedQuantity:5,cancelledQuantity:0,unitPrice:11e3,totalPrice:55e3,orderNo:"PO20240410004",orderStatus:"completed",logisticsStatus:"已签收",financialStatus:"已付款",customer:"深圳智能制造有限公司"},{id:"p7",productName:"工业传感器阵列",model:"ISA-200",quantity:6,preparingQuantity:0,inTransitQuantity:0,acceptedQuantity:0,cancelledQuantity:6,unitPrice:7e3,totalPrice:42e3,orderNo:"PO20240405005",orderStatus:"cancelled",logisticsStatus:"已取消",financialStatus:"已退款",customer:"广州机电工程有限公司"},{id:"p8",productName:"数据处理单元",model:"DPU-A100",quantity:10,preparingQuantity:0,inTransitQuantity:0,acceptedQuantity:0,cancelledQuantity:0,unitPrice:8e3,totalPrice:8e4,orderNo:"PO20240402006",orderStatus:"cancelling",logisticsStatus:"待发货",financialStatus:"退款中",customer:"天津电子科技有限公司"},{id:"p9",productName:"信号放大器",model:"SA-50",quantity:20,preparingQuantity:0,inTransitQuantity:0,acceptedQuantity:0,cancelledQuantity:0,unitPrice:2e3,totalPrice:4e4,orderNo:"PO20240402006",orderStatus:"cancelling",logisticsStatus:"待发货",financialStatus:"退款中",customer:"天津电子科技有限公司"},{id:"p10",productName:"高性能控制器",model:"HPC-800",quantity:5,preparingQuantity:5,inTransitQuantity:0,acceptedQuantity:0,cancelledQuantity:0,unitPrice:1e4,totalPrice:5e4,orderNo:"PO20240328007",orderStatus:"exception_pending",logisticsStatus:"待发货",financialStatus:"待处理",customer:"重庆智能设备有限公司"},{id:"p11",productName:"自动化生产线控制系统",model:"APLCS-2000",quantity:1,preparingQuantity:0,inTransitQuantity:0,acceptedQuantity:1,cancelledQuantity:0,unitPrice:1e5,totalPrice:1e5,orderNo:"PO20240320008",orderStatus:"archived",logisticsStatus:"已归档",financialStatus:"已归档",customer:"北京自动化设备有限公司"},{id:"p12",productName:"环境传感器组",model:"ESG-100",quantity:10,preparingQuantity:0,inTransitQuantity:0,acceptedQuantity:10,cancelledQuantity:0,unitPrice:3e3,totalPrice:3e4,orderNo:"PO20240320008",orderStatus:"archived",logisticsStatus:"已归档",financialStatus:"已归档",customer:"北京自动化设备有限公司"},{id:"p13",productName:"数据采集终端",model:"DAT-5000",quantity:5,preparingQuantity:0,inTransitQuantity:0,acceptedQuantity:5,cancelledQuantity:0,unitPrice:1e4,totalPrice:5e4,orderNo:"PO20240320008",orderStatus:"archived",logisticsStatus:"已归档",financialStatus:"已归档",customer:"北京自动化设备有限公司"}]),r=u=>{C.value=u,console.log("Selected products:",u)},S=u=>{if(u==="ship"){const d=C.value.filter(x=>x.preparingQuantity>0);if(d.length===0){console.warn("No products selected for shipping or none have pending stock");return}console.log("Bulk shipping selected products:",d)}},f=u=>{y.value=u,console.log("Page size changed to:",u)},h=u=>{v.value=u,console.log("Current page changed to:",u)},z=u=>({pending_confirmation:"待确认",confirming:"确认中",in_progress:"执行中",completed:"已完成",cancelled:"已取消",cancelling:"取消中",exception_pending:"异常待处理",archived:"已归档"})[u]||u,k=u=>({pending_confirmation:"info",confirming:"warning",in_progress:"primary",completed:"success",cancelled:"danger",cancelling:"warning",exception_pending:"danger",archived:"info"})[u]||"";return(u,d)=>{const x=o("el-icon"),U=o("el-button"),D=o("el-dropdown-item"),$=o("el-dropdown-menu"),p=o("el-dropdown"),R=o("el-checkbox"),j=o("el-drawer"),E=o("el-table-column"),F=o("el-tag"),L=o("el-table"),i=o("el-pagination");return c(),_("div",null,[Q("div",Qe,[e(p,{onCommand:S},{dropdown:t(()=>[e($,null,{default:t(()=>[e(D,{command:"ship"},{default:t(()=>d[5]||(d[5]=[m("发货")])),_:1})]),_:1})]),default:t(()=>[e(U,{type:"primary"},{default:t(()=>[d[4]||(d[4]=m(" 批量操作")),e(x,{class:"el-icon--right"},{default:t(()=>[e(te(ae))]),_:1})]),_:1})]),_:1}),e(U,{onClick:d[0]||(d[0]=a=>n.value=!0),type:"primary"},{default:t(()=>d[6]||(d[6]=[m(" 配置列 ")])),_:1})]),e(j,{title:"配置列",modelValue:n.value,"onUpdate:modelValue":d[1]||(d[1]=a=>n.value=a),direction:"rtl",size:"300px"},{default:t(()=>[Q("div",Pe,[(c(!0),_(N,null,A(V,a=>(c(),_("div",{key:a.prop},[e(R,{modelValue:a.visible,"onUpdate:modelValue":b=>a.visible=b},{default:t(()=>[m(P(a.label),1)]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),128))])]),_:1},8,["modelValue"]),e(L,{data:W.value,style:{width:"100%"},border:"",onSelectionChange:r},{default:t(()=>[e(E,{type:"selection",width:"55"}),(c(!0),_(N,null,A(M.value,a=>(c(),_(N,{key:a.prop},[a.visible?(c(),T(E,{key:0,prop:a.prop,label:a.label,"min-width":a.minWidth},H({_:2},[a.prop==="unitPrice"||a.prop==="totalPrice"?{name:"default",fn:t(b=>[m(" ¥"+P(w(b.row[a.prop])),1)]),key:"0"}:a.prop==="orderStatus"?{name:"default",fn:t(b=>[e(F,{type:k(b.row.orderStatus)},{default:t(()=>[m(P(z(b.row.orderStatus)),1)]),_:2},1032,["type"])]),key:"1"}:void 0]),1032,["prop","label","min-width"])):O("",!0)],64))),128))]),_:1},8,["data"]),Q("div",Ce,[e(i,{"current-page":v.value,"onUpdate:currentPage":d[2]||(d[2]=a=>v.value=a),"page-size":y.value,"onUpdate:pageSize":d[3]||(d[3]=a=>y.value=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:q.value,onSizeChange:f,onCurrentChange:h},null,8,["current-page","page-size","total"])])])}}},Ne=J(we,[["__scopeId","data-v-1d5e4097"]]),Ve={class:"filter-container"},ke={style:{"margin-bottom":"20px",display:"flex","justify-content":"flex-end"}},xe={class:"view-content-container"},Te={__name:"index",setup(X){const n=I({orderNo:"",customerName:"",supplier:"",status:"",productName:"",model:"",category:"",brand:"",pageNum:1,pageSize:10}),C=g([]),v=g(!1),y=g("order"),q=()=>{v.value=!0,console.log("Fetching list with params:",JSON.parse(JSON.stringify(n)),"Current view:",y.value),setTimeout(()=>{console.log(`Data fetching for ${y.value} view would happen here.`),v.value=!1},300)},w=()=>{n.pageNum=1,q()},V=()=>{n.orderNo="",n.customerName="",n.supplier="",n.status="",n.productName="",n.model="",n.category="",n.brand="",C.value=[],w()},M=W=>{console.log("Switched to view:",W),w()};return me(()=>{q()}),(W,r)=>{const S=o("el-input"),f=o("el-form-item"),h=o("el-option"),z=o("el-select"),k=o("el-date-picker"),u=o("el-button"),d=o("el-form"),x=o("el-radio-button"),U=o("el-radio-group"),D=o("el-card"),$=ye("loading");return c(),T(D,{class:"order-list-container"},{default:t(()=>[Q("div",Ve,[e(d,{inline:!0,model:n,class:"demo-form-inline"},{default:t(()=>[e(f,{label:"销售订单号"},{default:t(()=>[e(S,{modelValue:n.orderNo,"onUpdate:modelValue":r[0]||(r[0]=p=>n.orderNo=p),placeholder:"请输入销售订单号",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"客户名称"},{default:t(()=>[e(S,{modelValue:n.customerName,"onUpdate:modelValue":r[1]||(r[1]=p=>n.customerName=p),placeholder:"请输入客户名称",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"供应商"},{default:t(()=>[e(S,{modelValue:n.supplier,"onUpdate:modelValue":r[2]||(r[2]=p=>n.supplier=p),placeholder:"请输入供应商",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"物料名称"},{default:t(()=>[e(S,{modelValue:n.productName,"onUpdate:modelValue":r[3]||(r[3]=p=>n.productName=p),placeholder:"请输入物料名称",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"物料型号"},{default:t(()=>[e(S,{modelValue:n.model,"onUpdate:modelValue":r[4]||(r[4]=p=>n.model=p),placeholder:"请输入物料型号",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"物料分类"},{default:t(()=>[e(S,{modelValue:n.category,"onUpdate:modelValue":r[5]||(r[5]=p=>n.category=p),placeholder:"请输入物料分类",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"品牌"},{default:t(()=>[e(S,{modelValue:n.brand,"onUpdate:modelValue":r[6]||(r[6]=p=>n.brand=p),placeholder:"请输入品牌",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"订单状态"},{default:t(()=>[e(z,{modelValue:n.status,"onUpdate:modelValue":r[7]||(r[7]=p=>n.status=p),placeholder:"请选择状态",clearable:""},{default:t(()=>[e(h,{label:"待确认",value:"pending_confirmation"}),e(h,{label:"确认中",value:"confirming"}),e(h,{label:"执行中",value:"in_progress"}),e(h,{label:"已完成",value:"completed"}),e(h,{label:"已取消",value:"cancelled"}),e(h,{label:"取消中",value:"cancelling"}),e(h,{label:"异常待处理",value:"exception_pending"}),e(h,{label:"已归档",value:"archived"})]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"下单时间"},{default:t(()=>[e(k,{modelValue:C.value,"onUpdate:modelValue":r[8]||(r[8]=p=>C.value=p),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(f,null,{default:t(()=>[e(u,{type:"primary",onClick:w},{default:t(()=>r[10]||(r[10]=[m("查询")])),_:1}),e(u,{onClick:V},{default:t(()=>r[11]||(r[11]=[m("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),Q("div",ke,[e(U,{modelValue:y.value,"onUpdate:modelValue":r[9]||(r[9]=p=>y.value=p),onChange:M},{default:t(()=>[e(x,{label:"order"},{default:t(()=>r[12]||(r[12]=[m("订单视图")])),_:1}),e(x,{label:"product"},{default:t(()=>r[13]||(r[13]=[m("物料视图")])),_:1})]),_:1},8,["modelValue"])]),ge((c(),_("div",xe,[y.value==="order"?(c(),T(he,{key:0,filters:n},null,8,["filters"])):O("",!0),y.value==="product"?(c(),T(Ne,{key:1,filters:n},null,8,["filters"])):O("",!0)])),[[$,v.value]])]),_:1})}}},Oe=J(Te,[["__scopeId","data-v-cba72b3d"]]);export{Oe as default};
