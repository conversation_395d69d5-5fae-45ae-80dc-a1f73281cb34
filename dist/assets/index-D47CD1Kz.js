import{_ as oe,l as O,d as b,e as _,m as ne,o as ue,w as r,a as h,b as e,r as f,i as s,t as p}from"./index-Di-G_xQb.js";const se={class:"filter-container"},de={class:"table-content-container"},pe={class:"inner-table-container"},ce={class:"quote-history-container"},me={class:"material-info"},fe={class:"history-filter-container"},ge={class:"history-table-container"},ye={__name:"index",setup(be){const i=O({customer:"",materialModel:"",brand:"",materialCategory:"",rfqNumber:"",inquiryStatus:"",inquiryDate:[],deadline:[]}),D=b([]),q=b(1),V=b(10),C=b(!1),v=b({}),w=b(1),T=b(10),d=O({customer:"",supplier:"",quoteDate:[]}),F=b([{id:1,materialId:1,customer:"ABC科技有限公司",supplier:"供应商A",quantity:100,supplierPrice:2800,salesPrice:3200,totalPrice:32e4,supplierRemarks:"现货充足，可立即发货，质保一年",quoteTime:"2024-07-29 14:30:00"},{id:2,materialId:1,customer:"ABC科技有限公司",supplier:"供应商B",quantity:100,supplierPrice:2900,salesPrice:3300,totalPrice:33e4,supplierRemarks:"质量保证，一年质保，可提供技术支持",quoteTime:"2024-07-29 16:20:00"},{id:3,materialId:1,customer:"DEF电子公司",supplier:"供应商A",quantity:50,supplierPrice:2850,salesPrice:3250,totalPrice:162500,supplierRemarks:"小批量采购，价格略有上调",quoteTime:"2024-07-25 10:15:00"},{id:4,materialId:2,customer:"XYZ电子公司",supplier:"供应商C",quantity:50,supplierPrice:1200,salesPrice:1400,totalPrice:7e4,supplierRemarks:"需要确认库存，预计3天内发货",quoteTime:"2024-07-30 11:00:00"},{id:5,materialId:2,customer:"GHI科技公司",supplier:"供应商D",quantity:30,supplierPrice:1180,salesPrice:1380,totalPrice:41400,supplierRemarks:"原厂正品，可提供质保证书",quoteTime:"2024-07-28 15:45:00"},{id:6,materialId:3,customer:"精密机械制造厂",supplier:"供应商D",quantity:200,supplierPrice:85,salesPrice:100,totalPrice:2e4,supplierRemarks:"批量采购优惠价格，包装完好",quoteTime:"2024-07-28 09:45:00"},{id:7,materialId:3,customer:"精密机械制造厂",supplier:"供应商E",quantity:200,supplierPrice:88,salesPrice:105,totalPrice:21e3,supplierRemarks:"进口原装，质量可靠",quoteTime:"2024-07-28 15:30:00"},{id:8,materialId:3,customer:"JKL工业公司",supplier:"供应商F",quantity:100,supplierPrice:90,salesPrice:108,totalPrice:10800,supplierRemarks:"现货供应，可立即交付",quoteTime:"2024-07-26 13:20:00"}]),Q=_(()=>{let t=F.value.filter(l=>l.materialId===v.value.id);if(d.customer&&(t=t.filter(l=>l.customer.includes(d.customer))),d.supplier&&(t=t.filter(l=>l.supplier.includes(d.supplier))),d.quoteDate&&d.quoteDate.length===2){const[l,c]=d.quoteDate;t=t.filter(n=>{const u=new Date(n.quoteTime),m=new Date(c);return m.setHours(23,59,59,999),u>=new Date(l)&&u<=m})}return t}),U=_(()=>Q.value.length),j=_(()=>{const t=(w.value-1)*T.value,l=t+T.value;return Q.value.slice(t,l)}),A=b([{id:1,customer:"ABC科技有限公司",materialName:"英特尔处理器芯片",model:"i7-13700K",inquiryStatus:"quoting",brand:"Intel",materialCategory:"electronics",quantity:100,bestSupplierPrice:2800,salesPrice:3200,totalPrice:32e4,minOrderQuantity:10,deliveryTime:"7-10天",deadline:"2024-08-15 18:00:00",rfqNumber:"RFQ202407001",inquiryTime:"2024-07-28 09:30:00",supplierQuotes:[{supplierName:"供应商A",supplierPrice:2800,salesPrice:3200,totalPrice:32e4,minOrderQuantity:10,promisedDelivery:"7天",quoteTime:"2024-07-29 14:30:00",quoteStatus:"active",remarks:"现货充足，可立即发货"},{supplierName:"供应商B",supplierPrice:2900,salesPrice:3300,totalPrice:33e4,minOrderQuantity:5,promisedDelivery:"10天",quoteTime:"2024-07-29 16:20:00",quoteStatus:"active",remarks:"质量保证，一年质保"}]},{id:2,customer:"XYZ电子公司",materialName:"三星内存条",model:"DDR5-4800 32GB",inquiryStatus:"pending",brand:"Samsung",materialCategory:"electronics",quantity:50,bestSupplierPrice:1200,salesPrice:1400,totalPrice:7e4,minOrderQuantity:2,deliveryTime:"5-7天",deadline:"2024-08-20 17:00:00",rfqNumber:"RFQ202407002",inquiryTime:"2024-07-29 10:15:00",supplierQuotes:[{supplierName:"供应商C",supplierPrice:1200,salesPrice:1400,totalPrice:7e4,minOrderQuantity:2,promisedDelivery:"5天",quoteTime:"2024-07-30 11:00:00",quoteStatus:"pending",remarks:"需要确认库存"}]},{id:3,customer:"精密机械制造厂",materialName:"精密轴承",model:"SKF-6205",inquiryStatus:"completed",brand:"SKF",materialCategory:"mechanical",quantity:200,bestSupplierPrice:85,salesPrice:100,totalPrice:2e4,minOrderQuantity:20,deliveryTime:"3-5天",deadline:"2024-08-10 16:00:00",rfqNumber:"RFQ202407003",inquiryTime:"2024-07-27 14:20:00",supplierQuotes:[{supplierName:"供应商D",supplierPrice:85,salesPrice:100,totalPrice:2e4,minOrderQuantity:20,promisedDelivery:"3天",quoteTime:"2024-07-28 09:45:00",quoteStatus:"selected",remarks:"已选择此报价"},{supplierName:"供应商E",supplierPrice:88,salesPrice:105,totalPrice:21e3,minOrderQuantity:15,promisedDelivery:"5天",quoteTime:"2024-07-28 15:30:00",quoteStatus:"rejected",remarks:"价格偏高"}]}]),x=_(()=>{let t=A.value;if(i.customer&&(t=t.filter(l=>l.customer.includes(i.customer))),i.materialModel&&(t=t.filter(l=>l.model.includes(i.materialModel))),i.brand&&(t=t.filter(l=>l.brand.includes(i.brand))),i.materialCategory&&(t=t.filter(l=>l.materialCategory===i.materialCategory)),i.rfqNumber&&(t=t.filter(l=>l.rfqNumber.includes(i.rfqNumber))),i.inquiryStatus&&(t=t.filter(l=>l.inquiryStatus===i.inquiryStatus)),i.inquiryDate&&i.inquiryDate.length===2){const[l,c]=i.inquiryDate;t=t.filter(n=>{const u=new Date(n.inquiryTime),m=new Date(c);return m.setHours(23,59,59,999),u>=new Date(l)&&u<=m})}if(i.deadline&&i.deadline.length===2){const[l,c]=i.deadline;t=t.filter(n=>{const u=new Date(n.deadline),m=new Date(c);return m.setHours(23,59,59,999),u>=new Date(l)&&u<=m})}return t}),E=_(()=>x.value.length),B=_(()=>{const t=(q.value-1)*V.value,l=t+V.value;return x.value.slice(t,l)}),M=()=>{q.value=1,console.log("Searching with:",i)},$=()=>{Object.keys(i).forEach(t=>{Array.isArray(i[t])?i[t]=[]:i[t]=""}),q.value=1,console.log("Search form reset")},K=(t,l)=>{if(l.includes(t))D.value.push(t.id);else{const c=D.value.indexOf(t.id);c>-1&&D.value.splice(c,1)}},y=t=>t==null||t===""?"-":Number(t).toLocaleString("zh-CN",{minimumFractionDigits:2}),k=t=>{if(!t)return"-";const l=new Date(t),c=l.getFullYear(),n=(l.getMonth()+1).toString().padStart(2,"0"),u=l.getDate().toString().padStart(2,"0"),m=l.getHours().toString().padStart(2,"0"),P=l.getMinutes().toString().padStart(2,"0");return`${c}-${n}-${u} ${m}:${P}`},L=t=>{switch(t){case"pending":return"待报价";case"quoting":return"报价中";case"completed":return"已完成";case"expired":return"已过期";default:return"未知"}},Y=t=>{switch(t){case"pending":return"warning";case"quoting":return"info";case"completed":return"success";case"expired":return"danger";default:return"default"}},G=t=>{switch(t){case"pending":return"待确认";case"active":return"有效";case"selected":return"已选择";case"rejected":return"已拒绝";default:return"未知"}},X=t=>{switch(t){case"pending":return"warning";case"active":return"info";case"selected":return"success";case"rejected":return"danger";default:return"default"}},Z=t=>{console.log("View quote history:",t),v.value=t,C.value=!0,R()},J=t=>{V.value=t,q.value=1},W=t=>{q.value=t},ee=()=>{w.value=1,console.log("Searching history with:",d)},R=()=>{Object.keys(d).forEach(t=>{Array.isArray(d[t])?d[t]=[]:d[t]=""}),w.value=1,console.log("History search form reset")},te=()=>{C.value=!1,v.value={}},le=t=>{T.value=t,w.value=1},ae=t=>{w.value=t};return(t,l)=>{const c=f("el-input"),n=f("el-form-item"),u=f("el-option"),m=f("el-select"),P=f("el-date-picker"),S=f("el-button"),z=f("el-form"),o=f("el-table-column"),H=f("el-tag"),N=f("el-table"),I=f("el-pagination"),re=f("el-dialog"),ie=f("el-card");return ue(),ne(ie,{class:"rfq-list-container"},{default:r(()=>[h("div",se,[e(z,{inline:!0,model:i,class:"search-form"},{default:r(()=>[e(n,{label:"客户"},{default:r(()=>[e(c,{modelValue:i.customer,"onUpdate:modelValue":l[0]||(l[0]=a=>i.customer=a),placeholder:"请输入客户名称",clearable:""},null,8,["modelValue"])]),_:1}),e(n,{label:"物料型号"},{default:r(()=>[e(c,{modelValue:i.materialModel,"onUpdate:modelValue":l[1]||(l[1]=a=>i.materialModel=a),placeholder:"请输入物料型号",clearable:""},null,8,["modelValue"])]),_:1}),e(n,{label:"品牌"},{default:r(()=>[e(c,{modelValue:i.brand,"onUpdate:modelValue":l[2]||(l[2]=a=>i.brand=a),placeholder:"请输入品牌",clearable:""},null,8,["modelValue"])]),_:1}),e(n,{label:"物料分类"},{default:r(()=>[e(m,{modelValue:i.materialCategory,"onUpdate:modelValue":l[3]||(l[3]=a=>i.materialCategory=a),placeholder:"请选择物料分类",clearable:""},{default:r(()=>[e(u,{label:"全部",value:""}),e(u,{label:"电子元器件",value:"electronics"}),e(u,{label:"机械配件",value:"mechanical"}),e(u,{label:"化工材料",value:"chemical"}),e(u,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"询价单号"},{default:r(()=>[e(c,{modelValue:i.rfqNumber,"onUpdate:modelValue":l[4]||(l[4]=a=>i.rfqNumber=a),placeholder:"请输入询价单号",clearable:""},null,8,["modelValue"])]),_:1}),e(n,{label:"询价状态"},{default:r(()=>[e(m,{modelValue:i.inquiryStatus,"onUpdate:modelValue":l[5]||(l[5]=a=>i.inquiryStatus=a),placeholder:"请选择询价状态",clearable:""},{default:r(()=>[e(u,{label:"全部",value:""}),e(u,{label:"待报价",value:"pending"}),e(u,{label:"报价中",value:"quoting"}),e(u,{label:"已完成",value:"completed"}),e(u,{label:"已过期",value:"expired"})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"询价时间"},{default:r(()=>[e(P,{modelValue:i.inquiryDate,"onUpdate:modelValue":l[6]||(l[6]=a=>i.inquiryDate=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:""},null,8,["modelValue"])]),_:1}),e(n,{label:"截止时间"},{default:r(()=>[e(P,{modelValue:i.deadline,"onUpdate:modelValue":l[7]||(l[7]=a=>i.deadline=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:""},null,8,["modelValue"])]),_:1}),e(n,null,{default:r(()=>[e(S,{type:"primary",onClick:M},{default:r(()=>l[12]||(l[12]=[s("搜索")])),_:1}),e(S,{onClick:$},{default:r(()=>l[13]||(l[13]=[s("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),h("div",de,[e(N,{data:B.value,style:{width:"100%"},border:"","expand-row-keys":D.value,onExpandChange:K,"row-key":"id"},{default:r(()=>[e(o,{type:"expand",width:"50"},{default:r(({row:a})=>[h("div",pe,[e(N,{data:a.supplierQuotes,border:"",size:"small"},{default:r(()=>[e(o,{prop:"supplierName",label:"供应商","min-width":"120"}),e(o,{prop:"supplierPrice",label:"供应商报价（¥）","min-width":"120",align:"right"},{default:r(g=>[s(p(y(g.row.supplierPrice)),1)]),_:2},1024),e(o,{prop:"salesPrice",label:"销售价（¥）","min-width":"100",align:"right"},{default:r(g=>[s(p(y(g.row.salesPrice)),1)]),_:2},1024),e(o,{prop:"totalPrice",label:"总价（¥）","min-width":"100",align:"right"},{default:r(g=>[s(p(y(g.row.totalPrice)),1)]),_:2},1024),e(o,{prop:"minOrderQuantity",label:"最小起订量","min-width":"100",align:"center"}),e(o,{prop:"promisedDelivery",label:"承诺交期","min-width":"120",align:"center"}),e(o,{prop:"quoteTime",label:"报价时间","min-width":"140"},{default:r(g=>[s(p(k(g.row.quoteTime)),1)]),_:2},1024),e(o,{prop:"quoteStatus",label:"报价状态","min-width":"100",align:"center"},{default:r(g=>[e(H,{type:X(g.row.quoteStatus)},{default:r(()=>[s(p(G(g.row.quoteStatus)),1)]),_:2},1032,["type"])]),_:2},1024),e(o,{prop:"remarks",label:"备注","min-width":"150","show-overflow-tooltip":""})]),_:2},1032,["data"])])]),_:1}),e(o,{prop:"customer",label:"客户","min-width":"120"}),e(o,{prop:"materialName",label:"物料名称","min-width":"150","show-overflow-tooltip":""}),e(o,{prop:"model",label:"型号","min-width":"120"}),e(o,{prop:"inquiryStatus",label:"询价状态","min-width":"100",align:"center"},{default:r(a=>[e(H,{type:Y(a.row.inquiryStatus)},{default:r(()=>[s(p(L(a.row.inquiryStatus)),1)]),_:2},1032,["type"])]),_:1}),e(o,{prop:"brand",label:"品牌","min-width":"100"}),e(o,{prop:"materialCategory",label:"物料分类","min-width":"120"}),e(o,{prop:"quantity",label:"数量","min-width":"80",align:"center"}),e(o,{prop:"bestSupplierPrice",label:"供应商报价（¥）","min-width":"130",align:"right"},{default:r(a=>[s(p(y(a.row.bestSupplierPrice)),1)]),_:1}),e(o,{prop:"salesPrice",label:"销售价（¥）","min-width":"110",align:"right"},{default:r(a=>[s(p(y(a.row.salesPrice)),1)]),_:1}),e(o,{prop:"totalPrice",label:"总价（¥）","min-width":"110",align:"right"},{default:r(a=>[s(p(y(a.row.totalPrice)),1)]),_:1}),e(o,{prop:"minOrderQuantity",label:"最小起订量","min-width":"110",align:"center"}),e(o,{prop:"deliveryTime",label:"交期","min-width":"100",align:"center"}),e(o,{prop:"deadline",label:"截止时间","min-width":"140"},{default:r(a=>[s(p(k(a.row.deadline)),1)]),_:1}),e(o,{prop:"rfqNumber",label:"询价单号","min-width":"140"}),e(o,{prop:"inquiryTime",label:"询价时间","min-width":"140"},{default:r(a=>[s(p(k(a.row.inquiryTime)),1)]),_:1}),e(o,{label:"操作","min-width":"180",align:"center",fixed:"right"},{default:r(a=>[e(S,{type:"primary",link:"",size:"small",onClick:g=>Z(a.row)},{default:r(()=>l[14]||(l[14]=[s("查看报价历史")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","expand-row-keys"]),e(I,{class:"pagination-container","current-page":q.value,"page-sizes":[10,20,50,100],"page-size":V.value,layout:"total, sizes, prev, pager, next, jumper",total:E.value,onSizeChange:J,onCurrentChange:W},null,8,["current-page","page-size","total"])]),e(re,{modelValue:C.value,"onUpdate:modelValue":l[11]||(l[11]=a=>C.value=a),title:"报价历史",width:"80%",top:"5vh","before-close":te},{default:r(()=>[h("div",ce,[h("div",me,[h("h4",null,"物料信息："+p(v.value.materialName)+" - "+p(v.value.model)+" ("+p(v.value.brand)+")",1)]),h("div",fe,[e(z,{inline:!0,model:d,class:"history-search-form"},{default:r(()=>[e(n,{label:"客户"},{default:r(()=>[e(c,{modelValue:d.customer,"onUpdate:modelValue":l[8]||(l[8]=a=>d.customer=a),placeholder:"请输入客户名称",clearable:""},null,8,["modelValue"])]),_:1}),e(n,{label:"供应商"},{default:r(()=>[e(c,{modelValue:d.supplier,"onUpdate:modelValue":l[9]||(l[9]=a=>d.supplier=a),placeholder:"请输入供应商名称",clearable:""},null,8,["modelValue"])]),_:1}),e(n,{label:"报价时间"},{default:r(()=>[e(P,{modelValue:d.quoteDate,"onUpdate:modelValue":l[10]||(l[10]=a=>d.quoteDate=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:""},null,8,["modelValue"])]),_:1}),e(n,null,{default:r(()=>[e(S,{type:"primary",onClick:ee},{default:r(()=>l[15]||(l[15]=[s("搜索")])),_:1}),e(S,{onClick:R},{default:r(()=>l[16]||(l[16]=[s("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),h("div",ge,[e(N,{data:j.value,border:"",style:{width:"100%"}},{default:r(()=>[e(o,{prop:"customer",label:"客户","min-width":"150"}),e(o,{prop:"supplier",label:"供应商","min-width":"150"}),e(o,{prop:"quantity",label:"数量","min-width":"80",align:"center"}),e(o,{prop:"supplierPrice",label:"供应商报价（¥）","min-width":"130",align:"right"},{default:r(a=>[s(p(y(a.row.supplierPrice)),1)]),_:1}),e(o,{prop:"salesPrice",label:"销售价（¥）","min-width":"110",align:"right"},{default:r(a=>[s(p(y(a.row.salesPrice)),1)]),_:1}),e(o,{prop:"totalPrice",label:"总价（¥）","min-width":"110",align:"right"},{default:r(a=>[s(p(y(a.row.totalPrice)),1)]),_:1}),e(o,{prop:"supplierRemarks",label:"供应商备注","min-width":"200","show-overflow-tooltip":""}),e(o,{prop:"quoteTime",label:"报价时间","min-width":"140"},{default:r(a=>[s(p(k(a.row.quoteTime)),1)]),_:1})]),_:1},8,["data"]),e(I,{class:"history-pagination","current-page":w.value,"page-sizes":[10,20,50,100],"page-size":T.value,layout:"total, sizes, prev, pager, next, jumper",total:U.value,onSizeChange:le,onCurrentChange:ae},null,8,["current-page","page-size","total"])])])]),_:1},8,["modelValue"])]),_:1})}}},qe=oe(ye,[["__scopeId","data-v-c7bcd7ca"]]);export{qe as default};
