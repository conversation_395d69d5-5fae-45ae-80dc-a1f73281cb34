import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    redirect: '/dashboard',
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('./views/Dashboard.vue'),
    meta: { title: '首页' },
  },
  {
    path: '/enterprise',
    name: 'Enterprise',
    component: () => import('./views/enterprise/index.vue'),
    meta: { title: '企业' },
    children: [
      {
        path: 'settings',
        name: 'EnterpriseSettings',
        component: () => import('./views/enterprise/EnterpriseSettings.vue'),
        meta: { title: '企业设置' },
      },
      {
        path: 'agent',
        name: 'EnterpriseAgent',
        component: () => import('./views/enterprise/AgentManage.vue'),
        meta: { title: '智能体管理' },
      },
    ],
  },
  {
    path: '/commodity',
    name: 'Commodity',
    component: () => import('./views/commodity/index.vue'),
    meta: { title: '商品' },
    children: [
      {
        path: 'management',
        name: 'CommodityManagement',
        component: () => import('./views/commodity/CommodityManagement.vue'),
        meta: { title: '商品管理' },
      },
      {
        path: 'publish',
        name: 'CommodityPublish',
        component: () => import('./views/commodity/CommodityPublish.vue'),
        meta: { title: '发布商品' },
      },
      {
        path: 'category',
        name: 'CommodityCategory',
        component: () => import('./views/commodity/CommodityCategory.vue'),
        meta: { title: '分类管理' },
      },
      {
        path: 'specification',
        name: 'CommoditySpecification',
        component: () => import('./views/commodity/CommoditySpecification.vue'),
        meta: { title: '规格管理' },
      },
      {
        path: 'brand',
        name: 'CommodityBrand',
        component: () => import('./views/commodity/CommodityBrand.vue'),
        meta: { title: '品牌管理' },
      },
      {
        path: 'model',
        name: 'CommodityModel',
        component: () => import('./views/commodity/CommodityModel.vue'),
        meta: { title: '模型管理' },
      },
    ],
  },
  {
    path: '/trade',
    name: 'Trade',
    component: () => import('./views/trade/index.vue'),
    meta: { title: '交易' },
    children: [
      {
        path: 'rfq',
        name: 'RFQ',
        meta: { title: '询价管理' },
        redirect: '/rfq/index',
        children: [
          {
            path: 'index',
            name: 'RFQIndex',
            component: () => import('./views/trade/rfq/index.vue'),
            meta: { title: '询价列表' },
          },
          {
            path: 'adjust',
            name: 'RFQAdjust',
            component: () => import('./views/trade/rfq/Adjust.vue'),
            meta: { title: '报价调整' },
          },
        ],
      },

      {
        path: 'sale',
        name: 'Sale',
        redirect: '/trade/sale/so/index',
        children: [
          {
            path: 'so',
            name: 'SOIndex',
            component: () => import('./views/trade/sale/so/index.vue'),
            meta: { title: '销售订单' },
          },
          {
            path: 'so/detail/:id?',
            name: 'SODetail',
            component: () => import('./views/trade/sale/so/SoDetail.vue'),
            meta: { title: '订单详情' },
          },
          {
            path: 'inv',
            name: 'SOInv',
            component: () => import('./views/trade/sale/inv/index.vue'),
            meta: { title: '对账单' },
          },
          {
            path: 'pay',
            name: 'SOPay',
            component: () => import('./views/trade/sale/pay/index.vue'),
            meta: { title: '付款单' },
          },
          {
            path: 'pay/detail/:id',
            name: 'SOPayDetail',
            component: () => import('./views/trade/sale/pay/payDetail.vue'),
            meta: { title: '付款单详情' },
          },
        ],
      },

      {
        path: 'buy',
        name: 'Buy',
        redirect: '/trade/buy/po/index',
        children: [
          {
            path: 'po',
            name: 'POIndex',
            component: () => import('./views/trade/buy/po/index.vue'),
            meta: { title: '采购单' },
          },
        ],
      },
      {
        path: 'dn',
        name: 'DN',
        component: () => import('./views/trade/dn/index.vue'),
        meta: { title: '送货管理' },
        redirect: '/trade/dn/index',
        children: [
          {
            path: 'index',
            name: 'DNIndex',
            component: () => import('./views/trade/dn/index.vue'),
          },
          {
            path: 'return',
            name: 'DNReturn',
            component: () => import('./views/trade/dn/rn.vue'),
          },
        ],
      },
      
    ],
  },
  {
    path: '/data',
    name: 'Data',
    component: () => import('./views/data/index.vue'),
    meta: { title: '数据' },
    children: [
      {
        path: 'supplier',
        name: 'DataSupplier',
        component: () => import('./views/data/Supplier.vue'),
        meta: { title: '供应商' },
      },
      {
        path: 'part/jobs',
        name: 'DataPartJobs',
        component: () => import('./views/data/part/jobs.vue'),
        meta: { title: '导入任务' },
      },
      {
        path: 'part/mapping',
        name: 'DataPartMapping',
        component: () => import('./views/data/part/mapping.vue'),
        meta: { title: '字段映射' },
      },
      {
        path: 'part/rawRecords',
        name: 'DataPartRawRecords',
        component: () => import('./views/data/part/rawRecords.vue'),
        meta: { title: '原始记录' },
      },
      {
        path: 'part/errors',
        name: 'DataPartErrors',
        component: () => import('./views/data/part/errors.vue'),
        meta: { title: '错误与修复' },
      },
    ],
  },
  {
    path: '/system',
    name: 'System',
    component: () => import('./views/system/index.vue'),
    meta: { title: '系统' },
    children: [
      {
        path: 'organization',
        name: 'SystemOrganization',
        component: () => import('./views/system/SystemOrganization.vue'),
        meta: { title: '组织架构' },
      },
      {
        path: 'resources',
        name: 'SystemResources',
        component: () => import('./views/system/SystemResources.vue'),
        meta: { title: '资源管理' },
      },
      {
        path: 'messages',
        name: 'SystemMessages',
        component: () => import('./views/system/SystemMessages.vue'),
        meta: { title: '消息管理' },
      },
    ],
  },
];

const router = createRouter({
  history: createWebHistory('/platform/'),
  routes,
});

export default router;
