<template>
  <div class="agent-manage">
    <el-card class="agent-manage-container">
      <div class="tabs-container">
        <el-tabs v-model="activeTab" class="agent-tabs">
          <el-tab-pane label="小妍" name="xiaoyan">
            <div class="tab-content">
              <!-- 小妍表格 -->
              <div class="table-content-container" v-loading="loading">
                <el-table :data="xiaoyanAgents" style="width: 100%" border>
                  <el-table-column prop="name" label="名称" min-width="120"></el-table-column>
                  <el-table-column prop="avatar" label="头像" min-width="80" align="center">
                    <template #default="scope">
                      <el-avatar :src="scope.row.avatar" :alt="scope.row.name" size="small"></el-avatar>
                    </template>
                  </el-table-column>
                  <el-table-column prop="brand" label="品牌" min-width="100"></el-table-column>
                  <el-table-column prop="status" label="状态" min-width="100" align="center">
                    <template #default="scope">
                      <el-tag :type="scope.row.status === '启用' ? 'success' : 'warning'">
                        {{ scope.row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="120" align="center">
                    <template #default="scope">
                      <el-button 
                        :type="scope.row.status === '启用' ? 'danger' : 'success'"
                        link 
                        size="small"
                        @click="toggleAgentStatus(scope.row)"
                      >
                        {{ scope.row.status === '启用' ? '禁用' : '启用' }}
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="小彩" name="xiaocai">
            <div class="tab-content">
              <div class="xiaocai-layout">
                <!-- 左侧小彩分身列表 -->
                <div class="left-panel">
                  <div class="avatar-search">
                    <el-input 
                      v-model="avatarSearch" 
                      placeholder="搜索分身名称" 
                      clearable 
                      @input="handleAvatarSearch"
                    >
                      <template #prefix>
                        <el-icon><Search /></el-icon>
                      </template>
                    </el-input>
                  </div>
                                      <div class="avatar-list">
                      <div 
                        v-for="avatar in filteredAvatars" 
                        :key="avatar.id"
                        :class="['avatar-item', { active: selectedAvatar === avatar.id }]"
                        @click="selectAvatar(avatar.id)"
                      >
                        <el-avatar :src="avatar.image" :alt="avatar.name" size="small"></el-avatar>
                        <span class="avatar-name">{{ avatar.name }}</span>
                      </div>
                    </div>
                </div>
                
                <!-- 右侧企业管理 -->
                <div class="right-panel">
                  <!-- 搜索区 -->
                  <div class="filter-container">
                    <el-form :inline="true" :model="xiaocaiSearch" class="search-form">
                      <el-form-item label="企业名称">
                        <el-input v-model="xiaocaiSearch.enterpriseName" placeholder="请输入企业名称" clearable></el-input>
                      </el-form-item>
                                             <el-form-item label="企业性质">
                         <el-select v-model="xiaocaiSearch.enterpriseType" placeholder="请选择企业性质" clearable>
                           <el-option label="全部" value=""></el-option>
                           <el-option label="设备商" value="设备商"></el-option>
                           <el-option label="供应商" value="供应商"></el-option>
                         </el-select>
                       </el-form-item>
                      <el-form-item label="企业微信群名称">
                        <el-input v-model="xiaocaiSearch.groupName" placeholder="请输入群名称" clearable></el-input>
                      </el-form-item>
                      <el-form-item>
                        <el-button type="primary" @click="searchXiaocaiEnterprises">搜索</el-button>
                        <el-button @click="resetXiaocaiSearch">重置</el-button>
                      </el-form-item>
                    </el-form>
                  </div>
                  
                  <!-- 企业表格 -->
                  <div class="table-content-container" v-loading="loading">
                    <el-table :data="filteredXiaocaiEnterprises" style="width: 100%" border>
                      <el-table-column prop="name" label="企业名称" min-width="150"></el-table-column>
                      <el-table-column prop="type" label="企业性质" min-width="100"></el-table-column>
                      <el-table-column prop="groupName" label="企业微信群名称" min-width="150"></el-table-column>
                      <el-table-column prop="status" label="状态" min-width="100" align="center">
                        <template #default="scope">
                          <el-tag :type="scope.row.status === '启用' ? 'success' : 'warning'">
                            {{ scope.row.status }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="bindTime" label="绑定时间" min-width="160">
                        <template #default="scope">
                          {{ formatDateTime(scope.row.bindTime) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" min-width="160" align="center">
                        <template #default="scope">
                          <el-button 
                            :type="scope.row.status === '启用' ? 'danger' : 'success'"
                            link 
                            size="small"
                            @click="toggleEnterpriseStatus(scope.row)"
                          >
                            {{ scope.row.status === '启用' ? '禁用' : '启用' }}
                          </el-button>
                          <el-button type="warning" link size="small" @click="unbindEnterprise(scope.row)">
                            解绑
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="大麦" name="damai">
            <div class="tab-content">
              <!-- 搜索区 -->
              <div class="filter-container">
                <el-form :inline="true" :model="damaiSearch" class="search-form">
                  <el-form-item label="名称">
                    <el-input v-model="damaiSearch.name" placeholder="请输入名称" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="品牌">
                    <el-input v-model="damaiSearch.brand" placeholder="请输入品牌" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="状态">
                    <el-select v-model="damaiSearch.status" placeholder="请选择状态" clearable>
                      <el-option label="全部" value=""></el-option>
                      <el-option label="启用" value="启用"></el-option>
                      <el-option label="禁用" value="禁用"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="searchDamaiAgents">搜索</el-button>
                    <el-button @click="resetDamaiSearch">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>
              
              <!-- 大麦表格 -->
              <div class="table-content-container" v-loading="loading">
                <el-table :data="filteredDamaiAgents" style="width: 100%" border>
                  <el-table-column prop="name" label="名称" min-width="120"></el-table-column>
                  <el-table-column prop="avatar" label="头像" min-width="80" align="center">
                    <template #default="scope">
                      <el-avatar :src="scope.row.avatar" :alt="scope.row.name" size="small"></el-avatar>
                    </template>
                  </el-table-column>
                  <el-table-column prop="brand" label="品牌" min-width="100"></el-table-column>
                  <el-table-column prop="status" label="状态" min-width="100" align="center">
                    <template #default="scope">
                      <el-tag :type="scope.row.status === '启用' ? 'success' : 'warning'">
                        {{ scope.row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="createTime" label="创建时间" min-width="160">
                    <template #default="scope">
                      {{ formatDateTime(scope.row.createTime) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="120" align="center">
                    <template #default="scope">
                      <el-button 
                        :type="scope.row.status === '启用' ? 'danger' : 'success'"
                        link 
                        size="small"
                        @click="toggleDamaiAgentStatus(scope.row)"
                      >
                        {{ scope.row.status === '启用' ? '禁用' : '启用' }}
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

const activeTab = ref('xiaoyan')
const loading = ref(false)

// 小妍智能体数据
const xiaoyanAgents = ref([
  {
    id: 1,
    name: '小妍',
    avatar: 'https://via.placeholder.com/40x40?text=小妍',
    brand: '官方',
    status: '启用'
  },
  {
    id: 2,
    name: '小智',
    avatar: 'https://via.placeholder.com/40x40?text=小智',
    brand: '官方',
    status: '启用'
  },
  {
    id: 3,
    name: '小助手',
    avatar: 'https://via.placeholder.com/40x40?text=助手',
    brand: '官方',
    status: '禁用'
  }
])

// 小彩分身数据
const xiaocaiAvatars = ref([
  {
    id: 1,
    name: '小彩0001',
    image: 'https://via.placeholder.com/60x60?text=0001'
  },
  {
    id: 2,
    name: '小彩0002',
    image: 'https://via.placeholder.com/60x60?text=0002'
  },
  {
    id: 3,
    name: '小彩0003',
    image: 'https://via.placeholder.com/60x60?text=0003'
  },
  {
    id: 4,
    name: '小彩0004',
    image: 'https://via.placeholder.com/60x60?text=0004'
  }
])

const selectedAvatar = ref(1)
const avatarSearch = ref('')

// 小彩企业数据
const xiaocaiEnterprises = ref([
  {
    id: 1,
    name: '阿里巴巴集团',
    type: '设备商',
    groupName: '阿里技术交流群',
    status: '启用',
    bindTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    name: '中国石油',
    type: '供应商',
    groupName: '石油采购群',
    status: '启用',
    bindTime: '2024-01-20 14:20:00'
  },
  {
    id: 3,
    name: '微软中国',
    type: '设备商',
    groupName: '微软供应链群',
    status: '禁用',
    bindTime: '2024-02-01 09:15:00'
  }
])

// 小彩搜索条件
const xiaocaiSearch = ref({
  enterpriseName: '',
  enterpriseType: '',
  groupName: ''
})

// 大麦智能体数据
const damaiAgents = ref([
  {
    id: 1,
    name: '大麦助手',
    avatar: 'https://via.placeholder.com/40x40?text=大麦',
    brand: '自定义',
    status: '启用',
    createTime: '2024-01-10 08:00:00'
  },
  {
    id: 2,
    name: '采购专家',
    avatar: 'https://via.placeholder.com/40x40?text=采购',
    brand: '自定义',
    status: '启用',
    createTime: '2024-01-12 10:30:00'
  },
  {
    id: 3,
    name: '销售顾问',
    avatar: 'https://via.placeholder.com/40x40?text=销售',
    brand: '自定义',
    status: '禁用',
    createTime: '2024-01-15 16:45:00'
  }
])

// 大麦搜索条件
const damaiSearch = ref({
  name: '',
  brand: '',
  status: ''
})

// 计算属性：过滤后的小彩分身
const filteredAvatars = computed(() => {
  if (!avatarSearch.value) {
    return xiaocaiAvatars.value
  }
  return xiaocaiAvatars.value.filter(avatar => 
    avatar.name.toLowerCase().includes(avatarSearch.value.toLowerCase())
  )
})

// 计算属性：过滤后的小彩企业数据
const filteredXiaocaiEnterprises = computed(() => {
  return xiaocaiEnterprises.value.filter(enterprise => {
    return (!xiaocaiSearch.value.enterpriseName || 
            enterprise.name.includes(xiaocaiSearch.value.enterpriseName)) &&
           (!xiaocaiSearch.value.enterpriseType || 
            enterprise.type === xiaocaiSearch.value.enterpriseType) &&
           (!xiaocaiSearch.value.groupName || 
            enterprise.groupName.includes(xiaocaiSearch.value.groupName))
  })
})

// 计算属性：过滤后的大麦智能体数据
const filteredDamaiAgents = computed(() => {
  return damaiAgents.value.filter(agent => {
    return (!damaiSearch.value.name || 
            agent.name.includes(damaiSearch.value.name)) &&
           (!damaiSearch.value.brand || 
            agent.brand.includes(damaiSearch.value.brand)) &&
           (!damaiSearch.value.status || 
            agent.status === damaiSearch.value.status)
  })
})

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return ''
  const date = new Date(dateTimeStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 方法：搜索小彩分身
const handleAvatarSearch = () => {
  // 搜索逻辑通过计算属性自动实现
}

// 方法：选择小彩分身
const selectAvatar = (avatarId) => {
  selectedAvatar.value = avatarId
}

// 方法：切换智能体状态
const toggleAgentStatus = (agent) => {
  loading.value = true
  setTimeout(() => {
    agent.status = agent.status === '启用' ? '禁用' : '启用'
    loading.value = false
    ElMessage.success(`${agent.name} 已${agent.status}`)
  }, 500)
}

// 方法：切换企业状态
const toggleEnterpriseStatus = (enterprise) => {
  loading.value = true
  setTimeout(() => {
    enterprise.status = enterprise.status === '启用' ? '禁用' : '启用'
    loading.value = false
    ElMessage.success(`${enterprise.name} 已${enterprise.status}`)
  }, 500)
}

// 方法：解绑企业
const unbindEnterprise = (enterprise) => {
  ElMessageBox.confirm(
    `确定要解绑企业 "${enterprise.name}" 吗？`,
    '确认解绑',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    loading.value = true
    setTimeout(() => {
      const index = xiaocaiEnterprises.value.findIndex(e => e.id === enterprise.id)
      if (index > -1) {
        xiaocaiEnterprises.value.splice(index, 1)
      }
      loading.value = false
      ElMessage.success('解绑成功')
    }, 500)
  }).catch(() => {
    ElMessage.info('已取消解绑')
  })
}

// 方法：切换大麦智能体状态
const toggleDamaiAgentStatus = (agent) => {
  loading.value = true
  setTimeout(() => {
    agent.status = agent.status === '启用' ? '禁用' : '启用'
    loading.value = false
    ElMessage.success(`${agent.name} 已${agent.status}`)
  }, 500)
}

// 方法：搜索小彩企业
const searchXiaocaiEnterprises = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('搜索完成')
  }, 500)
}

// 方法：重置小彩搜索
const resetXiaocaiSearch = () => {
  xiaocaiSearch.value = {
    enterpriseName: '',
    enterpriseType: '',
    groupName: ''
  }
  ElMessage.info('搜索条件已重置')
}

// 方法：搜索大麦智能体
const searchDamaiAgents = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('搜索完成')
  }, 500)
}

// 方法：重置大麦搜索
const resetDamaiSearch = () => {
  damaiSearch.value = {
    name: '',
    brand: '',
    status: ''
  }
  ElMessage.info('搜索条件已重置')
}

onMounted(() => {
  console.log('智能体管理页面已加载')
})
</script>

<style scoped>
.agent-manage {
  margin: 20px;
}

.agent-manage-container {
  min-height: 600px;
}

.tabs-container {
  width: 100%;
}

.agent-tabs {
  width: 100%;
}

.tab-content {
}

/* 小彩布局样式 */
.xiaocai-layout {
  display: flex;
  gap: 20px;
  min-height: 500px;
}

.left-panel {
  width: 200px;
  background: #fafafa;
  border-radius: 4px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.panel-header h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.avatar-search {
  margin-bottom: 12px;
}

.avatar-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.avatar-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.avatar-item:hover {
  background: #ecf5ff;
}

.avatar-item.active {
  background: #ecf5ff;
  border-color: #409eff;
}

.avatar-name {
  font-size: 12px;
  color: #606266;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 搜索区域样式 */
.filter-container {
  margin-bottom: 20px;
}

.search-form {
  background: #fafafa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

/* 表格样式 */
.table-content-container {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .xiaocai-layout {
    flex-direction: column;
    gap: 16px;
  }
  
  .left-panel {
    width: 100%;
  }
  
  .avatar-list {
    flex-direction: row;
    flex-wrap: wrap;
  }
}
</style>