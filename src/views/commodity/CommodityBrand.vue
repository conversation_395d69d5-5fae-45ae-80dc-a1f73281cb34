<template>
  <div class="brand-management-container">
    <!-- 标签页 -->
    <el-tabs v-model="activeTab" class="brand-tabs">
      <el-tab-pane label="全部品牌" name="all"></el-tab-pane>
      <el-tab-pane label="审核记录" name="audit"></el-tab-pane>
    </el-tabs>

    <!-- 搜索筛选区域 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="品牌名称:">
          <el-input
            v-model="searchForm.brandName"
            placeholder="品牌名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="供应商:">
          <el-input
            v-model="searchForm.supplier"
            placeholder="供应商名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="产品分类:">
          <el-select
            v-model="searchForm.category"
            placeholder="商品分类"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="电子元器件" value="electronics"></el-option>
            <el-option label="机械配件" value="mechanical"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态:">
          <el-select
            v-model="searchForm.status"
            placeholder="状态"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="正常" value="normal"></el-option>
            <el-option label="待审核" value="pending"></el-option>
            <el-option label="已禁用" value="disabled"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增
      </el-button>
    </div>

    <!-- 品牌列表表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="paginatedBrands"
        v-loading="loading"
        style="width: 100%"
        border
      >
        <el-table-column prop="sequence" label="序号" width="80" align="center" />
        <el-table-column prop="brandName" label="品牌名称" min-width="120">
          <template #default="{ row }">
            <el-link type="primary" @click="handleViewDetail(row)">
              {{ row.brandName }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="brandEnglish" label="品牌英文" min-width="120" />
        <el-table-column prop="supplierCount" label="供应商数量" width="100" align="center">
          <template #default="{ row }">
            <el-link type="primary" @click="handleViewSuppliers(row)">
              {{ row.supplierCount }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="brandLogo" label="品牌logo" width="100" align="center">
          <template #default="{ row }">
            <el-image
              v-if="row.brandLogo"
              :src="row.brandLogo"
              :preview-src-list="[row.brandLogo]"
              style="width: 40px; height: 40px"
              fit="contain"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="brandInitial" label="品牌首字母" width="100" align="center" />
        <el-table-column prop="sortOrder" label="序号" width="80" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" @click="handleTradeSettings(row)">交易设置</el-button>
            <el-button type="primary" link @click="handleAlternativeSettings(row)">平替设置</el-button>
            <el-dropdown @command="(command) => handleMoreAction(command, row)">
              <el-button type="primary" link>
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">修改</el-dropdown-item>
                  <el-dropdown-item command="offline">下架</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 品牌详情弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="brandFormRef"
        :model="brandForm"
        :rules="brandRules"
        label-width="120px"
      >
        <el-form-item label="品牌名称" prop="brandName">
          <el-input v-model="brandForm.brandName" placeholder="请输入品牌名称" />
        </el-form-item>
        <el-form-item label="品牌英文" prop="brandEnglish">
          <el-input v-model="brandForm.brandEnglish" placeholder="请输入品牌英文名称" />
        </el-form-item>
        <el-form-item label="品牌首字母" prop="brandInitial">
          <el-input v-model="brandForm.brandInitial" placeholder="请输入品牌首字母" maxlength="1" />
        </el-form-item>
        <el-form-item label="品牌Logo" prop="brandLogo">
          <el-upload
            class="logo-uploader"
            :show-file-list="false"
            :on-success="handleLogoSuccess"
            :before-upload="beforeLogoUpload"
            action="/api/upload"
          >
            <img v-if="brandForm.brandLogo" :src="brandForm.brandLogo" class="logo" />
            <el-icon v-else class="logo-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="brandForm.sortOrder" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="brandForm.status">
            <el-radio label="normal">正常</el-radio>
            <el-radio label="disabled">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ArrowDown } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const activeTab = ref('all')
const dialogVisible = ref(false)
const dialogTitle = ref('')
const brandFormRef = ref()

// 搜索表单
const searchForm = reactive({
  brandName: '',
  supplier: '',
  category: '',
  status: ''
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 品牌表单
const brandForm = reactive({
  id: null,
  brandName: '',
  brandEnglish: '',
  brandInitial: '',
  brandLogo: '',
  sortOrder: 0,
  status: 'normal'
})

// 表单验证规则
const brandRules = {
  brandName: [
    { required: true, message: '请输入品牌名称', trigger: 'blur' }
  ],
  brandEnglish: [
    { required: true, message: '请输入品牌英文名称', trigger: 'blur' }
  ],
  brandInitial: [
    { required: true, message: '请输入品牌首字母', trigger: 'blur' },
    { pattern: /^[A-Za-z]$/, message: '请输入单个英文字母', trigger: 'blur' }
  ]
}

// 模拟品牌数据
const brandList = ref([
  {
    id: 1,
    sequence: 1,
    brandName: '研选测试',
    brandEnglish: 'YXCS',
    supplierCount: 3,
    brandLogo: '/api/placeholder/logo1.png',
    brandInitial: 'Y',
    sortOrder: 0,
    status: 'normal',
    createTime: '2025-07-22 14:48'
  },
  {
    id: 2,
    sequence: 2,
    brandName: 'SMC',
    brandEnglish: 'SMC',
    supplierCount: 3,
    brandLogo: '/api/placeholder/smc-logo.png',
    brandInitial: 'S',
    sortOrder: 0,
    status: 'normal',
    createTime: '2025-04-15 09:29'
  },
  {
    id: 3,
    sequence: 3,
    brandName: '三立期',
    brandEnglish: 'SMS',
    supplierCount: 2,
    brandLogo: '/api/placeholder/sms-logo.png',
    brandInitial: 'S',
    sortOrder: 0,
    status: 'normal',
    createTime: '2025-04-16 17:09'
  },
  {
    id: 4,
    sequence: 4,
    brandName: '上银',
    brandEnglish: 'HIWIN',
    supplierCount: 2,
    brandLogo: '/api/placeholder/hiwin-logo.png',
    brandInitial: 'S',
    sortOrder: 0,
    status: 'normal',
    createTime: '2025-04-15 09:38'
  },
  {
    id: 5,
    sequence: 5,
    brandName: '研祥',
    brandEnglish: 'Yanxiang',
    supplierCount: 1,
    brandLogo: '/api/placeholder/yanxiang-logo.png',
    brandInitial: 'Y',
    sortOrder: 0,
    status: 'normal',
    createTime: '2025-08-25 16:24'
  },
  {
    id: 6,
    sequence: 6,
    brandName: '凌华',
    brandEnglish: 'Linghua',
    supplierCount: 1,
    brandLogo: '/api/placeholder/linghua-logo.png',
    brandInitial: 'L',
    sortOrder: 0,
    status: 'normal',
    createTime: '2025-08-25 16:17'
  }
])

// 计算属性 - 过滤后的品牌列表
const filteredBrands = computed(() => {
  let filtered = brandList.value

  if (searchForm.brandName) {
    filtered = filtered.filter(brand =>
      brand.brandName.toLowerCase().includes(searchForm.brandName.toLowerCase()) ||
      brand.brandEnglish.toLowerCase().includes(searchForm.brandName.toLowerCase())
    )
  }

  if (searchForm.supplier) {
    // 这里可以根据实际需求过滤供应商
    filtered = filtered.filter(brand => brand.supplierCount > 0)
  }

  if (searchForm.status) {
    filtered = filtered.filter(brand => brand.status === searchForm.status)
  }

  pagination.total = filtered.length
  return filtered
})

// 计算属性 - 分页后的品牌列表
const paginatedBrands = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredBrands.value.slice(start, end)
})

// 工具方法
const getStatusType = (status) => {
  const statusMap = {
    'normal': 'success',
    'pending': 'warning',
    'disabled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'normal': '正常',
    'pending': '待审核',
    'disabled': '已禁用'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return dateStr
}

// 事件处理方法
const handleSearch = () => {
  pagination.currentPage = 1
  // 搜索逻辑已在计算属性中处理
}

const handleReset = () => {
  Object.assign(searchForm, {
    brandName: '',
    supplier: '',
    category: '',
    status: ''
  })
  pagination.currentPage = 1
}

const handleAdd = () => {
  dialogTitle.value = '新增品牌'
  Object.assign(brandForm, {
    id: null,
    brandName: '',
    brandEnglish: '',
    brandInitial: '',
    brandLogo: '',
    sortOrder: 0,
    status: 'normal'
  })
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑品牌'
  Object.assign(brandForm, { ...row })
  dialogVisible.value = true
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除品牌"${row.brandName}"吗？此操作不可恢复！`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = brandList.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      brandList.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 新的操作方法
const handleTradeSettings = (row) => {
  ElMessage.info(`配置交易设置: ${row.brandName}`)
  // 这里可以跳转到交易设置页面或打开交易设置弹窗
}

const handleAlternativeSettings = (row) => {
  ElMessage.info(`配置平替设置: ${row.brandName}`)
  // 这里可以跳转到平替设置页面或打开平替设置弹窗
}

const handleMoreAction = (command, row) => {
  switch (command) {
    case 'edit':
      handleEdit(row)
      break
    case 'offline':
      handleOffline(row)
      break
    case 'delete':
      handleDelete(row)
      break
    default:
      break
  }
}

const handleOffline = (row) => {
  ElMessageBox.confirm(
    `确定要下架品牌"${row.brandName}"吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 这里处理下架逻辑
    ElMessage.success('下架成功')
  }).catch(() => {
    ElMessage.info('已取消下架')
  })
}

const handleViewDetail = (row) => {
  ElMessage.info(`查看品牌详情: ${row.brandName}`)
}

const handleViewSuppliers = (row) => {
  ElMessage.info(`查看供应商列表: ${row.brandName}`)
}

const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.currentPage = 1
}

const handleCurrentChange = (val) => {
  pagination.currentPage = val
}

const handleDialogClose = () => {
  if (brandFormRef.value) {
    brandFormRef.value.resetFields()
  }
}

const handleSubmit = async () => {
  if (!brandFormRef.value) return

  try {
    await brandFormRef.value.validate()

    if (brandForm.id) {
      // 编辑
      const index = brandList.value.findIndex(item => item.id === brandForm.id)
      if (index > -1) {
        Object.assign(brandList.value[index], { ...brandForm })
        ElMessage.success('编辑成功')
      }
    } else {
      // 新增
      const newBrand = {
        ...brandForm,
        id: Date.now(),
        sequence: brandList.value.length + 1,
        supplierCount: 0,
        createTime: new Date().toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }).replace(/\//g, '-')
      }
      brandList.value.unshift(newBrand)
      ElMessage.success('新增成功')
    }

    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleLogoSuccess = (_response, file) => {
  brandForm.brandLogo = URL.createObjectURL(file.raw)
}

const beforeLogoUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

// 初始化
onMounted(() => {
  pagination.total = brandList.value.length
})
</script>

<style scoped>
.brand-management-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 50px);
}

.brand-tabs {
  background: white;
  padding: 0 20px;
  margin-bottom: 20px;
  border-radius: 4px;
}

:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  font-size: 16px;
}

:deep(.el-tabs__item.is-active) {
  color: #f94c30;
  border-bottom: 2px solid #f94c30;
}

.filter-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 20px 0;
}

.search-form .el-form-item {
  margin-bottom: 16px;
}

.search-form .el-form-item__label {
  font-weight: 500;
  color: #333;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.table-card {
  background: white;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* Logo上传样式 */
.logo-uploader .logo {
  width: 80px;
  height: 80px;
  display: block;
  object-fit: contain;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.logo-uploader .logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.2s;
}

.logo-uploader .logo-uploader-icon:hover {
  border-color: #409eff;
  color: #409eff;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #333;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table .el-button--text) {
  padding: 0;
  margin-right: 8px;
}

:deep(.el-table .el-button--text:last-child) {
  margin-right: 0;
}

/* 状态标签样式 */
:deep(.el-tag) {
  border: none;
  font-weight: 500;
}

:deep(.el-tag--success) {
  background-color: #f0f9ff;
  color: #1890ff;
}

:deep(.el-tag--warning) {
  background-color: #fff7e6;
  color: #fa8c16;
}

:deep(.el-tag--danger) {
  background-color: #fff2f0;
  color: #ff4d4f;
}

/* 对话框样式 */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #ebeef5;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__inner) {
  border-radius: 4px;
}

:deep(.el-select .el-input__inner) {
  border-radius: 4px;
}

/* 按钮样式 */
.el-button--primary {
  background-color: #f94c30;
  border-color: #f94c30;
}

.el-button--primary:hover {
  background-color: #fa6d4f;
  border-color: #fa6d4f;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .brand-management-container {
    padding: 10px;
  }

  .search-form .el-form-item {
    width: 100%;
  }

  .search-form .el-input,
  .search-form .el-select {
    width: 100% !important;
  }
}
</style>