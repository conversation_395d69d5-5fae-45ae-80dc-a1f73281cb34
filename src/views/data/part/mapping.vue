<template>
  <div class="mapping-container">
    <!-- 任务信息 -->
    <el-card class="task-info-card">
      <div class="task-info-header">
        <h3>字段映射 - {{ currentTask?.jobId }}</h3>
        <div class="task-actions">
          <el-button @click="loadTemplate">加载模板</el-button>
          <el-button @click="saveTemplate">保存模板</el-button>
          <el-button type="primary" @click="confirmMapping" :disabled="!canConfirm">
            确认映射并开始解析
          </el-button>
        </div>
      </div>

      <el-descriptions :column="4" border size="small">
        <el-descriptions-item label="任务ID">{{ currentTask?.jobId }}</el-descriptions-item>
        <el-descriptions-item label="文件名">{{ currentTask?.fileName }}</el-descriptions-item>
        <el-descriptions-item label="来源类型">
          <el-tag :type="getSourceTypeColor(currentTask?.sourceType)">
            {{ getSourceTypeText(currentTask?.sourceType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="总行数">{{ currentTask?.totalRows }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 映射配置 -->
    <el-card class="mapping-card">
      <div class="mapping-header">
        <h4>字段映射配置</h4>
        <div class="mapping-actions">
          <el-button size="small" @click="autoMapping">智能映射</el-button>
          <el-button size="small" @click="clearMapping">清空映射</el-button>
        </div>
      </div>

      <div class="mapping-content">
        <!-- 左侧：原始字段 -->
        <div class="left-panel">
          <div class="panel-header">
            <h5>原始字段</h5>
            <span class="field-count">共 {{ originalFields.length }} 个字段</span>
          </div>

          <div class="fields-list">
            <div
              v-for="(field, index) in originalFields"
              :key="index"
              class="field-item"
              :class="{ 'mapped': field.mapped }"
            >
              <div class="field-info">
                <div class="field-name">{{ field.name }}</div>
                <div class="field-samples">
                  <span
                    v-for="(sample, sIndex) in field.samples.slice(0, 3)"
                    :key="sIndex"
                    class="sample-value"
                  >
                    {{ sample }}
                  </span>
                  <span v-if="field.samples.length > 3" class="more-samples">
                    +{{ field.samples.length - 3 }}
                  </span>
                </div>
              </div>

              <div class="field-actions">
                <el-button
                  size="small"
                  type="primary"
                  link
                  @click="showFieldDetail(field)"
                >
                  详情
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：标准字段映射 -->
        <div class="right-panel">
          <div class="panel-header">
            <h5>标准字段映射</h5>
            <span class="mapping-count">已映射 {{ mappedCount }} / {{ standardFields.length }}</span>
          </div>

          <div class="standard-fields">
            <div
              v-for="standardField in standardFields"
              :key="standardField.key"
              class="standard-field-item"
              :class="{ 'required': standardField.required, 'mapped': standardField.mapped }"
            >
              <div class="standard-field-info">
                <div class="standard-field-name">
                  {{ standardField.name }}
                  <el-tag v-if="standardField.required" type="danger" size="small">必填</el-tag>
                </div>
                <div class="standard-field-desc">{{ standardField.description }}</div>
              </div>

              <div class="mapping-selector">
                <el-select
                  v-model="standardField.mappedTo"
                  placeholder="选择原始字段"
                  clearable
                  @change="handleMappingChange(standardField)"
                  style="width: 200px"
                >
                  <el-option
                    v-for="originalField in originalFields"
                    :key="originalField.name"
                    :label="originalField.name"
                    :value="originalField.name"
                    :disabled="originalField.mapped && originalField.mappedTo !== standardField.key"
                  >
                    <div class="option-content">
                      <span class="option-name">{{ originalField.name }}</span>
                      <span class="option-samples">
                        {{ originalField.samples.slice(0, 2).join(', ') }}
                      </span>
                    </div>
                  </el-option>
                </el-select>
              </div>

              <!-- 映射预览 -->
              <div v-if="standardField.mappedTo" class="mapping-preview">
                <div class="preview-samples">
                  <span
                    v-for="(sample, index) in getMappedSamples(standardField.mappedTo)"
                    :key="index"
                    class="preview-sample"
                  >
                    {{ sample }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 映射预览 -->
    <el-card class="preview-card">
      <div class="preview-header">
        <h4>映射预览</h4>
        <div class="preview-actions">
          <el-button size="small" @click="refreshPreview">刷新预览</el-button>
        </div>
      </div>

      <el-table :data="previewData" border size="small" max-height="300">
        <el-table-column
          v-for="field in mappedStandardFields"
          :key="field.key"
          :prop="field.key"
          :label="field.name"
          min-width="120"
          show-overflow-tooltip
        >
          <template #header>
            <div class="preview-header-cell">
              <span>{{ field.name }}</span>
              <el-tag v-if="field.required" type="danger" size="small">必填</el-tag>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 模板管理对话框 -->
    <el-dialog
      v-model="showTemplateDialog"
      :title="templateDialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="templateDialogMode === 'load'">
        <el-form :model="templateForm" label-width="100px">
          <el-form-item label="模板类型">
            <el-select v-model="templateForm.type" placeholder="请选择模板类型">
              <el-option label="客户BOM模板" value="customer_bom" />
              <el-option label="供应商目录模板" value="supplier_catalog" />
              <el-option label="爬虫数据模板" value="crawler_data" />
            </el-select>
          </el-form-item>
          <el-form-item label="选择模板">
            <el-select v-model="templateForm.templateId" placeholder="请选择模板">
              <el-option
                v-for="template in availableTemplates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <div v-else-if="templateDialogMode === 'save'">
        <el-form :model="templateForm" :rules="templateRules" ref="templateFormRef" label-width="100px">
          <el-form-item label="模板名称" prop="name">
            <el-input v-model="templateForm.name" placeholder="请输入模板名称" />
          </el-form-item>
          <el-form-item label="模板类型" prop="type">
            <el-select v-model="templateForm.type" placeholder="请选择模板类型">
              <el-option label="客户BOM模板" value="customer_bom" />
              <el-option label="供应商目录模板" value="supplier_catalog" />
              <el-option label="爬虫数据模板" value="crawler_data" />
            </el-select>
          </el-form-item>
          <el-form-item label="模板描述">
            <el-input
              v-model="templateForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入模板描述"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTemplateDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="templateDialogMode === 'load' ? applyTemplate() : saveTemplateConfirm()"
          >
            {{ templateDialogMode === 'load' ? '应用模板' : '保存模板' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 字段详情对话框 -->
    <el-dialog
      v-model="showFieldDetailDialog"
      title="字段详情"
      width="500px"
    >
      <div v-if="currentField">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="字段名称">{{ currentField.name }}</el-descriptions-item>
          <el-descriptions-item label="数据类型">{{ currentField.dataType }}</el-descriptions-item>
          <el-descriptions-item label="非空值数量">{{ currentField.nonNullCount }}</el-descriptions-item>
          <el-descriptions-item label="唯一值数量">{{ currentField.uniqueCount }}</el-descriptions-item>
        </el-descriptions>

        <div class="field-samples-section">
          <h5>样本数据 (前20个)</h5>
          <div class="samples-grid">
            <el-tag
              v-for="(sample, index) in currentField.samples.slice(0, 20)"
              :key="index"
              class="sample-tag"
            >
              {{ sample }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const showTemplateDialog = ref(false)
const showFieldDetailDialog = ref(false)
const templateDialogMode = ref('load') // 'load' | 'save'
const currentField = ref(null)

// 当前任务信息
const currentTask = ref({
  jobId: 'JOB_20241201_001',
  fileName: '客户A_BOM清单_20241201.xlsx',
  sourceType: 'customer_bom',
  totalRows: 1250
})

// 原始字段数据
const originalFields = ref([
  {
    name: '品牌',
    samples: ['Intel', 'AMD', 'NVIDIA', 'Qualcomm', 'Apple'],
    dataType: 'string',
    nonNullCount: 1200,
    uniqueCount: 45,
    mapped: false,
    mappedTo: null
  },
  {
    name: '型号',
    samples: ['i7-12700K', 'Ryzen 7 5800X', 'RTX 4080', 'Snapdragon 8 Gen 2'],
    dataType: 'string',
    nonNullCount: 1180,
    uniqueCount: 980,
    mapped: false,
    mappedTo: null
  },
  {
    name: '数量',
    samples: ['10', '5', '20', '1', '100'],
    dataType: 'number',
    nonNullCount: 1250,
    uniqueCount: 156,
    mapped: false,
    mappedTo: null
  },
  {
    name: '单位',
    samples: ['PCS', '个', 'EA', 'SET'],
    dataType: 'string',
    nonNullCount: 1100,
    uniqueCount: 8,
    mapped: false,
    mappedTo: null
  },
  {
    name: '单价',
    samples: ['2500.00', '2200.00', '8999.99', '1299.00'],
    dataType: 'number',
    nonNullCount: 980,
    uniqueCount: 890,
    mapped: false,
    mappedTo: null
  },
  {
    name: '币种',
    samples: ['CNY', 'USD', 'EUR'],
    dataType: 'string',
    nonNullCount: 950,
    uniqueCount: 3,
    mapped: false,
    mappedTo: null
  },
  {
    name: '备注',
    samples: ['原装正品', '散装', '工程样品', ''],
    dataType: 'string',
    nonNullCount: 600,
    uniqueCount: 120,
    mapped: false,
    mappedTo: null
  }
])

// 标准字段定义
const standardFields = ref([
  {
    key: 'brand',
    name: '品牌',
    description: '零部件品牌名称，如Intel、AMD等',
    required: true,
    mapped: false,
    mappedTo: null
  },
  {
    key: 'model',
    name: '型号',
    description: '零部件具体型号，如i7-12700K',
    required: true,
    mapped: false,
    mappedTo: null
  },
  {
    key: 'quantity',
    name: '数量',
    description: '需求数量',
    required: true,
    mapped: false,
    mappedTo: null
  },
  {
    key: 'unit',
    name: '单位',
    description: '数量单位，如PCS、个、套等',
    required: false,
    mapped: false,
    mappedTo: null
  },
  {
    key: 'price',
    name: '单价',
    description: '单个零部件价格',
    required: false,
    mapped: false,
    mappedTo: null
  },
  {
    key: 'currency',
    name: '币种',
    description: '价格币种，如CNY、USD等',
    required: false,
    mapped: false,
    mappedTo: null
  },
  {
    key: 'specification',
    name: '规格参数',
    description: '零部件技术规格参数',
    required: false,
    mapped: false,
    mappedTo: null
  },
  {
    key: 'manufacturer',
    name: '制造商',
    description: '零部件制造商',
    required: false,
    mapped: false,
    mappedTo: null
  },
  {
    key: 'category',
    name: '分类',
    description: '零部件分类',
    required: false,
    mapped: false,
    mappedTo: null
  },
  {
    key: 'remarks',
    name: '备注',
    description: '其他备注信息',
    required: false,
    mapped: false,
    mappedTo: null
  }
])

// 模板相关数据
const templateForm = reactive({
  type: '',
  templateId: '',
  name: '',
  description: ''
})

const templateFormRef = ref()

const templateRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择模板类型', trigger: 'change' }
  ]
}

const availableTemplates = ref([
  { id: 'tpl_001', name: '标准客户BOM模板', type: 'customer_bom' },
  { id: 'tpl_002', name: '电子元器件供应商模板', type: 'supplier_catalog' },
  { id: 'tpl_003', name: '机械零件供应商模板', type: 'supplier_catalog' }
])

// 计算属性
const mappedCount = computed(() => {
  return standardFields.value.filter(field => field.mapped).length
})

const canConfirm = computed(() => {
  const requiredFields = standardFields.value.filter(field => field.required)
  return requiredFields.every(field => field.mapped)
})

const mappedStandardFields = computed(() => {
  return standardFields.value.filter(field => field.mapped)
})

const templateDialogTitle = computed(() => {
  return templateDialogMode.value === 'load' ? '加载映射模板' : '保存映射模板'
})

const previewData = computed(() => {
  // 模拟预览数据
  return [
    {
      brand: 'Intel',
      model: 'i7-12700K',
      quantity: '10',
      unit: 'PCS',
      price: '2500.00',
      currency: 'CNY'
    },
    {
      brand: 'AMD',
      model: 'Ryzen 7 5800X',
      quantity: '5',
      unit: 'PCS',
      price: '2200.00',
      currency: 'CNY'
    }
  ]
})

// 工具方法
const getSourceTypeText = (type) => {
  const map = {
    'customer_bom': '客户BOM',
    'supplier_catalog': '供应商目录',
    'crawler_data': '爬虫数据',
    'manual_input': '手工录入'
  }
  return map[type] || type
}

const getSourceTypeColor = (type) => {
  const map = {
    'customer_bom': 'primary',
    'supplier_catalog': 'success',
    'crawler_data': 'warning',
    'manual_input': 'info'
  }
  return map[type] || ''
}

const getMappedSamples = (originalFieldName) => {
  const field = originalFields.value.find(f => f.name === originalFieldName)
  return field ? field.samples.slice(0, 3) : []
}

// 事件处理方法
const handleMappingChange = (standardField) => {
  // 清除之前的映射
  const previousField = originalFields.value.find(f => f.mappedTo === standardField.key)
  if (previousField) {
    previousField.mapped = false
    previousField.mappedTo = null
  }

  // 设置新的映射
  if (standardField.mappedTo) {
    const originalField = originalFields.value.find(f => f.name === standardField.mappedTo)
    if (originalField) {
      originalField.mapped = true
      originalField.mappedTo = standardField.key
      standardField.mapped = true
    }
  } else {
    standardField.mapped = false
  }
}

const autoMapping = () => {
  ElMessage.info('正在执行智能映射...')

  // 模拟智能映射逻辑
  setTimeout(() => {
    // 清除现有映射
    clearMapping()

    // 基于字段名称的智能匹配
    const mappingRules = [
      { standard: 'brand', original: '品牌' },
      { standard: 'model', original: '型号' },
      { standard: 'quantity', original: '数量' },
      { standard: 'unit', original: '单位' },
      { standard: 'price', original: '单价' },
      { standard: 'currency', original: '币种' },
      { standard: 'remarks', original: '备注' }
    ]

    mappingRules.forEach(rule => {
      const standardField = standardFields.value.find(f => f.key === rule.standard)
      const originalField = originalFields.value.find(f => f.name === rule.original)

      if (standardField && originalField) {
        standardField.mappedTo = originalField.name
        standardField.mapped = true
        originalField.mapped = true
        originalField.mappedTo = standardField.key
      }
    })

    ElMessage.success('智能映射完成')
  }, 1000)
}

const clearMapping = () => {
  standardFields.value.forEach(field => {
    field.mapped = false
    field.mappedTo = null
  })

  originalFields.value.forEach(field => {
    field.mapped = false
    field.mappedTo = null
  })

  ElMessage.info('映射已清空')
}

const showFieldDetail = (field) => {
  currentField.value = field
  showFieldDetailDialog.value = true
}

const loadTemplate = () => {
  templateDialogMode.value = 'load'
  templateForm.type = currentTask.value.sourceType
  showTemplateDialog.value = true
}

const saveTemplate = () => {
  if (mappedCount.value === 0) {
    ElMessage.warning('请先配置字段映射')
    return
  }

  templateDialogMode.value = 'save'
  templateForm.type = currentTask.value.sourceType
  templateForm.name = ''
  templateForm.description = ''
  showTemplateDialog.value = true
}

const applyTemplate = () => {
  if (!templateForm.templateId) {
    ElMessage.warning('请选择要应用的模板')
    return
  }

  ElMessage.info('正在应用模板...')

  // 模拟应用模板
  setTimeout(() => {
    autoMapping() // 使用智能映射作为模板应用的示例
    showTemplateDialog.value = false
    ElMessage.success('模板应用成功')
  }, 1000)
}

const saveTemplateConfirm = async () => {
  if (!templateFormRef.value) return

  try {
    await templateFormRef.value.validate()

    ElMessage.info('正在保存模板...')

    // 模拟保存模板
    setTimeout(() => {
      showTemplateDialog.value = false
      ElMessage.success('模板保存成功')
    }, 1000)

  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const refreshPreview = () => {
  ElMessage.info('预览数据已刷新')
}

const confirmMapping = () => {
  if (!canConfirm.value) {
    ElMessage.warning('请完成所有必填字段的映射')
    return
  }

  ElMessageBox.confirm(
    '确认映射配置并开始解析数据？解析过程可能需要一些时间。',
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    ElMessage.success('映射确认成功，正在开始解析...')
    // 跳转到任务列表页面
    router.push('/data/part/jobs')
  }).catch(() => {
    ElMessage.info('已取消操作')
  })
}

onMounted(() => {
  // 从URL参数获取任务ID
  const jobId = route.query.jobId
  if (jobId) {
    currentTask.value.jobId = jobId
  }
})
</script>

<style scoped>
.mapping-container {
  margin: 20px;
}

.task-info-card,
.mapping-card,
.preview-card {
  margin-bottom: 20px;
}

.task-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.task-info-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.mapping-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.mapping-actions {
  display: flex;
  gap: 8px;
}

.mapping-content {
  display: flex;
  gap: 20px;
  min-height: 500px;
}

.left-panel,
.right-panel {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f5f7fa;
}

.panel-header h5 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.field-count,
.mapping-count {
  font-size: 12px;
  color: #909399;
}

.fields-list {
  padding: 8px;
  max-height: 450px;
  overflow-y: auto;
}

.field-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  transition: all 0.3s;
}

.field-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.field-item.mapped {
  border-color: #67c23a;
  background: #f0f9ff;
}

.field-info {
  flex: 1;
}

.field-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.field-samples {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.sample-value {
  display: inline-block;
  padding: 2px 6px;
  background: #f0f2f5;
  border-radius: 2px;
  font-size: 11px;
  color: #606266;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.more-samples {
  font-size: 11px;
  color: #909399;
}

.standard-fields {
  padding: 8px;
  max-height: 450px;
  overflow-y: auto;
}

.standard-field-item {
  padding: 16px;
  margin-bottom: 12px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  transition: all 0.3s;
}

.standard-field-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.standard-field-item.required {
  border-left: 4px solid #f56c6c;
}

.standard-field-item.mapped {
  border-color: #67c23a;
  background: #f0f9ff;
}

.standard-field-info {
  margin-bottom: 12px;
}

.standard-field-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.standard-field-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.mapping-selector {
  margin-bottom: 8px;
}

.option-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.option-name {
  font-size: 13px;
  color: #303133;
}

.option-samples {
  font-size: 11px;
  color: #909399;
}

.mapping-preview {
  padding: 8px;
  background: #f0f9ff;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}

.preview-samples {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.preview-sample {
  display: inline-block;
  padding: 2px 6px;
  background: #ecf5ff;
  border-radius: 2px;
  font-size: 11px;
  color: #409eff;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-header-cell {
  display: flex;
  align-items: center;
  gap: 4px;
}

.field-samples-section {
  margin-top: 16px;
}

.field-samples-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.samples-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.sample-tag {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .mapping-content {
    flex-direction: column;
  }

  .left-panel,
  .right-panel {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .mapping-container {
    margin: 10px;
  }

  .task-info-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .mapping-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .preview-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>