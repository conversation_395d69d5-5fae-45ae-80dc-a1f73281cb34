<template>
  <div class="raw-records-container">
    <!-- 搜索过滤区域 -->
    <el-card class="filter-card">
      <div class="filter-header">
        <h3>原始记录查询</h3>
        <div class="view-toggle">
          <el-radio-group v-model="viewMode" @change="handleViewModeChange">
            <el-radio-button label="latest">最新版本</el-radio-button>
            <el-radio-button label="history">历史版本</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="记录ID">
          <el-input v-model="searchForm.recordId" placeholder="请输入记录ID" clearable />
        </el-form-item>
        <el-form-item label="品牌">
          <el-input v-model="searchForm.brand" placeholder="请输入品牌名称" clearable />
        </el-form-item>
        <el-form-item label="型号">
          <el-input v-model="searchForm.model" placeholder="请输入型号" clearable />
        </el-form-item>
        <el-form-item label="任务ID">
          <el-input v-model="searchForm.jobId" placeholder="请输入任务ID" clearable />
        </el-form-item>
        <el-form-item label="来源类型">
          <el-select v-model="searchForm.sourceType" placeholder="请选择来源类型" clearable>
            <el-option label="全部" value="" />
            <el-option label="客户BOM" value="customer_bom" />
            <el-option label="供应商目录" value="supplier_catalog" />
            <el-option label="爬虫数据" value="crawler_data" />
            <el-option label="手工录入" value="manual_input" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据类型">
          <el-select v-model="searchForm.dataType" placeholder="请选择数据类型" clearable>
            <el-option label="全部" value="" />
            <el-option label="SKU级别" value="sku" />
            <el-option label="SPU参数空间" value="spu_params" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchRecords">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button @click="exportRecords">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计信息 -->
    <el-card class="stats-card">
      <div class="stats-content">
        <div class="stat-item">
          <div class="stat-value">{{ stats.totalRecords }}</div>
          <div class="stat-label">总记录数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ stats.skuRecords }}</div>
          <div class="stat-label">SKU记录</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ stats.spuRecords }}</div>
          <div class="stat-label">SPU参数空间</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ stats.uniqueBrands }}</div>
          <div class="stat-label">品牌数量</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ stats.latestVersion }}</div>
          <div class="stat-label">最新版本</div>
        </div>
      </div>
    </el-card>

    <!-- 记录列表 -->
    <el-card class="table-card">
      <div class="table-header">
        <h4>记录列表</h4>
        <div class="table-actions">
          <el-button size="small" @click="refreshRecords">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <el-table
        :data="recordsList"
        v-loading="loading"
        border
        style="width: 100%"
        @row-click="viewRecordDetail"
        row-class-name="clickable-row"
      >
        <el-table-column prop="recordId" label="记录ID" width="140" />
        <el-table-column prop="fingerprint" label="指纹" width="120" show-overflow-tooltip />
        <el-table-column prop="version" label="版本" width="80" align="center">
          <template #default="scope">
            <el-tag size="small" :type="scope.row.version === scope.row.latestVersion ? 'success' : 'info'">
              v{{ scope.row.version }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="dataType" label="数据类型" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.dataType === 'sku' ? 'primary' : 'warning'" size="small">
              {{ scope.row.dataType === 'sku' ? 'SKU级别' : 'SPU参数空间' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="brandOriginal" label="品牌(原文)" min-width="120" show-overflow-tooltip />
        <el-table-column prop="modelOriginal" label="型号(原文)" min-width="150" show-overflow-tooltip />
        <el-table-column prop="quantity" label="数量" width="80" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="sourceType" label="来源" width="100">
          <template #default="scope">
            <el-tag :type="getSourceTypeColor(scope.row.sourceType)" size="small">
              {{ getSourceTypeText(scope.row.sourceType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="jobId" label="任务ID" width="140" />
        <el-table-column prop="createdAt" label="创建时间" width="160" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button link type="primary" size="small" @click.stop="viewRecordDetail(scope.row)">
              详情
            </el-button>
            <el-button
              link
              type="info"
              size="small"
              @click.stop="viewVersionHistory(scope.row)"
              v-if="scope.row.totalVersions > 1"
            >
              历史
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 记录详情抽屉 -->
    <el-drawer
      v-model="showDetailDrawer"
      title="记录详情"
      size="60%"
      :close-on-click-modal="false"
    >
      <div v-if="currentRecord" class="record-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h4>基本信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="记录ID">{{ currentRecord.recordId }}</el-descriptions-item>
            <el-descriptions-item label="指纹">{{ currentRecord.fingerprint }}</el-descriptions-item>
            <el-descriptions-item label="当前版本">
              <el-tag :type="currentRecord.version === currentRecord.latestVersion ? 'success' : 'info'">
                v{{ currentRecord.version }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="最新版本">v{{ currentRecord.latestVersion }}</el-descriptions-item>
            <el-descriptions-item label="数据类型">
              <el-tag :type="currentRecord.dataType === 'sku' ? 'primary' : 'warning'">
                {{ currentRecord.dataType === 'sku' ? 'SKU级别' : 'SPU参数空间' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="来源类型">
              <el-tag :type="getSourceTypeColor(currentRecord.sourceType)">
                {{ getSourceTypeText(currentRecord.sourceType) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="任务ID">{{ currentRecord.jobId }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ currentRecord.createdAt }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 字段对照 -->
        <div class="detail-section">
          <h4>字段对照</h4>
          <el-table :data="currentRecord.fieldComparison" border size="small">
            <el-table-column prop="fieldName" label="字段名称" width="120" />
            <el-table-column prop="originalValue" label="原始值" min-width="150">
              <template #default="scope">
                <span class="original-value">{{ scope.row.originalValue }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="parsedValue" label="解析值" min-width="150">
              <template #default="scope">
                <span class="parsed-value">{{ scope.row.parsedValue }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="confidence" label="置信度" width="100">
              <template #default="scope">
                <el-progress
                  :percentage="scope.row.confidence"
                  :stroke-width="6"
                  :show-text="false"
                  :color="getConfidenceColor(scope.row.confidence)"
                />
                <span class="confidence-text">{{ scope.row.confidence }}%</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- SPU参数空间 (仅当dataType为spu_params时显示) -->
        <div v-if="currentRecord.dataType === 'spu_params'" class="detail-section">
          <h4>参数空间定义</h4>
          <div class="param-space-info">
            <el-descriptions :column="2" border size="small">
              <el-descriptions-item label="参数维度数">{{ currentRecord.paramSpace?.dimensions || 0 }}</el-descriptions-item>
              <el-descriptions-item label="组合规模估计">{{ currentRecord.paramSpace?.estimatedCombinations || 0 }}</el-descriptions-item>
            </el-descriptions>

            <div class="param-definitions">
              <h5>参数定义</h5>
              <el-table :data="currentRecord.paramSpace?.parameters || []" border size="small">
                <el-table-column prop="name" label="参数名" width="120" />
                <el-table-column prop="type" label="类型" width="100" />
                <el-table-column prop="values" label="取值域" min-width="200">
                  <template #default="scope">
                    <el-tag
                      v-for="value in scope.row.values.slice(0, 5)"
                      :key="value"
                      size="small"
                      class="param-value-tag"
                    >
                      {{ value }}
                    </el-tag>
                    <span v-if="scope.row.values.length > 5" class="more-values">
                      +{{ scope.row.values.length - 5 }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="constraints" label="约束条件" min-width="150" />
              </el-table>
            </div>
          </div>
        </div>

        <!-- 溯源信息 -->
        <div class="detail-section">
          <h4>溯源信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="上传人">{{ currentRecord.traceInfo?.uploader }}</el-descriptions-item>
            <el-descriptions-item label="文件名">{{ currentRecord.traceInfo?.fileName }}</el-descriptions-item>
            <el-descriptions-item label="文件哈希">{{ currentRecord.traceInfo?.fileHash }}</el-descriptions-item>
            <el-descriptions-item label="行号">{{ currentRecord.traceInfo?.lineNumber }}</el-descriptions-item>
            <el-descriptions-item label="导入批次">{{ currentRecord.traceInfo?.batchId }}</el-descriptions-item>
            <el-descriptions-item label="处理时间">{{ currentRecord.traceInfo?.processedAt }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 版本历史 -->
        <div v-if="currentRecord.totalVersions > 1" class="detail-section">
          <h4>版本历史</h4>
          <el-timeline>
            <el-timeline-item
              v-for="version in currentRecord.versionHistory"
              :key="version.version"
              :timestamp="version.createdAt"
              :type="version.version === currentRecord.latestVersion ? 'primary' : 'info'"
            >
              <div class="version-item">
                <div class="version-header">
                  <span class="version-number">v{{ version.version }}</span>
                  <el-tag
                    size="small"
                    :type="version.version === currentRecord.latestVersion ? 'success' : 'info'"
                  >
                    {{ version.version === currentRecord.latestVersion ? '最新' : '历史' }}
                  </el-tag>
                </div>
                <div class="version-changes">
                  <div v-for="change in version.changes" :key="change.field" class="change-item">
                    <span class="change-field">{{ change.field }}:</span>
                    <span class="change-old">{{ change.oldValue }}</span>
                    <el-icon class="change-arrow"><Right /></el-icon>
                    <span class="change-new">{{ change.newValue }}</span>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-drawer>

    <!-- 版本历史对话框 -->
    <el-dialog
      v-model="showVersionDialog"
      title="版本历史"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="versionHistoryData">
        <el-table :data="versionHistoryData" border>
          <el-table-column prop="version" label="版本" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.version === scope.row.latestVersion ? 'success' : 'info'">
                v{{ scope.row.version }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="changeType" label="变更类型" width="100">
            <template #default="scope">
              <el-tag :type="getChangeTypeColor(scope.row.changeType)" size="small">
                {{ getChangeTypeText(scope.row.changeType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="changedFields" label="变更字段" min-width="150" />
          <el-table-column prop="createdBy" label="创建人" width="100" />
          <el-table-column prop="createdAt" label="创建时间" width="160" />
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="viewVersionDetail(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Right } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const showDetailDrawer = ref(false)
const showVersionDialog = ref(false)
const currentRecord = ref(null)
const versionHistoryData = ref(null)
const viewMode = ref('latest')

// 搜索表单
const searchForm = reactive({
  recordId: '',
  brand: '',
  model: '',
  jobId: '',
  sourceType: '',
  dataType: '',
  dateRange: []
})

// 统计数据
const stats = reactive({
  totalRecords: 15680,
  skuRecords: 12450,
  spuRecords: 3230,
  uniqueBrands: 156,
  latestVersion: 'v3.2'
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 15680
})

// 记录列表数据
const recordsList = ref([
  {
    recordId: 'REC_20241201_001',
    fingerprint: 'fp_abc123',
    version: 2,
    latestVersion: 3,
    totalVersions: 3,
    dataType: 'sku',
    brandOriginal: 'Intel',
    modelOriginal: 'i7-12700K',
    quantity: '10',
    unit: 'PCS',
    sourceType: 'customer_bom',
    jobId: 'JOB_20241201_001',
    createdAt: '2024-12-01 10:30:00',
    fieldComparison: [
      { fieldName: '品牌', originalValue: 'Intel', parsedValue: 'Intel', confidence: 95 },
      { fieldName: '型号', originalValue: 'i7-12700K', parsedValue: 'i7-12700K', confidence: 98 },
      { fieldName: '数量', originalValue: '10', parsedValue: '10', confidence: 100 },
      { fieldName: '单位', originalValue: 'PCS', parsedValue: 'PCS', confidence: 90 }
    ],
    traceInfo: {
      uploader: '张三',
      fileName: '客户A_BOM清单_20241201.xlsx',
      fileHash: 'sha256:abc123...',
      lineNumber: 15,
      batchId: 'BATCH_001',
      processedAt: '2024-12-01 10:35:00'
    },
    versionHistory: [
      {
        version: 3,
        createdAt: '2024-12-01 15:20:00',
        changes: [
          { field: '单价', oldValue: '2500.00', newValue: '2450.00' }
        ]
      },
      {
        version: 2,
        createdAt: '2024-12-01 12:10:00',
        changes: [
          { field: '数量', oldValue: '5', newValue: '10' }
        ]
      },
      {
        version: 1,
        createdAt: '2024-12-01 10:30:00',
        changes: [
          { field: '初始创建', oldValue: '', newValue: '所有字段' }
        ]
      }
    ]
  },
  {
    recordId: 'REC_20241201_002',
    fingerprint: 'fp_def456',
    version: 1,
    latestVersion: 1,
    totalVersions: 1,
    dataType: 'spu_params',
    brandOriginal: 'AMD',
    modelOriginal: 'Ryzen系列',
    quantity: '-',
    unit: '-',
    sourceType: 'crawler_data',
    jobId: 'JOB_20241201_003',
    createdAt: '2024-12-01 16:45:00',
    fieldComparison: [
      { fieldName: '品牌', originalValue: 'AMD', parsedValue: 'AMD', confidence: 98 },
      { fieldName: '产品系列', originalValue: 'Ryzen系列', parsedValue: 'Ryzen', confidence: 85 }
    ],
    paramSpace: {
      dimensions: 4,
      estimatedCombinations: 2560,
      parameters: [
        {
          name: '核心数',
          type: 'discrete',
          values: ['4', '6', '8', '12', '16'],
          constraints: '必须为偶数'
        },
        {
          name: '基础频率',
          type: 'continuous',
          values: ['3.0GHz', '3.2GHz', '3.4GHz', '3.6GHz', '3.8GHz'],
          constraints: '范围: 3.0-4.0GHz'
        },
        {
          name: '制程工艺',
          type: 'categorical',
          values: ['7nm', '5nm'],
          constraints: '仅支持先进制程'
        },
        {
          name: 'TDP',
          type: 'discrete',
          values: ['65W', '105W', '125W'],
          constraints: '标准功耗等级'
        }
      ]
    },
    traceInfo: {
      uploader: '王五',
      fileName: '爬虫数据_电子元器件_20241201.json',
      fileHash: 'sha256:def456...',
      lineNumber: 1205,
      batchId: 'BATCH_003',
      processedAt: '2024-12-01 16:50:00'
    },
    versionHistory: [
      {
        version: 1,
        createdAt: '2024-12-01 16:45:00',
        changes: [
          { field: '初始创建', oldValue: '', newValue: '参数空间定义' }
        ]
      }
    ]
  }
])

// 工具方法
const getSourceTypeText = (type) => {
  const map = {
    'customer_bom': '客户BOM',
    'supplier_catalog': '供应商目录',
    'crawler_data': '爬虫数据',
    'manual_input': '手工录入'
  }
  return map[type] || type
}

const getSourceTypeColor = (type) => {
  const map = {
    'customer_bom': 'primary',
    'supplier_catalog': 'success',
    'crawler_data': 'warning',
    'manual_input': 'info'
  }
  return map[type] || ''
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 90) return '#67c23a'
  if (confidence >= 70) return '#e6a23c'
  return '#f56c6c'
}

const getChangeTypeText = (type) => {
  const map = {
    'create': '创建',
    'update': '更新',
    'merge': '合并',
    'split': '拆分'
  }
  return map[type] || type
}

const getChangeTypeColor = (type) => {
  const map = {
    'create': 'success',
    'update': 'primary',
    'merge': 'warning',
    'split': 'info'
  }
  return map[type] || ''
}

// 事件处理方法
const handleViewModeChange = (mode) => {
  ElMessage.info(`切换到${mode === 'latest' ? '最新版本' : '历史版本'}视图`)
  refreshRecords()
}

const searchRecords = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('搜索完成')
  }, 500)
}

const resetSearch = () => {
  Object.assign(searchForm, {
    recordId: '',
    brand: '',
    model: '',
    jobId: '',
    sourceType: '',
    dataType: '',
    dateRange: []
  })
  ElMessage.info('搜索条件已重置')
}

const exportRecords = () => {
  ElMessage.info('正在导出记录数据...')
  // 这里实现导出逻辑
}

const refreshRecords = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 500)
}

const viewRecordDetail = (record) => {
  currentRecord.value = record
  showDetailDrawer.value = true
}

const viewVersionHistory = (record) => {
  // 模拟版本历史数据
  versionHistoryData.value = [
    {
      version: 3,
      latestVersion: 3,
      changeType: 'update',
      changedFields: '单价',
      createdBy: '李四',
      createdAt: '2024-12-01 15:20:00'
    },
    {
      version: 2,
      latestVersion: 3,
      changeType: 'update',
      changedFields: '数量',
      createdBy: '张三',
      createdAt: '2024-12-01 12:10:00'
    },
    {
      version: 1,
      latestVersion: 3,
      changeType: 'create',
      changedFields: '所有字段',
      createdBy: '张三',
      createdAt: '2024-12-01 10:30:00'
    }
  ]
  showVersionDialog.value = true
}

const viewVersionDetail = (version) => {
  ElMessage.info(`查看版本 v${version.version} 详情`)
  showVersionDialog.value = false
  // 这里可以加载特定版本的详细数据并显示在详情抽屉中
}

const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  refreshRecords()
}

const handleCurrentChange = (val) => {
  pagination.currentPage = val
  refreshRecords()
}

onMounted(() => {
  refreshRecords()
})
</script>

<style scoped>
.raw-records-container {
  margin: 20px;
}

.filter-card,
.stats-card,
.table-card {
  margin-bottom: 20px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.view-toggle {
  display: flex;
  align-items: center;
}

.search-form {
  background: #fafafa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.stats-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 16px 0;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

:deep(.clickable-row) {
  cursor: pointer;
}

:deep(.clickable-row:hover) {
  background-color: #f5f7fa;
}

.record-detail {
  padding: 0 16px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.detail-section h5 {
  margin: 16px 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.original-value {
  color: #909399;
  font-style: italic;
}

.parsed-value {
  color: #303133;
  font-weight: 500;
}

.confidence-text {
  font-size: 12px;
  color: #606266;
  margin-left: 8px;
}

.param-space-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.param-definitions {
  margin-top: 16px;
}

.param-value-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.more-values {
  font-size: 12px;
  color: #909399;
}

.version-item {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.version-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.version-number {
  font-weight: 600;
  color: #303133;
}

.version-changes {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.change-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.change-field {
  font-weight: 500;
  color: #606266;
}

.change-old {
  color: #f56c6c;
  text-decoration: line-through;
}

.change-new {
  color: #67c23a;
  font-weight: 500;
}

.change-arrow {
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-content {
    flex-wrap: wrap;
    gap: 16px;
  }

  .stat-item {
    flex: 1;
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .raw-records-container {
    margin: 10px;
  }

  .filter-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .stats-content {
    flex-direction: column;
    gap: 12px;
  }

  .change-item {
    flex-wrap: wrap;
  }
}
</style>