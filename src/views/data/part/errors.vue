<template>
  <div class="errors-container">
    <!-- 错误总览 -->
    <el-card class="overview-card">
      <div class="overview-header">
        <h3>错误总览</h3>
        <div class="overview-actions">
          <el-button @click="refreshErrors">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button type="primary" @click="showBatchFixDialog = true">
            批量修复
          </el-button>
        </div>
      </div>

      <div class="error-stats">
        <div class="stat-card">
          <div class="stat-value error">{{ errorStats.totalErrors }}</div>
          <div class="stat-label">总错误数</div>
        </div>
        <div class="stat-card">
          <div class="stat-value warning">{{ errorStats.pendingFix }}</div>
          <div class="stat-label">待修复</div>
        </div>
        <div class="stat-card">
          <div class="stat-value success">{{ errorStats.fixed }}</div>
          <div class="stat-label">已修复</div>
        </div>
        <div class="stat-card">
          <div class="stat-value info">{{ errorStats.ignored }}</div>
          <div class="stat-label">已忽略</div>
        </div>
      </div>
    </el-card>

    <!-- 按任务聚合的错误统计 -->
    <el-card class="job-errors-card">
      <div class="card-header">
        <h4>任务错误统计</h4>
      </div>

      <el-table :data="jobErrorsList" border>
        <el-table-column prop="jobId" label="任务ID" width="140" />
        <el-table-column prop="fileName" label="文件名" min-width="200" show-overflow-tooltip />
        <el-table-column prop="totalRecords" label="总记录数" width="100" />
        <el-table-column prop="errorRecords" label="错误记录数" width="120" />
        <el-table-column prop="errorRate" label="错误率" width="100">
          <template #default="scope">
            <span :class="getErrorRateClass(scope.row.errorRate)">
              {{ scope.row.errorRate }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="topErrorType" label="主要错误类型" min-width="150" />
        <el-table-column prop="status" label="修复状态" width="100">
          <template #default="scope">
            <el-tag :type="getFixStatusColor(scope.row.status)">
              {{ getFixStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="viewJobErrors(scope.row)">
              查看详情
            </el-button>
            <el-button
              link
              type="success"
              size="small"
              @click="fixJobErrors(scope.row)"
              v-if="scope.row.status === 'pending'"
            >
              修复
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 错误明细 -->
    <el-card class="error-details-card">
      <div class="card-header">
        <h4>错误明细</h4>
        <div class="filter-actions">
          <el-select v-model="errorFilter.jobId" placeholder="选择任务" clearable style="width: 200px">
            <el-option
              v-for="job in jobErrorsList"
              :key="job.jobId"
              :label="job.jobId"
              :value="job.jobId"
            />
          </el-select>
          <el-select v-model="errorFilter.errorType" placeholder="错误类型" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option label="品牌不规范" value="brand_invalid" />
            <el-option label="型号格式错误" value="model_format" />
            <el-option label="数量异常" value="quantity_invalid" />
            <el-option label="单位不匹配" value="unit_mismatch" />
            <el-option label="价格异常" value="price_invalid" />
          </el-select>
          <el-select v-model="errorFilter.status" placeholder="修复状态" clearable style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="待修复" value="pending" />
            <el-option label="已修复" value="fixed" />
            <el-option label="已忽略" value="ignored" />
          </el-select>
          <el-button @click="filterErrors">筛选</el-button>
        </div>
      </div>

      <el-table
        :data="errorDetailsList"
        v-loading="loading"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="recordId" label="记录ID" width="140" />
        <el-table-column prop="errorType" label="错误类型" width="120">
          <template #default="scope">
            <el-tag :type="getErrorTypeColor(scope.row.errorType)" size="small">
              {{ getErrorTypeText(scope.row.errorType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fieldName" label="错误字段" width="100" />
        <el-table-column prop="originalValue" label="原始值" min-width="150" show-overflow-tooltip />
        <el-table-column prop="suggestedValue" label="建议修复值" min-width="150" show-overflow-tooltip />
        <el-table-column prop="confidence" label="置信度" width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.confidence"
              :stroke-width="6"
              :show-text="false"
              :color="getConfidenceColor(scope.row.confidence)"
            />
            <span class="confidence-text">{{ scope.row.confidence }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getFixStatusColor(scope.row.status)" size="small">
              {{ getFixStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              @click="editError(scope.row)"
              v-if="scope.row.status === 'pending'"
            >
              编辑
            </el-button>
            <el-button
              link
              type="success"
              size="small"
              @click="acceptSuggestion(scope.row)"
              v-if="scope.row.status === 'pending' && scope.row.suggestedValue"
            >
              采纳建议
            </el-button>
            <el-button
              link
              type="info"
              size="small"
              @click="ignoreError(scope.row)"
              v-if="scope.row.status === 'pending'"
            >
              忽略
            </el-button>
            <el-button
              link
              type="warning"
              size="small"
              @click="revertFix(scope.row)"
              v-if="scope.row.status === 'fixed'"
            >
              撤销
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedErrors.length > 0">
        <span class="selection-info">已选择 {{ selectedErrors.length }} 项</span>
        <el-button size="small" @click="batchAcceptSuggestions">批量采纳建议</el-button>
        <el-button size="small" @click="batchIgnoreErrors">批量忽略</el-button>
        <el-button size="small" type="danger" @click="clearSelection">清空选择</el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 编辑错误对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑错误记录"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="currentError">
        <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="100px">
          <el-form-item label="记录ID">
            <el-input v-model="currentError.recordId" disabled />
          </el-form-item>
          <el-form-item label="错误类型">
            <el-tag :type="getErrorTypeColor(currentError.errorType)">
              {{ getErrorTypeText(currentError.errorType) }}
            </el-tag>
          </el-form-item>
          <el-form-item label="错误字段">
            <el-input v-model="currentError.fieldName" disabled />
          </el-form-item>
          <el-form-item label="原始值">
            <el-input v-model="currentError.originalValue" disabled />
          </el-form-item>
          <el-form-item label="修复值" prop="fixedValue">
            <el-input
              v-model="editForm.fixedValue"
              placeholder="请输入修复后的值"
            />
          </el-form-item>
          <el-form-item label="修复说明">
            <el-input
              v-model="editForm.fixNote"
              type="textarea"
              :rows="3"
              placeholder="请输入修复说明（可选）"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmEdit">确定修复</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量修复对话框 -->
    <el-dialog
      v-model="showBatchFixDialog"
      title="批量修复"
      width="700px"
      :close-on-click-modal="false"
    >
      <div class="batch-fix-content">
        <el-tabs v-model="batchFixMode">
          <el-tab-pane label="上传修复文件" name="upload">
            <div class="upload-section">
              <el-alert
                title="批量修复说明"
                type="info"
                :closable="false"
                show-icon
              >
                <template #default>
                  <p>1. 下载错误清单模板，填写修复值</p>
                  <p>2. 上传修复文件，系统将自动应用修复</p>
                  <p>3. 支持CSV格式，必须包含record_id和fixed_value列</p>
                </template>
              </el-alert>

              <div class="upload-actions">
                <el-button @click="downloadTemplate">
                  <el-icon><Download /></el-icon>
                  下载错误清单模板
                </el-button>
              </div>

              <el-upload
                ref="batchUploadRef"
                :file-list="batchFixForm.files"
                :auto-upload="false"
                :on-change="handleBatchFileChange"
                :on-remove="handleBatchFileRemove"
                :accept="'.csv'"
                drag
                class="batch-upload"
              >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  将修复文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    仅支持CSV格式文件，单个文件不超过10MB
                  </div>
                </template>
              </el-upload>
            </div>
          </el-tab-pane>

          <el-tab-pane label="规则修复" name="rules">
            <div class="rules-section">
              <el-form :model="batchFixForm" label-width="120px">
                <el-form-item label="错误类型">
                  <el-select v-model="batchFixForm.errorType" placeholder="选择要修复的错误类型">
                    <el-option label="品牌不规范" value="brand_invalid" />
                    <el-option label="型号格式错误" value="model_format" />
                    <el-option label="数量异常" value="quantity_invalid" />
                    <el-option label="单位不匹配" value="unit_mismatch" />
                  </el-select>
                </el-form-item>
                <el-form-item label="修复规则">
                  <el-select v-model="batchFixForm.fixRule" placeholder="选择修复规则">
                    <el-option label="采纳所有建议值" value="accept_all_suggestions" />
                    <el-option label="使用默认值替换" value="use_default_value" />
                    <el-option label="清空异常值" value="clear_invalid_values" />
                  </el-select>
                </el-form-item>
                <el-form-item label="默认值" v-if="batchFixForm.fixRule === 'use_default_value'">
                  <el-input v-model="batchFixForm.defaultValue" placeholder="请输入默认值" />
                </el-form-item>
                <el-form-item label="影响范围">
                  <el-input-number
                    v-model="batchFixForm.affectedCount"
                    :min="0"
                    :max="1000"
                    disabled
                  />
                  <span class="affected-info">条记录将被修复</span>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showBatchFixDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmBatchFix"
            :loading="batchFixing"
          >
            {{ batchFixMode === 'upload' ? '上传并修复' : '执行批量修复' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Download, UploadFilled } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const batchFixing = ref(false)
const showEditDialog = ref(false)
const showBatchFixDialog = ref(false)
const currentError = ref(null)
const selectedErrors = ref([])
const batchFixMode = ref('upload')

// 错误统计数据
const errorStats = reactive({
  totalErrors: 1250,
  pendingFix: 890,
  fixed: 280,
  ignored: 80
})

// 错误筛选条件
const errorFilter = reactive({
  jobId: '',
  errorType: '',
  status: ''
})

// 编辑表单
const editForm = reactive({
  fixedValue: '',
  fixNote: ''
})

const editFormRef = ref()

const editRules = {
  fixedValue: [
    { required: true, message: '请输入修复值', trigger: 'blur' }
  ]
}

// 批量修复表单
const batchFixForm = reactive({
  files: [],
  errorType: '',
  fixRule: '',
  defaultValue: '',
  affectedCount: 0
})

const batchUploadRef = ref()

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 1250
})

// 任务错误统计数据
const jobErrorsList = ref([
  {
    jobId: 'JOB_20241201_001',
    fileName: '客户A_BOM清单_20241201.xlsx',
    totalRecords: 1250,
    errorRecords: 70,
    errorRate: 5.6,
    topErrorType: '品牌不规范',
    status: 'pending'
  },
  {
    jobId: 'JOB_20241201_002',
    fileName: '供应商B_产品目录_20241201.csv',
    totalRecords: 5000,
    errorRecords: 850,
    errorRate: 17.0,
    topErrorType: '型号格式错误',
    status: 'fixing'
  },
  {
    jobId: 'JOB_20241201_003',
    fileName: '爬虫数据_电子元器件_20241201.json',
    totalRecords: 8000,
    errorRecords: 330,
    errorRate: 4.1,
    topErrorType: '单位不匹配',
    status: 'completed'
  }
])

// 错误明细数据
const errorDetailsList = ref([
  {
    recordId: 'REC_20241201_001',
    errorType: 'brand_invalid',
    fieldName: '品牌',
    originalValue: 'intel',
    suggestedValue: 'Intel',
    confidence: 95,
    status: 'pending',
    jobId: 'JOB_20241201_001'
  },
  {
    recordId: 'REC_20241201_002',
    errorType: 'model_format',
    fieldName: '型号',
    originalValue: 'i7 12700k',
    suggestedValue: 'i7-12700K',
    confidence: 88,
    status: 'pending',
    jobId: 'JOB_20241201_001'
  },
  {
    recordId: 'REC_20241201_003',
    errorType: 'quantity_invalid',
    fieldName: '数量',
    originalValue: '-5',
    suggestedValue: '5',
    confidence: 75,
    status: 'fixed',
    jobId: 'JOB_20241201_002'
  },
  {
    recordId: 'REC_20241201_004',
    errorType: 'unit_mismatch',
    fieldName: '单位',
    originalValue: '个',
    suggestedValue: 'PCS',
    confidence: 92,
    status: 'pending',
    jobId: 'JOB_20241201_003'
  },
  {
    recordId: 'REC_20241201_005',
    errorType: 'price_invalid',
    fieldName: '单价',
    originalValue: '0',
    suggestedValue: '',
    confidence: 0,
    status: 'ignored',
    jobId: 'JOB_20241201_001'
  }
])

// 工具方法
const getErrorRateClass = (rate) => {
  if (rate >= 15) return 'error-rate-high'
  if (rate >= 5) return 'error-rate-medium'
  return 'error-rate-low'
}

const getFixStatusText = (status) => {
  const map = {
    'pending': '待修复',
    'fixing': '修复中',
    'completed': '已完成',
    'failed': '修复失败'
  }
  return map[status] || status
}

const getFixStatusColor = (status) => {
  const map = {
    'pending': 'warning',
    'fixing': 'primary',
    'completed': 'success',
    'failed': 'danger'
  }
  return map[status] || ''
}

const getErrorTypeText = (type) => {
  const map = {
    'brand_invalid': '品牌不规范',
    'model_format': '型号格式错误',
    'quantity_invalid': '数量异常',
    'unit_mismatch': '单位不匹配',
    'price_invalid': '价格异常'
  }
  return map[type] || type
}

const getErrorTypeColor = (type) => {
  const map = {
    'brand_invalid': 'warning',
    'model_format': 'danger',
    'quantity_invalid': 'danger',
    'unit_mismatch': 'info',
    'price_invalid': 'warning'
  }
  return map[type] || ''
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 90) return '#67c23a'
  if (confidence >= 70) return '#e6a23c'
  return '#f56c6c'
}

// 事件处理方法
const refreshErrors = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 500)
}

const viewJobErrors = (job) => {
  errorFilter.jobId = job.jobId
  filterErrors()
  ElMessage.info(`已筛选任务 ${job.jobId} 的错误`)
}

const fixJobErrors = (job) => {
  ElMessageBox.confirm(
    `确定要修复任务 "${job.jobId}" 的所有错误吗？`,
    '确认修复',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('开始修复任务错误...')
    // 这里实现任务级别的错误修复逻辑
  }).catch(() => {
    ElMessage.info('已取消修复')
  })
}

const filterErrors = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('筛选完成')
  }, 500)
}

const handleSelectionChange = (selection) => {
  selectedErrors.value = selection
}

const editError = (error) => {
  currentError.value = error
  editForm.fixedValue = error.suggestedValue || ''
  editForm.fixNote = ''
  showEditDialog.value = true
}

const confirmEdit = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()

    // 更新错误状态
    currentError.value.status = 'fixed'
    currentError.value.fixedValue = editForm.fixedValue
    currentError.value.fixNote = editForm.fixNote

    showEditDialog.value = false
    ElMessage.success('错误修复成功')

    // 更新统计数据
    errorStats.pendingFix--
    errorStats.fixed++

  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const acceptSuggestion = (error) => {
  error.status = 'fixed'
  error.fixedValue = error.suggestedValue
  ElMessage.success('已采纳建议值')

  // 更新统计数据
  errorStats.pendingFix--
  errorStats.fixed++
}

const ignoreError = (error) => {
  ElMessageBox.confirm(
    '确定要忽略此错误吗？忽略后将不再提示修复。',
    '确认忽略',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    error.status = 'ignored'
    ElMessage.success('已忽略错误')

    // 更新统计数据
    errorStats.pendingFix--
    errorStats.ignored++
  }).catch(() => {
    ElMessage.info('已取消忽略')
  })
}

const revertFix = (error) => {
  ElMessageBox.confirm(
    '确定要撤销此修复吗？',
    '确认撤销',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    error.status = 'pending'
    delete error.fixedValue
    delete error.fixNote
    ElMessage.success('已撤销修复')

    // 更新统计数据
    errorStats.pendingFix++
    errorStats.fixed--
  }).catch(() => {
    ElMessage.info('已取消撤销')
  })
}

const batchAcceptSuggestions = () => {
  const validErrors = selectedErrors.value.filter(error =>
    error.status === 'pending' && error.suggestedValue
  )

  if (validErrors.length === 0) {
    ElMessage.warning('没有可采纳建议的错误')
    return
  }

  ElMessageBox.confirm(
    `确定要批量采纳 ${validErrors.length} 个错误的建议值吗？`,
    '确认批量采纳',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    validErrors.forEach(error => {
      error.status = 'fixed'
      error.fixedValue = error.suggestedValue
    })

    ElMessage.success(`已批量采纳 ${validErrors.length} 个建议值`)

    // 更新统计数据
    errorStats.pendingFix -= validErrors.length
    errorStats.fixed += validErrors.length

    clearSelection()
  }).catch(() => {
    ElMessage.info('已取消批量采纳')
  })
}

const batchIgnoreErrors = () => {
  const validErrors = selectedErrors.value.filter(error => error.status === 'pending')

  if (validErrors.length === 0) {
    ElMessage.warning('没有可忽略的错误')
    return
  }

  ElMessageBox.confirm(
    `确定要批量忽略 ${validErrors.length} 个错误吗？`,
    '确认批量忽略',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    validErrors.forEach(error => {
      error.status = 'ignored'
    })

    ElMessage.success(`已批量忽略 ${validErrors.length} 个错误`)

    // 更新统计数据
    errorStats.pendingFix -= validErrors.length
    errorStats.ignored += validErrors.length

    clearSelection()
  }).catch(() => {
    ElMessage.info('已取消批量忽略')
  })
}

const clearSelection = () => {
  selectedErrors.value = []
}

const handleBatchFileChange = (file, fileList) => {
  batchFixForm.files = fileList
}

const handleBatchFileRemove = (file, fileList) => {
  batchFixForm.files = fileList
}

const downloadTemplate = () => {
  ElMessage.info('正在下载错误清单模板...')
  // 这里实现模板下载逻辑
}

const confirmBatchFix = () => {
  if (batchFixMode.value === 'upload') {
    if (batchFixForm.files.length === 0) {
      ElMessage.warning('请选择要上传的修复文件')
      return
    }

    batchFixing.value = true

    // 模拟上传和修复过程
    setTimeout(() => {
      batchFixing.value = false
      showBatchFixDialog.value = false

      ElMessage.success('批量修复完成')

      // 重置表单
      batchFixForm.files = []

      refreshErrors()
    }, 3000)

  } else {
    // 规则修复
    if (!batchFixForm.errorType || !batchFixForm.fixRule) {
      ElMessage.warning('请完善修复规则配置')
      return
    }

    batchFixing.value = true

    // 模拟规则修复过程
    setTimeout(() => {
      batchFixing.value = false
      showBatchFixDialog.value = false

      ElMessage.success(`已使用规则修复 ${batchFixForm.affectedCount} 条错误`)

      // 重置表单
      Object.assign(batchFixForm, {
        errorType: '',
        fixRule: '',
        defaultValue: '',
        affectedCount: 0
      })

      refreshErrors()
    }, 2000)
  }
}

const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  filterErrors()
}

const handleCurrentChange = (val) => {
  pagination.currentPage = val
  filterErrors()
}

onMounted(() => {
  refreshErrors()
})
</script>

<style scoped>
.errors-container {
  margin: 20px;
}

.overview-card,
.job-errors-card,
.error-details-card {
  margin-bottom: 20px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.overview-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.overview-actions {
  display: flex;
  gap: 8px;
}

.error-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 16px 0;
}

.stat-card {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  min-width: 120px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-value.error {
  color: #f56c6c;
}

.stat-value.warning {
  color: #e6a23c;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.info {
  color: #409eff;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.filter-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.error-rate-high {
  color: #f56c6c;
  font-weight: 600;
}

.error-rate-medium {
  color: #e6a23c;
  font-weight: 600;
}

.error-rate-low {
  color: #67c23a;
  font-weight: 600;
}

.confidence-text {
  font-size: 12px;
  color: #606266;
  margin-left: 8px;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  margin-top: 16px;
}

.selection-info {
  font-size: 14px;
  color: #409eff;
  font-weight: 500;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.batch-fix-content {
  min-height: 300px;
}

.upload-section {
  padding: 16px 0;
}

.upload-actions {
  margin: 16px 0;
  text-align: center;
}

.batch-upload {
  margin-top: 16px;
}

.rules-section {
  padding: 16px 0;
}

.affected-info {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .error-stats {
    flex-wrap: wrap;
    gap: 16px;
  }

  .stat-card {
    flex: 1;
    min-width: 100px;
  }
}

@media (max-width: 768px) {
  .errors-container {
    margin: 10px;
  }

  .overview-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .filter-actions {
    flex-wrap: wrap;
  }

  .error-stats {
    flex-direction: column;
    gap: 12px;
  }

  .batch-actions {
    flex-wrap: wrap;
  }
}
</style>