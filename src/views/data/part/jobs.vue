<template>
  <div class="jobs-container">
    <!-- 操作区域 -->
    <el-card class="operation-card">
      <div class="operation-header">
        <h3>导入任务管理</h3>
        <el-button type="primary" @click="showUploadDialog = true">
          <el-icon><Upload /></el-icon>
          新建导入任务
        </el-button>
      </div>
    </el-card>

    <!-- 搜索过滤区域 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="任务ID">
          <el-input v-model="searchForm.jobId" placeholder="请输入任务ID" clearable />
        </el-form-item>
        <el-form-item label="来源类型">
          <el-select v-model="searchForm.sourceType" placeholder="请选择来源类型" clearable>
            <el-option label="全部" value="" />
            <el-option label="客户BOM" value="customer_bom" />
            <el-option label="供应商目录" value="supplier_catalog" />
            <el-option label="爬虫数据" value="crawler_data" />
            <el-option label="手工录入" value="manual_input" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="待处理" value="pending" />
            <el-option label="映射中" value="mapping" />
            <el-option label="解析中" value="parsing" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchJobs">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务列表 -->
    <el-card class="table-card">
      <div class="table-header">
        <h4>任务列表</h4>
        <div class="table-actions">
          <el-button size="small" @click="refreshJobs">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <el-table :data="jobsList" v-loading="loading" border style="width: 100%">
        <el-table-column prop="jobId" label="任务ID" width="120" />
        <el-table-column prop="fileName" label="文件名" min-width="200" show-overflow-tooltip />
        <el-table-column prop="sourceType" label="来源类型" width="120">
          <template #default="scope">
            <el-tag :type="getSourceTypeColor(scope.row.sourceType)">
              {{ getSourceTypeText(scope.row.sourceType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="totalRows" label="总行数" width="100" />
        <el-table-column prop="successRows" label="成功行数" width="100" />
        <el-table-column prop="successRate" label="成功率" width="100">
          <template #default="scope">
            <span :class="getSuccessRateClass(scope.row.successRate)">
              {{ scope.row.successRate }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createdBy" label="创建人" width="100" />
        <el-table-column prop="createdAt" label="创建时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="viewJobDetail(scope.row)">
              详情
            </el-button>
            <el-button
              link
              type="success"
              size="small"
              v-if="scope.row.status === 'pending'"
              @click="startMapping(scope.row)"
            >
              开始映射
            </el-button>
            <el-button
              link
              type="warning"
              size="small"
              v-if="scope.row.errorRows > 0"
              @click="downloadErrors(scope.row)"
            >
              下载错误
            </el-button>
            <el-button
              link
              type="danger"
              size="small"
              v-if="['pending', 'failed'].includes(scope.row.status)"
              @click="deleteJob(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 文件上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="新建导入任务"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="uploadForm" :rules="uploadRules" ref="uploadFormRef" label-width="100px">
        <el-form-item label="来源类型" prop="sourceType">
          <el-select v-model="uploadForm.sourceType" placeholder="请选择来源类型">
            <el-option label="客户BOM" value="customer_bom" />
            <el-option label="供应商目录" value="supplier_catalog" />
            <el-option label="爬虫数据" value="crawler_data" />
            <el-option label="手工录入" value="manual_input" />
          </el-select>
        </el-form-item>
        <el-form-item label="来源标识" prop="sourceId">
          <el-input v-model="uploadForm.sourceId" placeholder="客户ID/供应商ID/爬虫任务ID等" />
        </el-form-item>
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="uploadForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>
        <el-form-item label="上传文件" prop="files">
          <el-upload
            ref="uploadRef"
            :file-list="uploadForm.files"
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :accept="'.xlsx,.xls,.csv,.json,.zip'"
            multiple
            drag
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 Excel(.xlsx/.xls)、CSV、JSON、ZIP 格式，单个文件不超过50MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showUploadDialog = false">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploading">
            确定上传
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="任务详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentJob" class="job-detail">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="任务ID">{{ currentJob.jobId }}</el-descriptions-item>
          <el-descriptions-item label="文件名">{{ currentJob.fileName }}</el-descriptions-item>
          <el-descriptions-item label="来源类型">
            <el-tag :type="getSourceTypeColor(currentJob.sourceType)">
              {{ getSourceTypeText(currentJob.sourceType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(currentJob.status)">
              {{ getStatusText(currentJob.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="总行数">{{ currentJob.totalRows }}</el-descriptions-item>
          <el-descriptions-item label="成功行数">{{ currentJob.successRows }}</el-descriptions-item>
          <el-descriptions-item label="错误行数">{{ currentJob.errorRows }}</el-descriptions-item>
          <el-descriptions-item label="成功率">
            <span :class="getSuccessRateClass(currentJob.successRate)">
              {{ currentJob.successRate }}%
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ currentJob.createdBy }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentJob.createdAt }}</el-descriptions-item>
        </el-descriptions>

        <!-- 进度信息 -->
        <div class="progress-section" v-if="currentJob.status === 'parsing'">
          <h4>解析进度</h4>
          <el-progress
            :percentage="currentJob.progress || 0"
            :status="currentJob.progress === 100 ? 'success' : ''"
          />
          <p class="progress-text">
            已处理 {{ currentJob.processedRows || 0 }} / {{ currentJob.totalRows }} 行
          </p>
        </div>

        <!-- 文件清单 -->
        <div class="files-section">
          <h4>文件清单</h4>
          <el-table :data="currentJob.files || []" border size="small">
            <el-table-column prop="fileName" label="文件名" />
            <el-table-column prop="fileSize" label="文件大小" width="100" />
            <el-table-column prop="fileType" label="文件类型" width="100" />
            <el-table-column prop="rows" label="行数" width="80" />
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button link type="primary" size="small" @click="previewFile(scope.row)">
                  预览
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 样本预览 -->
        <div class="sample-section" v-if="currentJob.sampleData">
          <h4>数据样本</h4>
          <el-table :data="currentJob.sampleData" border size="small" max-height="300">
            <el-table-column
              v-for="(column, index) in currentJob.sampleColumns"
              :key="index"
              :prop="column"
              :label="column"
              min-width="120"
              show-overflow-tooltip
            />
          </el-table>
        </div>

        <!-- 错误统计 -->
        <div class="error-section" v-if="currentJob.errorStats">
          <h4>错误统计</h4>
          <el-table :data="currentJob.errorStats" border size="small">
            <el-table-column prop="errorType" label="错误类型" />
            <el-table-column prop="errorCount" label="错误数量" width="100" />
            <el-table-column prop="errorRate" label="错误率" width="100">
              <template #default="scope">
                {{ scope.row.errorRate }}%
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Refresh, UploadFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const showUploadDialog = ref(false)
const showDetailDialog = ref(false)
const currentJob = ref(null)

// 搜索表单
const searchForm = reactive({
  jobId: '',
  sourceType: '',
  status: '',
  dateRange: []
})

// 上传表单
const uploadForm = reactive({
  sourceType: '',
  sourceId: '',
  description: '',
  files: []
})

const uploadFormRef = ref()
const uploadRef = ref()

// 上传表单验证规则
const uploadRules = {
  sourceType: [
    { required: true, message: '请选择来源类型', trigger: 'change' }
  ],
  sourceId: [
    { required: true, message: '请输入来源标识', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入任务描述', trigger: 'blur' }
  ]
}

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 任务列表数据
const jobsList = ref([
  {
    jobId: 'JOB_20241201_001',
    fileName: '客户A_BOM清单_20241201.xlsx',
    sourceType: 'customer_bom',
    status: 'completed',
    totalRows: 1250,
    successRows: 1180,
    errorRows: 70,
    successRate: 94.4,
    createdBy: '张三',
    createdAt: '2024-12-01 10:30:00',
    files: [
      { fileName: '客户A_BOM清单_20241201.xlsx', fileSize: '2.5MB', fileType: 'Excel', rows: 1250 }
    ],
    sampleData: [
      { '品牌': 'Intel', '型号': 'i7-12700K', '数量': '10', '单位': 'PCS', '单价': '2500.00', '币种': 'CNY' },
      { '品牌': 'AMD', '型号': 'Ryzen 7 5800X', '数量': '5', '单位': 'PCS', '单价': '2200.00', '币种': 'CNY' }
    ],
    sampleColumns: ['品牌', '型号', '数量', '单位', '单价', '币种'],
    errorStats: [
      { errorType: '品牌名称不规范', errorCount: 45, errorRate: 3.6 },
      { errorType: '型号格式错误', errorCount: 25, errorRate: 2.0 }
    ]
  },
  {
    jobId: 'JOB_20241201_002',
    fileName: '供应商B_产品目录_20241201.csv',
    sourceType: 'supplier_catalog',
    status: 'parsing',
    totalRows: 5000,
    successRows: 3200,
    errorRows: 150,
    successRate: 64.0,
    progress: 67,
    processedRows: 3350,
    createdBy: '李四',
    createdAt: '2024-12-01 14:20:00',
    files: [
      { fileName: '供应商B_产品目录_20241201.csv', fileSize: '8.2MB', fileType: 'CSV', rows: 5000 }
    ]
  },
  {
    jobId: 'JOB_20241201_003',
    fileName: '爬虫数据_电子元器件_20241201.json',
    sourceType: 'crawler_data',
    status: 'pending',
    totalRows: 0,
    successRows: 0,
    errorRows: 0,
    successRate: 0,
    createdBy: '王五',
    createdAt: '2024-12-01 16:45:00',
    files: [
      { fileName: '爬虫数据_电子元器件_20241201.json', fileSize: '15.6MB', fileType: 'JSON', rows: 0 }
    ]
  }
])

// 工具方法
const getSourceTypeText = (type) => {
  const map = {
    'customer_bom': '客户BOM',
    'supplier_catalog': '供应商目录',
    'crawler_data': '爬虫数据',
    'manual_input': '手工录入'
  }
  return map[type] || type
}

const getSourceTypeColor = (type) => {
  const map = {
    'customer_bom': 'primary',
    'supplier_catalog': 'success',
    'crawler_data': 'warning',
    'manual_input': 'info'
  }
  return map[type] || ''
}

const getStatusText = (status) => {
  const map = {
    'pending': '待处理',
    'mapping': '映射中',
    'parsing': '解析中',
    'completed': '已完成',
    'failed': '失败'
  }
  return map[status] || status
}

const getStatusColor = (status) => {
  const map = {
    'pending': 'info',
    'mapping': 'warning',
    'parsing': 'primary',
    'completed': 'success',
    'failed': 'danger'
  }
  return map[status] || ''
}

const getSuccessRateClass = (rate) => {
  if (rate >= 95) return 'success-rate-high'
  if (rate >= 80) return 'success-rate-medium'
  return 'success-rate-low'
}

// 事件处理方法
const searchJobs = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('搜索完成')
  }, 500)
}

const resetSearch = () => {
  Object.assign(searchForm, {
    jobId: '',
    sourceType: '',
    status: '',
    dateRange: []
  })
  ElMessage.info('搜索条件已重置')
}

const refreshJobs = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 500)
}

const handleFileChange = (file, fileList) => {
  uploadForm.files = fileList
}

const handleFileRemove = (file, fileList) => {
  uploadForm.files = fileList
}

const submitUpload = async () => {
  if (!uploadFormRef.value) return

  try {
    await uploadFormRef.value.validate()

    if (uploadForm.files.length === 0) {
      ElMessage.warning('请选择要上传的文件')
      return
    }

    uploading.value = true

    // 模拟上传过程
    setTimeout(() => {
      uploading.value = false
      showUploadDialog.value = false

      // 重置表单
      Object.assign(uploadForm, {
        sourceType: '',
        sourceId: '',
        description: '',
        files: []
      })
      uploadFormRef.value.resetFields()

      ElMessage.success('任务创建成功，正在处理文件...')
      refreshJobs()
    }, 2000)

  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const viewJobDetail = (job) => {
  currentJob.value = job
  showDetailDialog.value = true
}

const startMapping = (job) => {
  ElMessageBox.confirm(
    `确定要开始映射任务 "${job.jobId}" 吗？`,
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    // 跳转到映射页面，并传递任务ID
    router.push(`/data/part/mapping?jobId=${job.jobId}`)
  }).catch(() => {
    ElMessage.info('已取消操作')
  })
}

const downloadErrors = (job) => {
  ElMessage.info(`正在下载任务 ${job.jobId} 的错误清单...`)
  // 这里实现下载逻辑
}

const deleteJob = (job) => {
  ElMessageBox.confirm(
    `确定要删除任务 "${job.jobId}" 吗？此操作不可恢复。`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = jobsList.value.findIndex(item => item.jobId === job.jobId)
    if (index > -1) {
      jobsList.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const previewFile = (file) => {
  ElMessage.info(`预览文件: ${file.fileName}`)
  // 这里实现文件预览逻辑
}

const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  refreshJobs()
}

const handleCurrentChange = (val) => {
  pagination.currentPage = val
  refreshJobs()
}

onMounted(() => {
  refreshJobs()
})
</script>

<style scoped>
.jobs-container {
  margin: 20px;
}

.operation-card,
.filter-card,
.table-card {
  margin-bottom: 20px;
}

.operation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.operation-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.search-form {
  background: #fafafa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.success-rate-high {
  color: #67c23a;
  font-weight: 600;
}

.success-rate-medium {
  color: #e6a23c;
  font-weight: 600;
}

.success-rate-low {
  color: #f56c6c;
  font-weight: 600;
}

.job-detail {
  max-height: 600px;
  overflow-y: auto;
}

.progress-section,
.files-section,
.sample-section,
.error-section {
  margin-top: 20px;
}

.progress-section h4,
.files-section h4,
.sample-section h4,
.error-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.progress-text {
  margin: 8px 0 0 0;
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .jobs-container {
    margin: 10px;
  }

  .operation-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>