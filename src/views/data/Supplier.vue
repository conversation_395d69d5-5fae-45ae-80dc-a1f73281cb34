<template>
  <div class="supplier-pool-container">
    <!-- Header Section -->
    <el-card class="header-card" shadow="never">
      <div class="header-content">
        <div class="title-section">
          <h2>供应商池管理</h2>
          <p class="subtitle">管理和维护供应商信息池，支持供应商发现、比较和验证流程</p>
        </div>
        <div class="action-buttons">
          <el-button type="primary" @click="handleAddSupplier">
            <el-icon><Plus /></el-icon>
            添加供应商
          </el-button>
          <el-button @click="handleBulkImport">
            <el-icon><Upload /></el-icon>
            批量导入
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- Statistics Cards -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ supplierStats.total }}</div>
              <div class="stat-label">总供应商数</div>
            </div>
            <el-icon class="stat-icon"><OfficeBuilding /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ supplierStats.verified }}</div>
              <div class="stat-label">已验证</div>
            </div>
            <el-icon class="stat-icon verified"><CircleCheck /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ supplierStats.pending }}</div>
              <div class="stat-label">待验证</div>
            </div>
            <el-icon class="stat-icon pending"><Clock /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ supplierStats.active }}</div>
              <div class="stat-label">活跃供应商</div>
            </div>
            <el-icon class="stat-icon active"><Star /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Search and Filter Section -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-header">
        <h3>搜索与筛选</h3>
        <el-button
          type="text"
          @click="toggleAdvancedSearch"
          class="toggle-advanced"
        >
          {{ showAdvancedSearch ? '收起高级搜索' : '展开高级搜索' }}
          <el-icon>
            <component :is="showAdvancedSearch ? 'ArrowUp' : 'ArrowDown'" />
          </el-icon>
        </el-button>
      </div>

      <!-- Basic Search -->
      <div class="basic-search">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索公司名称、统一社会信用代码、联系人..."
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.region" placeholder="选择地区" clearable>
              <el-option
                v-for="region in regionOptions"
                :key="region.value"
                :label="region.label"
                :value="region.value"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.industry" placeholder="选择行业" clearable>
              <el-option
                v-for="industry in industryOptions"
                :key="industry.value"
                :label="industry.label"
                :value="industry.value"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.verificationStatus" placeholder="验证状态" clearable>
              <el-option
                v-for="status in verificationStatusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- Advanced Search -->
      <div v-show="showAdvancedSearch" class="advanced-search">
        <el-divider />
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="注册资本范围">
              <el-input-number
                v-model="searchForm.minCapital"
                placeholder="最小值"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="至">
              <el-input-number
                v-model="searchForm.maxCapital"
                placeholder="最大值"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="成立时间">
              <el-date-picker
                v-model="searchForm.establishmentDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="员工规模">
              <el-select v-model="searchForm.employeeScale" placeholder="选择员工规模" clearable>
                <el-option label="1-50人" value="1-50" />
                <el-option label="51-200人" value="51-200" />
                <el-option label="201-500人" value="201-500" />
                <el-option label="500人以上" value="500+" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="工厂等级">
              <el-select v-model="searchForm.factoryLevel" placeholder="选择工厂等级" clearable>
                <el-option label="A级" value="A" />
                <el-option label="B级" value="B" />
                <el-option label="C级" value="C" />
                <el-option label="D级" value="D" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="评分范围">
              <el-slider
                v-model="searchForm.ratingRange"
                range
                :min="0"
                :max="5"
                :step="0.1"
                show-stops
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="供应商类型">
              <el-select v-model="searchForm.supplierType" placeholder="选择供应商类型" clearable>
                <el-option label="制造商" value="manufacturer" />
                <el-option label="代理商" value="agent" />
                <el-option label="贸易商" value="trader" />
                <el-option label="服务商" value="service" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <div class="advanced-actions">
              <el-button @click="resetSearch">重置</el-button>
              <el-button type="primary" @click="handleSearch">应用筛选</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- Main Content Area -->
    <el-card class="content-card" shadow="never">
      <!-- Toolbar -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-checkbox
            v-model="selectAll"
            @change="handleSelectAll"
            :indeterminate="isIndeterminate"
          >
            全选
          </el-checkbox>
          <span class="selected-count" v-if="selectedSuppliers.length > 0">
            已选择 {{ selectedSuppliers.length }} 项
          </span>
        </div>
        <div class="toolbar-center">
          <el-button-group>
            <el-button
              :type="viewMode === 'table' ? 'primary' : ''"
              @click="viewMode = 'table'"
            >
              <el-icon><List /></el-icon>
              列表视图
            </el-button>
            <el-button
              :type="viewMode === 'card' ? 'primary' : ''"
              @click="viewMode = 'card'"
            >
              <el-icon><Grid /></el-icon>
              卡片视图
            </el-button>
          </el-button-group>
        </div>
        <div class="toolbar-right">
          <el-dropdown
            v-if="selectedSuppliers.length > 0"
            @command="handleBulkAction"
          >
            <el-button type="primary">
              批量操作 <el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="verify">批量验证</el-dropdown-item>
                <el-dropdown-item command="export">导出选中</el-dropdown-item>
                <el-dropdown-item command="delete" divided>批量删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button @click="handleCompareSuppliers" :disabled="selectedSuppliers.length < 2">
            <el-icon><Scale /></el-icon>
            对比供应商
          </el-button>
        </div>
      </div>

      <!-- Table View -->
      <div v-if="viewMode === 'table'" class="table-container">
        <el-table
          ref="supplierTable"
          :data="paginatedSuppliers"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="companyName" label="公司名称" min-width="200" sortable="custom">
            <template #default="{ row }">
              <div class="company-info">
                <el-avatar
                  :src="row.companyLogo"
                  :size="32"
                  class="company-avatar"
                >
                  {{ row.companyName.charAt(0) }}
                </el-avatar>
                <div class="company-details">
                  <div class="company-name">{{ row.companyName }}</div>
                  <div class="company-id">ID: {{ row.supplierId }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="region" label="地区" width="120" />
          <el-table-column prop="industry" label="行业" width="120" />
          <el-table-column prop="supplierType" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getSupplierTypeTagType(row.supplierType)">
                {{ getSupplierTypeLabel(row.supplierType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="verificationStatus" label="验证状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getVerificationStatusType(row.verificationStatus)">
                {{ getVerificationStatusLabel(row.verificationStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="ratingScore" label="评分" width="100" sortable="custom">
            <template #default="{ row }">
              <el-rate
                v-model="row.ratingScore"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
              />
            </template>
          </el-table-column>
          <el-table-column prop="factoryLevel" label="工厂等级" width="100">
            <template #default="{ row }">
              <el-tag :type="getFactoryLevelType(row.factoryLevel)">
                {{ row.factoryLevel }}级
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="registeredCapital" label="注册资本" width="120" sortable="custom">
            <template #default="{ row }">
              {{ formatCurrency(row.registeredCapital) }}
            </template>
          </el-table-column>
          <el-table-column prop="establishmentDate" label="成立时间" width="120" sortable="custom">
            <template #default="{ row }">
              {{ formatDate(row.establishmentDate) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="text" @click="handleViewDetails(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="text" @click="handleEditSupplier(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="text" @click="handleInitiateInquiry(row)">
                <el-icon><ChatDotRound /></el-icon>
                询价
              </el-button>
              <el-dropdown @command="(command) => handleMoreActions(command, row)">
                <el-button type="text">
                  更多 <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="verify">验证供应商</el-dropdown-item>
                    <el-dropdown-item command="favorite">添加收藏</el-dropdown-item>
                    <el-dropdown-item command="export">导出信息</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- Card View -->
      <div v-if="viewMode === 'card'" class="card-container">
        <el-row :gutter="20">
          <el-col
            v-for="supplier in paginatedSuppliers"
            :key="supplier.supplierId"
            :span="8"
            class="supplier-card-col"
          >
            <el-card class="supplier-card" shadow="hover" @click="handleViewDetails(supplier)">
              <div class="card-header">
                <el-checkbox
                  v-model="supplier.selected"
                  @change="handleCardSelection(supplier)"
                  @click.stop
                />
                <el-avatar
                  :src="supplier.companyLogo"
                  :size="40"
                  class="company-avatar"
                >
                  {{ supplier.companyName.charAt(0) }}
                </el-avatar>
                <div class="card-actions">
                  <el-button type="text" @click.stop="handleEditSupplier(supplier)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button type="text" @click.stop="handleInitiateInquiry(supplier)">
                    <el-icon><ChatDotRound /></el-icon>
                  </el-button>
                </div>
              </div>
              <div class="card-content">
                <h4 class="company-name">{{ supplier.companyName }}</h4>
                <p class="company-info">{{ supplier.region }} · {{ supplier.industry }}</p>
                <div class="card-tags">
                  <el-tag
                    :type="getSupplierTypeTagType(supplier.supplierType)"
                    size="small"
                  >
                    {{ getSupplierTypeLabel(supplier.supplierType) }}
                  </el-tag>
                  <el-tag
                    :type="getVerificationStatusType(supplier.verificationStatus)"
                    size="small"
                  >
                    {{ getVerificationStatusLabel(supplier.verificationStatus) }}
                  </el-tag>
                </div>
                <div class="card-metrics">
                  <div class="metric">
                    <span class="metric-label">评分:</span>
                    <el-rate
                      v-model="supplier.ratingScore"
                      disabled
                      size="small"
                    />
                  </div>
                  <div class="metric">
                    <span class="metric-label">等级:</span>
                    <el-tag :type="getFactoryLevelType(supplier.factoryLevel)" size="small">
                      {{ supplier.factoryLevel }}级
                    </el-tag>
                  </div>
                </div>
                <div class="card-footer">
                  <span class="capital">注册资本: {{ formatCurrency(supplier.registeredCapital) }}</span>
                  <span class="establishment">成立: {{ formatDate(supplier.establishmentDate) }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- Pagination -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredSuppliers.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- Supplier Detail Modal -->
    <el-dialog
      v-model="detailModalVisible"
      :title="currentSupplier?.companyName || '供应商详情'"
      width="80%"
      :before-close="handleCloseDetail"
    >
      <SupplierDetail
        v-if="detailModalVisible && currentSupplier"
        :supplier="currentSupplier"
        @update="handleUpdateSupplier"
        @close="detailModalVisible = false"
      />
    </el-dialog>

    <!-- Supplier Form Modal -->
    <el-dialog
      v-model="formModalVisible"
      :title="isEditing ? '编辑供应商' : '添加供应商'"
      width="70%"
      :before-close="handleCloseForm"
    >
      <SupplierForm
        v-if="formModalVisible"
        :supplier="currentSupplier"
        :is-editing="isEditing"
        @save="handleSaveSupplier"
        @cancel="formModalVisible = false"
      />
    </el-dialog>

    <!-- Supplier Comparison Modal -->
    <el-dialog
      v-model="comparisonModalVisible"
      title="供应商对比"
      width="90%"
      :before-close="handleCloseComparison"
    >
      <SupplierComparison
        v-if="comparisonModalVisible"
        :suppliers="selectedSuppliersData"
        @close="comparisonModalVisible = false"
      />
    </el-dialog>

    <!-- Bulk Import Modal -->
    <el-dialog
      v-model="importModalVisible"
      title="批量导入供应商"
      width="60%"
    >
      <BulkImport
        v-if="importModalVisible"
        @success="handleImportSuccess"
        @close="importModalVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Import child components (these would be implemented separately)
// import SupplierDetail from './components/SupplierDetail.vue'
// import SupplierForm from './components/SupplierForm.vue'
// import SupplierComparison from './components/SupplierComparison.vue'
// import BulkImport from './components/BulkImport.vue'

// Reactive data
const loading = ref(false)
const viewMode = ref('table')
const showAdvancedSearch = ref(false)
const selectAll = ref(false)
const selectedSuppliers = ref([])

// Modal states
const detailModalVisible = ref(false)
const formModalVisible = ref(false)
const comparisonModalVisible = ref(false)
const importModalVisible = ref(false)
const isEditing = ref(false)
const currentSupplier = ref(null)

// Search form
const searchForm = reactive({
  keyword: '',
  region: '',
  industry: '',
  verificationStatus: '',
  minCapital: null,
  maxCapital: null,
  establishmentDateRange: [],
  employeeScale: '',
  factoryLevel: '',
  ratingRange: [0, 5],
  supplierType: ''
})

// Pagination
const pagination = reactive({
  currentPage: 1,
  pageSize: 20
})

// Sort configuration
const sortConfig = reactive({
  prop: '',
  order: ''
})

// Supplier data (mock data following the platform's pattern)
const supplierList = ref([
  {
    supplierId: 'SUP001',
    companyName: '深圳智能制造科技有限公司',
    companyLogo: '',
    region: '广东省深圳市',
    industry: '智能制造',
    registrationNumber: '91440300MA5DA1234X',
    unifiedSocialCreditCode: '91440300MA5DA1234X',
    establishmentDate: new Date('2018-03-15').getTime(),
    registeredCapital: 50000000,
    companyType: '有限责任公司',
    legalRepresentative: '张三',
    businessScope: '智能设备研发、生产、销售；工业自动化系统集成',
    registrationAddress: '深圳市南山区科技园南区',
    phone: '0755-12345678',
    email: '<EMAIL>',
    emailList: ['<EMAIL>', '<EMAIL>'],
    website: 'https://www.smartmfg.com',
    companyAddress: '深圳市南山区科技园南区智能制造大厦15楼',
    factoryLevel: 'A',
    levelScore: 95,
    serviceYears: 6,
    responseScore: 4.8,
    ratingScore: 4.7,
    complianceScore: 98,
    buyerIntent: 85,
    categoryName: '智能制造设备',
    homepageLink: 'https://www.smartmfg.com',
    searchKeywords: ['智能制造', '自动化', '工业4.0'],
    companySlogan: '智造未来，科技领先',
    promotionalImages: [],
    mainBusiness: '智能制造设备研发与生产',
    businessLicense: '',
    legalRepIdFront: '',
    legalRepIdBack: '',
    factoryOwnershipCert: '',
    factoryArea: 15000,
    employeeCount: 280,
    annualRevenue: *********,
    qualityCertifications: ['ISO9001', 'ISO14001', 'CE'],
    customerCaseStudies: [],
    factoryVideos: [],
    supplierType: 'manufacturer',
    mainBrands: ['SmartTech', 'AutoMaster'],
    agencyBrands: [],
    verificationStatus: 'verified',
    selected: false
  },
  {
    supplierId: 'SUP002',
    companyName: '上海精密仪器有限公司',
    companyLogo: '',
    region: '上海市',
    industry: '精密仪器',
    registrationNumber: '91310000MA1FL5678Y',
    unifiedSocialCreditCode: '91310000MA1FL5678Y',
    establishmentDate: new Date('2015-08-20').getTime(),
    registeredCapital: 30000000,
    companyType: '有限责任公司',
    legalRepresentative: '李四',
    businessScope: '精密仪器设备制造、销售、维修服务',
    registrationAddress: '上海市浦东新区张江高科技园区',
    phone: '021-87654321',
    email: '<EMAIL>',
    emailList: ['<EMAIL>'],
    website: 'https://www.precision-sh.com',
    companyAddress: '上海市浦东新区张江高科技园区创新大道100号',
    factoryLevel: 'B',
    levelScore: 88,
    serviceYears: 9,
    responseScore: 4.5,
    ratingScore: 4.3,
    complianceScore: 92,
    buyerIntent: 78,
    categoryName: '精密测量仪器',
    homepageLink: 'https://www.precision-sh.com',
    searchKeywords: ['精密仪器', '测量设备', '检测'],
    companySlogan: '精益求精，测量未来',
    promotionalImages: [],
    mainBusiness: '精密测量仪器制造',
    businessLicense: '',
    legalRepIdFront: '',
    legalRepIdBack: '',
    factoryOwnershipCert: '',
    factoryArea: 8000,
    employeeCount: 150,
    annualRevenue: 80000000,
    qualityCertifications: ['ISO9001', 'CNAS'],
    customerCaseStudies: [],
    factoryVideos: [],
    supplierType: 'manufacturer',
    mainBrands: ['PrecisionTech'],
    agencyBrands: ['Mitutoyo', 'Zeiss'],
    verificationStatus: 'pending',
    selected: false
  }
])

// Options for dropdowns
const regionOptions = ref([
  { value: '北京市', label: '北京市' },
  { value: '上海市', label: '上海市' },
  { value: '广东省深圳市', label: '深圳市' },
  { value: '广东省广州市', label: '广州市' },
  { value: '浙江省杭州市', label: '杭州市' },
  { value: '江苏省苏州市', label: '苏州市' }
])

const industryOptions = ref([
  { value: '智能制造', label: '智能制造' },
  { value: '精密仪器', label: '精密仪器' },
  { value: '电子元器件', label: '电子元器件' },
  { value: '机械设备', label: '机械设备' },
  { value: '新能源', label: '新能源' },
  { value: '生物医药', label: '生物医药' }
])

const verificationStatusOptions = ref([
  { value: 'verified', label: '已验证' },
  { value: 'pending', label: '待验证' },
  { value: 'rejected', label: '验证失败' },
  { value: 'unverified', label: '未验证' }
])

// Computed properties
const supplierStats = computed(() => {
  const total = supplierList.value.length
  const verified = supplierList.value.filter(s => s.verificationStatus === 'verified').length
  const pending = supplierList.value.filter(s => s.verificationStatus === 'pending').length
  const active = supplierList.value.filter(s => s.ratingScore >= 4.0).length

  return { total, verified, pending, active }
})

const filteredSuppliers = computed(() => {
  let filtered = [...supplierList.value]

  // Apply search filters
  if (searchForm.keyword) {
    const keyword = searchForm.keyword.toLowerCase()
    filtered = filtered.filter(supplier =>
      supplier.companyName.toLowerCase().includes(keyword) ||
      supplier.unifiedSocialCreditCode.includes(keyword) ||
      supplier.legalRepresentative.toLowerCase().includes(keyword)
    )
  }

  if (searchForm.region) {
    filtered = filtered.filter(supplier => supplier.region === searchForm.region)
  }

  if (searchForm.industry) {
    filtered = filtered.filter(supplier => supplier.industry === searchForm.industry)
  }

  if (searchForm.verificationStatus) {
    filtered = filtered.filter(supplier => supplier.verificationStatus === searchForm.verificationStatus)
  }

  // Apply advanced filters
  if (searchForm.minCapital !== null) {
    filtered = filtered.filter(supplier => supplier.registeredCapital >= searchForm.minCapital)
  }

  if (searchForm.maxCapital !== null) {
    filtered = filtered.filter(supplier => supplier.registeredCapital <= searchForm.maxCapital)
  }

  if (searchForm.factoryLevel) {
    filtered = filtered.filter(supplier => supplier.factoryLevel === searchForm.factoryLevel)
  }

  if (searchForm.supplierType) {
    filtered = filtered.filter(supplier => supplier.supplierType === searchForm.supplierType)
  }

  // Apply rating range filter
  filtered = filtered.filter(supplier =>
    supplier.ratingScore >= searchForm.ratingRange[0] &&
    supplier.ratingScore <= searchForm.ratingRange[1]
  )

  // Apply sorting
  if (sortConfig.prop && sortConfig.order) {
    filtered.sort((a, b) => {
      const aVal = a[sortConfig.prop]
      const bVal = b[sortConfig.prop]
      const modifier = sortConfig.order === 'ascending' ? 1 : -1

      if (aVal < bVal) return -1 * modifier
      if (aVal > bVal) return 1 * modifier
      return 0
    })
  }

  return filtered
})

const paginatedSuppliers = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredSuppliers.value.slice(start, end)
})

const isIndeterminate = computed(() => {
  const selectedCount = selectedSuppliers.value.length
  const totalCount = paginatedSuppliers.value.length
  return selectedCount > 0 && selectedCount < totalCount
})

const selectedSuppliersData = computed(() => {
  return supplierList.value.filter(supplier =>
    selectedSuppliers.value.includes(supplier.supplierId)
  )
})

// Methods
const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value
}

const handleSearch = () => {
  loading.value = true
  pagination.currentPage = 1

  // Simulate API call
  setTimeout(() => {
    loading.value = false
    ElMessage.success('搜索完成')
  }, 300)
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    region: '',
    industry: '',
    verificationStatus: '',
    minCapital: null,
    maxCapital: null,
    establishmentDateRange: [],
    employeeScale: '',
    factoryLevel: '',
    ratingRange: [0, 5],
    supplierType: ''
  })
  handleSearch()
}

const handleSelectAll = (value) => {
  if (value) {
    selectedSuppliers.value = paginatedSuppliers.value.map(s => s.supplierId)
  } else {
    selectedSuppliers.value = []
  }
  // Update selected state in supplier objects
  paginatedSuppliers.value.forEach(supplier => {
    supplier.selected = value
  })
}

const handleSelectionChange = (selection) => {
  selectedSuppliers.value = selection.map(s => s.supplierId)
  selectAll.value = selection.length === paginatedSuppliers.value.length
}

const handleCardSelection = (supplier) => {
  if (supplier.selected) {
    if (!selectedSuppliers.value.includes(supplier.supplierId)) {
      selectedSuppliers.value.push(supplier.supplierId)
    }
  } else {
    const index = selectedSuppliers.value.indexOf(supplier.supplierId)
    if (index > -1) {
      selectedSuppliers.value.splice(index, 1)
    }
  }
  selectAll.value = selectedSuppliers.value.length === paginatedSuppliers.value.length
}

const handleSortChange = ({ prop, order }) => {
  sortConfig.prop = prop
  sortConfig.order = order
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
}

// Supplier actions
const handleAddSupplier = () => {
  currentSupplier.value = null
  isEditing.value = false
  formModalVisible.value = true
}

const handleEditSupplier = (supplier) => {
  currentSupplier.value = { ...supplier }
  isEditing.value = true
  formModalVisible.value = true
}

const handleViewDetails = (supplier) => {
  currentSupplier.value = supplier
  detailModalVisible.value = true
}

const handleSaveSupplier = (supplierData) => {
  if (isEditing.value) {
    // Update existing supplier
    const index = supplierList.value.findIndex(s => s.supplierId === supplierData.supplierId)
    if (index > -1) {
      supplierList.value[index] = { ...supplierData }
      ElMessage.success('供应商信息更新成功')
    }
  } else {
    // Add new supplier
    const newSupplier = {
      ...supplierData,
      supplierId: `SUP${Date.now()}`,
      selected: false
    }
    supplierList.value.push(newSupplier)
    ElMessage.success('供应商添加成功')
  }
  formModalVisible.value = false
}

const handleUpdateSupplier = (supplierData) => {
  const index = supplierList.value.findIndex(s => s.supplierId === supplierData.supplierId)
  if (index > -1) {
    supplierList.value[index] = { ...supplierData }
    ElMessage.success('供应商信息更新成功')
  }
}

const handleInitiateInquiry = (supplier) => {
  ElMessage.info(`正在为 ${supplier.companyName} 发起询价...`)
  // This would integrate with the existing RFQ system
}

const handleCompareSuppliers = () => {
  if (selectedSuppliers.value.length < 2) {
    ElMessage.warning('请至少选择2个供应商进行对比')
    return
  }
  comparisonModalVisible.value = true
}

const handleMoreActions = (command, supplier) => {
  switch (command) {
    case 'verify':
      handleVerifySupplier(supplier)
      break
    case 'favorite':
      handleAddToFavorites(supplier)
      break
    case 'export':
      handleExportSupplier(supplier)
      break
    case 'delete':
      handleDeleteSupplier(supplier)
      break
  }
}

const handleVerifySupplier = (supplier) => {
  ElMessageBox.confirm(
    `确认验证供应商 ${supplier.companyName}？`,
    '确认验证',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    supplier.verificationStatus = 'verified'
    ElMessage.success('供应商验证成功')
  }).catch(() => {
    ElMessage.info('已取消验证')
  })
}

const handleAddToFavorites = (supplier) => {
  ElMessage.success(`已将 ${supplier.companyName} 添加到收藏`)
}

const handleExportSupplier = (supplier) => {
  ElMessage.info(`正在导出 ${supplier.companyName} 的信息...`)
}

const handleDeleteSupplier = (supplier) => {
  ElMessageBox.confirm(
    `确认删除供应商 ${supplier.companyName}？此操作不可恢复。`,
    '确认删除',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = supplierList.value.findIndex(s => s.supplierId === supplier.supplierId)
    if (index > -1) {
      supplierList.value.splice(index, 1)
      ElMessage.success('供应商删除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// Bulk operations
const handleBulkAction = (command) => {
  if (selectedSuppliers.value.length === 0) {
    ElMessage.warning('请先选择要操作的供应商')
    return
  }

  switch (command) {
    case 'verify':
      handleBulkVerify()
      break
    case 'export':
      handleBulkExport()
      break
    case 'delete':
      handleBulkDelete()
      break
  }
}

const handleBulkVerify = () => {
  ElMessageBox.confirm(
    `确认批量验证选中的 ${selectedSuppliers.value.length} 个供应商？`,
    '批量验证',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    selectedSuppliersData.value.forEach(supplier => {
      supplier.verificationStatus = 'verified'
    })
    ElMessage.success(`已成功验证 ${selectedSuppliers.value.length} 个供应商`)
    selectedSuppliers.value = []
    selectAll.value = false
  }).catch(() => {
    ElMessage.info('已取消批量验证')
  })
}

const handleBulkExport = () => {
  ElMessage.info(`正在导出选中的 ${selectedSuppliers.value.length} 个供应商信息...`)
}

const handleBulkDelete = () => {
  ElMessageBox.confirm(
    `确认删除选中的 ${selectedSuppliers.value.length} 个供应商？此操作不可恢复。`,
    '批量删除',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    selectedSuppliers.value.forEach(supplierId => {
      const index = supplierList.value.findIndex(s => s.supplierId === supplierId)
      if (index > -1) {
        supplierList.value.splice(index, 1)
      }
    })
    ElMessage.success(`已成功删除 ${selectedSuppliers.value.length} 个供应商`)
    selectedSuppliers.value = []
    selectAll.value = false
  }).catch(() => {
    ElMessage.info('已取消批量删除')
  })
}

// Import/Export operations
const handleBulkImport = () => {
  importModalVisible.value = true
}

const handleExport = () => {
  ElMessage.info('正在导出所有供应商数据...')
}

const handleImportSuccess = (importedData) => {
  supplierList.value.push(...importedData)
  ElMessage.success(`成功导入 ${importedData.length} 个供应商`)
  importModalVisible.value = false
}

// Modal handlers
const handleCloseDetail = () => {
  detailModalVisible.value = false
  currentSupplier.value = null
}

const handleCloseForm = () => {
  formModalVisible.value = false
  currentSupplier.value = null
  isEditing.value = false
}

const handleCloseComparison = () => {
  comparisonModalVisible.value = false
}

// Utility functions
const getSupplierTypeTagType = (type) => {
  const typeMap = {
    manufacturer: 'success',
    agent: 'warning',
    trader: 'info',
    service: 'primary'
  }
  return typeMap[type] || 'info'
}

const getSupplierTypeLabel = (type) => {
  const labelMap = {
    manufacturer: '制造商',
    agent: '代理商',
    trader: '贸易商',
    service: '服务商'
  }
  return labelMap[type] || type
}

const getVerificationStatusType = (status) => {
  const statusMap = {
    verified: 'success',
    pending: 'warning',
    rejected: 'danger',
    unverified: 'info'
  }
  return statusMap[status] || 'info'
}

const getVerificationStatusLabel = (status) => {
  const labelMap = {
    verified: '已验证',
    pending: '待验证',
    rejected: '验证失败',
    unverified: '未验证'
  }
  return labelMap[status] || status
}

const getFactoryLevelType = (level) => {
  const levelMap = {
    A: 'success',
    B: 'primary',
    C: 'warning',
    D: 'danger'
  }
  return levelMap[level] || 'info'
}

const formatCurrency = (amount) => {
  if (!amount) return '0'
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0
  }).format(amount)
}

const formatDate = (timestamp) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleDateString('zh-CN')
}

// Lifecycle
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.supplier-pool-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* Header Section */
.header-card {
  margin-bottom: 20px;
  border: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

/* Statistics Section */
.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  position: relative;
  overflow: hidden;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  color: #e4e7ed;
}

.stat-icon.verified {
  color: #67c23a;
}

.stat-icon.pending {
  color: #e6a23c;
}

.stat-icon.active {
  color: #409eff;
}

/* Filter Section */
.filter-card {
  margin-bottom: 20px;
  border: none;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.toggle-advanced {
  color: #409eff;
}

.basic-search {
  margin-bottom: 20px;
}

.advanced-search {
  padding-top: 20px;
}

.advanced-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  align-items: flex-end;
  height: 100%;
  padding-top: 28px;
}

/* Content Section */
.content-card {
  border: none;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.selected-count {
  color: #409eff;
  font-size: 14px;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

/* Table View */
.table-container {
  margin-bottom: 20px;
}

.company-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.company-avatar {
  flex-shrink: 0;
}

.company-details {
  flex: 1;
  min-width: 0;
}

.company-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.company-id {
  font-size: 12px;
  color: #909399;
}

/* Card View */
.card-container {
  margin-bottom: 20px;
}

.supplier-card-col {
  margin-bottom: 20px;
}

.supplier-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #ebeef5;
}

.supplier-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.company-info {
  color: #909399;
  font-size: 14px;
  margin-bottom: 12px;
}

.card-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.card-metrics {
  margin-bottom: 12px;
}

.metric {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  min-width: 40px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  padding-top: 12px;
  border-top: 1px solid #f0f2f5;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .supplier-card-col {
    width: 50%;
  }
}

@media (max-width: 768px) {
  .supplier-pool-container {
    padding: 12px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .action-buttons {
    width: 100%;
    justify-content: flex-start;
  }

  .toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .toolbar-center {
    justify-content: flex-start;
  }

  .basic-search .el-row {
    flex-direction: column;
  }

  .basic-search .el-col {
    width: 100% !important;
    margin-bottom: 12px;
  }

  .supplier-card-col {
    width: 100%;
  }
}

/* Custom Element Plus Overrides */
.el-table .el-table__cell {
  padding: 12px 0;
}

.el-card__body {
  padding: 24px;
}

.el-form-item {
  margin-bottom: 16px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* Loading and Empty States */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
}

.empty-container .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* Animation */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from {
  transform: translateY(-20px);
  opacity: 0;
}

.slide-leave-to {
  transform: translateY(20px);
  opacity: 0;
}
</style>