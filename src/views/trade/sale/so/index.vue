<template>
  <el-card class="order-list-container">
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="销售订单号">
          <el-input v-model="queryParams.orderNo" placeholder="请输入销售订单号" clearable />
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input v-model="queryParams.customerName" placeholder="请输入客户名称" clearable />
        </el-form-item>
        <el-form-item label="供应商">
          <el-input v-model="queryParams.supplier" placeholder="请输入供应商" clearable />
        </el-form-item>
        <el-form-item label="物料名称">
          <el-input v-model="queryParams.productName" placeholder="请输入物料名称" clearable />
        </el-form-item>
        <el-form-item label="物料型号">
          <el-input v-model="queryParams.model" placeholder="请输入物料型号" clearable />
        </el-form-item>
        <el-form-item label="物料分类">
          <el-input v-model="queryParams.category" placeholder="请输入物料分类" clearable />
        </el-form-item>
        <el-form-item label="品牌">
          <el-input v-model="queryParams.brand" placeholder="请输入品牌" clearable />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="待确认" value="pending_confirmation" />
            <el-option label="确认中" value="confirming" />
            <el-option label="执行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="取消中" value="cancelling" />
            <el-option label="异常待处理" value="exception_pending" />
            <el-option label="已归档" value="archived" />
          </el-select>
        </el-form-item>
        <el-form-item label="下单时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- View Switcher -->
    <div style="margin-bottom: 20px; display: flex; justify-content: flex-end;">
      <el-radio-group v-model="currentView" @change="handleViewChange">
        <el-radio-button label="order">订单视图</el-radio-button>
        <el-radio-button label="product">物料视图</el-radio-button>
      </el-radio-group>
    </div>

    <!-- Dynamically rendered view -->
    <div class="view-content-container" v-loading="loading">
      <OrderView v-if="currentView === 'order'" :filters="queryParams" />
      <ProductView v-if="currentView === 'product'" :filters="queryParams" />
    </div>

  </el-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import OrderView from './OrderView.vue'
import ProductView from './ProductView.vue'

// 查询参数
const queryParams = reactive({
  orderNo: '',
  customerName: '',
  supplier: '',
  status: '',
  productName: '',
  model: '',
  category: '',
  brand: '',
  pageNum: 1,
  pageSize: 10
})

const dateRange = ref([])
const loading = ref(false)
const currentView = ref('order')

// 获取销售订单列表
const getSaleOrderList = () => {
  loading.value = true
  console.log('Fetching list with params:', JSON.parse(JSON.stringify(queryParams)), 'Current view:', currentView.value)
  // 模拟 API 调用
  setTimeout(() => {
    console.log(`Data fetching for ${currentView.value} view would happen here.`);
    loading.value = false
  }, 300)
}

// 查询按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getSaleOrderList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.orderNo = ''
  queryParams.customerName = ''
  queryParams.supplier = ''
  queryParams.status = ''
  queryParams.productName = ''
  queryParams.model = ''
  queryParams.category = ''
  queryParams.brand = ''
  dateRange.value = []
  handleQuery()
}

// 视图切换处理
const handleViewChange = (view) => {
  console.log('Switched to view:', view)
  handleQuery()
}

// 组件挂载时获取订单列表
onMounted(() => {
  getSaleOrderList()
})
</script>

<style scoped>
.order-list-container {
  margin: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.view-content-container {
  min-height: 300px;
}
</style>