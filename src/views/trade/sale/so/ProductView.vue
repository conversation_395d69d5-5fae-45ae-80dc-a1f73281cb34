<template>
  <div>
    <!-- Button container -->
    <div style="display: flex; justify-content: space-between; margin-bottom: 16px;">
      <!-- Bulk actions dropdown -->
      <el-dropdown @command="handleBulkCommand">
        <el-button type="primary">
          批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="ship">发货</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <!-- Column configuration button -->
      <el-button @click="drawer = true" type="primary">
        配置列
      </el-button>
    </div>

    <!-- Column configuration drawer -->
    <el-drawer
      title="配置列"
      v-model="drawer"
      direction="rtl"
      size="300px">
      <div style="padding: 0 20px">
        <div v-for="column in availableColumns" :key="column.prop">
          <el-checkbox v-model="column.visible">{{ column.label }}</el-checkbox>
        </div>
      </div>
    </el-drawer>

    <!-- Product table -->
    <el-table 
      :data="productData" 
      style="width: 100%" 
      border 
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      
      <template v-for="column in visibleColumns" :key="column.prop">
        <el-table-column
          v-if="column.visible"
          :prop="column.prop"
          :label="column.label"
          :min-width="column.minWidth"
        >
          <template v-if="column.prop === 'unitPrice' || column.prop === 'totalPrice'" #default="scope">
            ¥{{ formatPrice(scope.row[column.prop]) }}
          </template>
          <template v-else-if="column.prop === 'orderStatus'" #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
              {{ getOrderStatusLabel(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
      </template>
      
      <!-- <el-table-column label="操作" min-width="180" fixed="right">
        <template #default="scope">
          <el-button size="small" link type="primary" @click="handleViewOrderDetails(scope.row)">查看订单</el-button>
          <el-button 
            v-if="scope.row.orderStatus === 'pending_confirmation'"
            size="small"
            link
            type="primary"
            @click="handleConfirmProduct(scope.row)"
          >
            确认
          </el-button>
          <el-button
            v-if="canShipProduct(scope.row)"
            size="small"
            link
            type="primary"
            @click="handleShipProduct(scope.row)"
          >
            发货
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <!-- Pagination -->
    <div class="pagination-container" style="margin-top: 15px;">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { ArrowDown } from '@element-plus/icons-vue';

const drawer = ref(false);
const selectedProducts = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// Helper function to format price
const formatPrice = (price) => {
  if (price === null || price === undefined) return '0.00';
  return price.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// Available columns configuration
const availableColumns = reactive([
  { prop: 'productName', label: '物料名称', visible: true, minWidth: 150 },
  { prop: 'model', label: '型号', visible: true, minWidth: 120 },
  { prop: 'brand', label: '品牌', visible: true, minWidth: 100 },
  { prop: 'category', label: '分类', visible: true, minWidth: 100 },
  { prop: 'quantity', label: '数量', visible: true, minWidth: 80 },
  { prop: 'supplier', label: '供应商', visible: true, minWidth: 150 },
  { prop: 'preparingQuantity', label: '备货中数量', visible: false, minWidth: 120 },
  { prop: 'inTransitQuantity', label: '在途数量', visible: false, minWidth: 100 },
  { prop: 'shippedQuantity', label: '已发货数量', visible: true, minWidth: 120 },
  { prop: 'acceptedQuantity', label: '已收货数量', visible: true, minWidth: 120 },
  { prop: 'cancelledQuantity', label: '已取消数量', visible: true, minWidth: 120 },
  { prop: 'unitPrice', label: '单价', visible: true, minWidth: 100 },
  { prop: 'totalPrice', label: '总价', visible: true, minWidth: 100 },
  { prop: 'orderNo', label: '所属销售订单', visible: true, minWidth: 150 },
  { prop: 'orderStatus', label: '订单状态', visible: true, minWidth: 120 },
  { prop: 'logisticsStatus', label: '物流状态', visible: true, minWidth: 120 },
  { prop: 'financialStatus', label: '财务状态', visible: true, minWidth: 120 },
  { prop: 'customer', label: '客户', visible: true, minWidth: 150 },
]);

// Computed property for visible columns
const visibleColumns = computed(() => availableColumns.filter(col => col.visible));

// Mock product data
const productData = ref([
  {
    id: 'p1',
    productName: '智能控制器',
    model: 'CT-X100',
    quantity: 50,
    preparingQuantity: 30,
    inTransitQuantity: 10,
    acceptedQuantity: 10,
    cancelledQuantity: 0,
    unitPrice: 2500.00,
    totalPrice: 125000.00,
    orderNo: 'PO20240501001',
    orderStatus: 'pending_confirmation',
    logisticsStatus: '待发货',
    financialStatus: '待付款',
    customer: '广州家电有限公司',
  },
  {
    id: 'p2',
    productName: '传感器模块',
    model: 'SM-500',
    quantity: 25,
    preparingQuantity: 15,
    inTransitQuantity: 5,
    acceptedQuantity: 5,
    cancelledQuantity: 0,
    unitPrice: 2500.00,
    totalPrice: 62500.00,
    orderNo: 'PO20240501001',
    orderStatus: 'pending_confirmation',
    logisticsStatus: '部分发货',
    financialStatus: '部分付款',
    customer: '广州家电有限公司',
  },
  {
    id: 'p3',
    productName: '中央处理单元',
    model: 'CPU-S2000',
    quantity: 20,
    preparingQuantity: 20,
    inTransitQuantity: 0,
    acceptedQuantity: 0,
    cancelledQuantity: 0,
    unitPrice: 4000.00,
    totalPrice: 80000.00,
    orderNo: 'PO20240428002',
    orderStatus: 'confirming',
    logisticsStatus: '备货中',
    financialStatus: '已付款',
    customer: '北京智能家居科技有限公司',
  },
  {
    id: 'p4',
    productName: '工业控制器',
    model: 'IC-800',
    quantity: 15,
    preparingQuantity: 0,
    inTransitQuantity: 15,
    acceptedQuantity: 0,
    cancelledQuantity: 0,
    unitPrice: 6000.00,
    totalPrice: 90000.00,
    orderNo: 'PO20240420003',
    orderStatus: 'in_progress',
    logisticsStatus: '运输中',
    financialStatus: '已付款',
    customer: '上海机械设备有限公司',
  },
  {
    id: 'p5',
    productName: '工业传感器',
    model: 'IS-300',
    quantity: 15,
    preparingQuantity: 0,
    inTransitQuantity: 15,
    acceptedQuantity: 0,
    cancelledQuantity: 0,
    unitPrice: 3000.00,
    totalPrice: 45000.00,
    orderNo: 'PO20240420003',
    orderStatus: 'in_progress',
    logisticsStatus: '运输中',
    financialStatus: '已付款',
    customer: '上海机械设备有限公司',
  },
  {
    id: 'p6',
    productName: '自动化控制系统',
    model: 'ACS-500',
    quantity: 5,
    preparingQuantity: 0,
    inTransitQuantity: 0,
    acceptedQuantity: 5,
    cancelledQuantity: 0,
    unitPrice: 11000.00,
    totalPrice: 55000.00,
    orderNo: 'PO20240410004',
    orderStatus: 'completed',
    logisticsStatus: '已签收',
    financialStatus: '已付款',
    customer: '深圳智能制造有限公司',
  },
  {
    id: 'p7',
    productName: '工业传感器阵列',
    model: 'ISA-200',
    quantity: 6,
    preparingQuantity: 0,
    inTransitQuantity: 0,
    acceptedQuantity: 0,
    cancelledQuantity: 6,
    unitPrice: 7000.00,
    totalPrice: 42000.00,
    orderNo: 'PO20240405005',
    orderStatus: 'cancelled',
    logisticsStatus: '已取消',
    financialStatus: '已退款',
    customer: '广州机电工程有限公司',
  },
  {
    id: 'p8',
    productName: '数据处理单元',
    model: 'DPU-A100',
    quantity: 10,
    preparingQuantity: 0,
    inTransitQuantity: 0,
    acceptedQuantity: 0,
    cancelledQuantity: 0,
    unitPrice: 8000.00,
    totalPrice: 80000.00,
    orderNo: 'PO20240402006',
    orderStatus: 'cancelling',
    logisticsStatus: '待发货',
    financialStatus: '退款中',
    customer: '天津电子科技有限公司',
  },
  {
    id: 'p9',
    productName: '信号放大器',
    model: 'SA-50',
    quantity: 20,
    preparingQuantity: 0,
    inTransitQuantity: 0,
    acceptedQuantity: 0,
    cancelledQuantity: 0,
    unitPrice: 2000.00,
    totalPrice: 40000.00,
    orderNo: 'PO20240402006',
    orderStatus: 'cancelling',
    logisticsStatus: '待发货',
    financialStatus: '退款中',
    customer: '天津电子科技有限公司',
  },
  {
    id: 'p10',
    productName: '高性能控制器',
    model: 'HPC-800',
    quantity: 5,
    preparingQuantity: 5,
    inTransitQuantity: 0,
    acceptedQuantity: 0,
    cancelledQuantity: 0,
    unitPrice: 10000.00,
    totalPrice: 50000.00,
    orderNo: 'PO20240328007',
    orderStatus: 'exception_pending',
    logisticsStatus: '待发货',
    financialStatus: '待处理',
    customer: '重庆智能设备有限公司',
  },
  {
    id: 'p11',
    productName: '自动化生产线控制系统',
    model: 'APLCS-2000',
    quantity: 1,
    preparingQuantity: 0,
    inTransitQuantity: 0,
    acceptedQuantity: 1,
    cancelledQuantity: 0,
    unitPrice: 100000.00,
    totalPrice: 100000.00,
    orderNo: 'PO20240320008',
    orderStatus: 'archived',
    logisticsStatus: '已归档',
    financialStatus: '已归档',
    customer: '北京自动化设备有限公司',
  },
  {
    id: 'p12',
    productName: '环境传感器组',
    model: 'ESG-100',
    quantity: 10,
    preparingQuantity: 0,
    inTransitQuantity: 0,
    acceptedQuantity: 10,
    cancelledQuantity: 0,
    unitPrice: 3000.00,
    totalPrice: 30000.00,
    orderNo: 'PO20240320008',
    orderStatus: 'archived',
    logisticsStatus: '已归档',
    financialStatus: '已归档',
    customer: '北京自动化设备有限公司',
  },
  {
    id: 'p13',
    productName: '数据采集终端',
    model: 'DAT-5000',
    quantity: 5,
    preparingQuantity: 0,
    inTransitQuantity: 0,
    acceptedQuantity: 5,
    cancelledQuantity: 0,
    unitPrice: 10000.00,
    totalPrice: 50000.00,
    orderNo: 'PO20240320008',
    orderStatus: 'archived',
    logisticsStatus: '已归档',
    financialStatus: '已归档',
    customer: '北京自动化设备有限公司',
  },
]);

// Event handlers
const handleSelectionChange = (selection) => {
  selectedProducts.value = selection;
  console.log('Selected products:', selection);
};

const handleViewOrderDetails = (row) => {
  console.log('View order details for:', row.orderNo);
};

const canShipProduct = (product) => {
  return product.preparingQuantity > 0;
};

const handleShipProduct = (product) => {
  console.log('Shipping product:', product);
};

const handleBulkCommand = (command) => {
  if (command === 'ship') {
    const shippableProducts = selectedProducts.value.filter(p => p.preparingQuantity > 0);
    if (shippableProducts.length === 0) {
      console.warn('No products selected for shipping or none have pending stock');
      return;
    }
    console.log('Bulk shipping selected products:', shippableProducts);
  }
};

const handleSizeChange = (val) => {
  pageSize.value = val;
  console.log('Page size changed to:', val);
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  console.log('Current page changed to:', val);
};

// Order status helper functions
const getOrderStatusLabel = (status) => {
  const statusMap = {
    'pending_confirmation': '待确认',
    'confirming': '确认中',
    'in_progress': '执行中', 
    'completed': '已完成',
    'cancelled': '已取消',
    'cancelling': '取消中',
    'exception_pending': '异常待处理',
    'archived': '已归档'
  };
  return statusMap[status] || status;
};

const getOrderStatusType = (status) => {
  const typeMap = {
    'pending_confirmation': 'info',
    'confirming': 'warning',
    'in_progress': 'primary',
    'completed': 'success', 
    'cancelled': 'danger',
    'cancelling': 'warning',
    'exception_pending': 'danger',
    'archived': 'info'
  };
  return typeMap[status] || '';
};

const handleConfirmProduct = (product) => {
  console.log('Confirming product:', product);
};
</script>

<style scoped>
.pagination-container {
  display: flex;
  justify-content: flex-end;
}
</style>
