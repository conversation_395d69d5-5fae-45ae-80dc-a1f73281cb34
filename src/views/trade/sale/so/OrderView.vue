<template>
  <div>
    <!-- Button container -->
    <div style="display: flex; justify-content: space-between; margin-bottom: 16px;">
      <div>
        <el-button @click="toggleAllRows" type="primary" style="margin-right: 10px;">
          {{ allRowsExpanded ? '全部收起' : '全部展开' }}
        </el-button>
        <el-dropdown @command="handleBatchCommand">
          <el-button type="primary">
            批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="confirm">确认</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <el-button @click="drawer = true" type="primary">配置列</el-button>
    </div>

    <!-- Column configuration drawer -->
    <el-drawer title="配置列" v-model="drawer" direction="rtl" size="300px">
      <div style="padding: 0 20px">
        <h4>订单列</h4>
        <div v-for="column in availableOrderColumns" :key="column.prop">
          <el-checkbox v-model="column.visible">{{ column.label }}</el-checkbox>
        </div>
        <el-divider />
        <h4>物料列</h4>
        <div v-for="column in availableProductColumns" :key="column.prop">
          <el-checkbox v-model="column.visible">{{ column.label }}</el-checkbox>
        </div>
      </div>
    </el-drawer>

    <!-- Order table with expandable product rows -->
    <el-table ref="orderTableRef" :data="orderData" style="width: 100%" border row-key="id" @expand-change="handleExpandChange" @selection-change="handleOrderSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column type="expand">
        <template #default="props">
          <div style="padding: 10px 20px">
            <el-table :data="props.row.products" border style="width: 100%">
              <template v-for="column in visibleProductColumns" :key="column.prop">
                <el-table-column v-if="column.visible" :prop="column.prop" :label="column.label" :min-width="column.minWidth">
                  <template v-if="column.prop === 'unitPrice' || column.prop === 'totalPrice' || column.prop === 'supplierQuote'" #default="scope">
                    ¥{{ formatPrice(scope.row[column.prop]) }}
                  </template>
                </el-table-column>
              </template>
            </el-table>
          </div>
        </template>
      </el-table-column>

      <!-- Order columns -->
      <template v-for="column in visibleOrderColumns" :key="column.prop">
        <el-table-column v-if="column.visible" :prop="column.prop" :label="column.label" :min-width="column.minWidth">
          <template v-if="column.prop === 'orderNo'" #default="scope">
            <el-button link type="primary" @click.stop="handleViewOrderDetails(scope.row)">
              {{ scope.row.orderNo }}
            </el-button>
          </template>
          <template v-else-if="column.prop === 'totalAmount' || column.prop === 'totalCost'" #default="scope">
            ¥{{ formatPrice(scope.row[column.prop]) }}
          </template>
          <template v-else-if="column.prop === 'profitMargin'" #default="scope">
            {{ scope.row.profitMargin }}%
          </template>
          <template v-else-if="column.prop === 'paymentProgress'" #default="scope">
            <el-progress :percentage="scope.row.paymentProgress" :format="percentFormat" />
          </template>
          <template v-else-if="column.prop === 'status'" #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.status)">
              {{ getOrderStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </template>

      <!-- Operations column -->
      <el-table-column label="操作" min-width="150" fixed="right">
        <template #default="scope">
          <el-button v-if="scope.row.status === 'pending_confirmation'" size="small" link type="primary" @click="handleConfirmOrder(scope.row)">确认</el-button>
          <el-button v-if="canDownloadContract(scope.row)" size="small" link type="primary" @click="handleDownloadContract(scope.row)">下载合同</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- Pagination -->
    <div class="pagination-container" style="margin-top: 15px;">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ArrowDown } from '@element-plus/icons-vue';

const router = useRouter();

const drawer = ref(false);
const orderTableRef = ref(null); // Ref for the main order table
const allRowsExpanded = ref(false); // Tracks if all rows are expanded
const selectedOrders = ref([]); // To store selected orders from the outer table
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// Helper function to format price
const formatPrice = (price) => {
  if (price === null || price === undefined) return '0.00';
  return price.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// Percentage format function for payment progress
const percentFormat = (percentage) => {
  return percentage + '%';
};

// Available columns for order table (outer table)
const availableOrderColumns = reactive([
  { prop: 'orderNo', label: '销售订单号', visible: true, minWidth: 180 },
  { prop: 'customerName', label: '客户名称', visible: true, minWidth: 150 },
  { prop: 'status', label: '订单状态', visible: true, minWidth: 120 },
  { prop: 'productCount', label: '物料数量', visible: true, minWidth: 100 },
  { prop: 'totalCost', label: '总成本', visible: true, minWidth: 120 },
  { prop: 'totalAmount', label: '总金额', visible: true, minWidth: 120 },
  { prop: 'profitMargin', label: '利润率', visible: true, minWidth: 100 },
  // { prop: 'paymentProgress', label: '付款进度', visible: true, minWidth: 150 },
  { prop: 'createTime', label: '下单时间', visible: true, minWidth: 150 },
]);

// Available columns for product table (inner table)
const availableProductColumns = reactive([
  { prop: 'productName', label: '物料名称', visible: true, minWidth: 150 },
  { prop: 'model', label: '型号', visible: true, minWidth: 120 },
  { prop: 'brand', label: '品牌', visible: true, minWidth: 100 },
  { prop: 'category', label: '分类', visible: true, minWidth: 100 },
  { prop: 'quantity', label: '数量', visible: true, minWidth: 80 },
  { prop: 'supplier', label: '供应商', visible: true, minWidth: 150 },
  // { prop: 'preparingQuantity', label: '备货中数量', visible: true, minWidth: 120 },
  // { prop: 'inTransitQuantity', label: '在途数量', visible: true, minWidth: 100 },
  { prop: 'shippedQuantity', label: '已发货数量', visible: true, minWidth: 120 },
  { prop: 'receivedQuantity', label: '已收货数量', visible: true, minWidth: 120 },
  { prop: 'cancelledQuantity', label: '已取消数量', visible: true, minWidth: 120 },
  { prop: 'supplierQuote', label: '供应商报价', visible: true, minWidth: 120 },
  { prop: 'unitPrice', label: '销售价', visible: true, minWidth: 100 },
  { prop: 'totalPrice', label: '总价', visible: true, minWidth: 100 },
  { prop: 'logisticsStatus', label: '物流状态', visible: true, minWidth: 120 },
  { prop: 'financialStatus', label: '财务状态', visible: true, minWidth: 120 },
]);

// Computed properties for visible columns
const visibleOrderColumns = computed(() => availableOrderColumns.filter(col => col.visible));
const visibleProductColumns = computed(() => availableProductColumns.filter(col => col.visible));

// Mock order data
const orderData = ref([
  {
    id: 'sale1',
    orderNo: 'SO20240501001',
    customerName: '广州家电有限公司',
    status: 'pending_confirmation',
    productCount: 3,
    totalAmount: 187500.00,
    totalCost: 150000.00,
    profitMargin: 20.0,
    paymentProgress: 30,
    createTime: '2024-05-01 10:30:00',
    products: [
      {
        id: 'sp1-1',
        productName: '智能控制器',
        model: 'CT-X100',
        brand: 'SelecTech',
        category: '控制设备',
        quantity: 50,
        supplier: '东莞电子科技有限公司',
        supplierQuote: 2000.00,
        shippingCost: 200.00,
        shippedQuantity: 5,
        receivedQuantity: 10,
        cancelledQuantity: 0,
        unitPrice: 2500.00,
        totalPrice: 125000.00,
        logisticsStatus: '部分发货',
        financialStatus: '部分付款',
      },
      {
        id: 'sp1-2',
        productName: '传感器模块',
        model: 'SM-500',
        brand: 'SelecTech',
        category: '传感设备',
        quantity: 25,
        supplier: '深圳传感技术有限公司',
        supplierQuote: 2200.00,
        shippingCost: 150.00,
        shippedQuantity: 2,
        receivedQuantity: 5,
        cancelledQuantity: 0,
        unitPrice: 2500.00,
        totalPrice: 62500.00,
        logisticsStatus: '部分发货',
        financialStatus: '部分付款',
      },
    ],
  },
  {
    id: 'sale2',
    orderNo: 'SO20240428002',
    customerName: '北京智能家居科技有限公司',
    status: 'pending_supplier_confirmation',
    productCount: 1,
    totalAmount: 80000.00,
    totalCost: 65000.00,
    profitMargin: 18.8,
    paymentProgress: 100,
    createTime: '2024-04-28 14:20:00',
    products: [
      {
        id: 'sp2-1',
        productName: '中央处理单元',
        model: 'CPU-S2000',
        brand: 'SelecTech',
        category: '处理设备',
        quantity: 20,
        supplier: '上海芯片制造有限公司',
        supplierQuote: 3200.00,
        shippingCost: 250.00,
        shippedQuantity: 0,
        receivedQuantity: 0,
        cancelledQuantity: 0,
        unitPrice: 4000.00,
        totalPrice: 80000.00,
        logisticsStatus: '备货中',
        financialStatus: '已付款',
      },
    ],
  },
  {
    id: 'sale3',
    orderNo: 'SO20240420003',
    customerName: '上海机械设备有限公司',
    status: 'in_progress',
    productCount: 2,
    totalAmount: 135000.00,
    totalCost: 108000.00,
    profitMargin: 20.0,
    paymentProgress: 50,
    createTime: '2024-04-20 09:15:00',
    products: [
      {
        id: 'sp3-1',
        productName: '工业控制器',
        model: 'IC-800',
        brand: 'SelecTech',
        category: '控制设备',
        quantity: 15,
        supplier: '东莞电子科技有限公司',
        supplierQuote: 4800.00,
        shippingCost: 300.00,
        shippedQuantity: 0,
        receivedQuantity: 0,
        cancelledQuantity: 0,
        unitPrice: 6000.00,
        totalPrice: 90000.00,
        logisticsStatus: '运输中',
        financialStatus: '已付款',
      },
      {
        id: 'sp3-2',
        productName: '工业传感器',
        model: 'IS-300',
        brand: 'SelecTech',
        category: '传感设备',
        quantity: 15,
        supplier: '深圳传感技术有限公司',
        supplierQuote: 2400.00,
        shippingCost: 180.00,
        shippedQuantity: 0,
        receivedQuantity: 0,
        cancelledQuantity: 0,
        unitPrice: 3000.00,
        totalPrice: 45000.00,
        logisticsStatus: '运输中',
        financialStatus: '已付款',
      },
    ],
  },
  {
    id: 'sale4',
    orderNo: 'SO20240410004',
    customerName: '深圳智能制造有限公司',
    status: 'completed',
    productCount: 1,
    totalAmount: 55000.00,
    totalCost: 44000.00,
    profitMargin: 20.0,
    paymentProgress: 100,
    createTime: '2024-04-10 16:45:00',
    products: [
      {
        id: 'sp4-1',
        productName: '自动化控制系统',
        model: 'ACS-500',
        brand: 'SelecTech',
        category: '控制系统',
        quantity: 5,
        supplier: '东莞电子科技有限公司',
        supplierQuote: 8500.00,
        shippingCost: 300.00,
        shippedQuantity: 5,
        receivedQuantity: 5,
        cancelledQuantity: 0,
        unitPrice: 11000.00,
        totalPrice: 55000.00,
        logisticsStatus: '已签收',
        financialStatus: '已付款',
      }
    ],
  },
  {
    id: 'sale5',
    orderNo: 'SO20240405005',
    customerName: '广州机电工程有限公司',
    status: 'cancelled',
    productCount: 1,
    totalAmount: 42000.00,
    totalCost: 33600.00,
    profitMargin: 20.0,
    paymentProgress: 0,
    createTime: '2024-04-05 11:30:00',
    products: [
      {
        id: 'sp5-1',
        productName: '工业传感器阵列',
        model: 'ISA-200',
        brand: 'SelecTech',
        category: '传感设备',
        quantity: 6,
        supplier: '深圳传感技术有限公司',
        supplierQuote: 5400.00,
        shippingCost: 200.00,
        shippedQuantity: 0,
        receivedQuantity: 0,
        cancelledQuantity: 6,
        unitPrice: 7000.00,
        totalPrice: 42000.00,
        logisticsStatus: '已取消',
        financialStatus: '已退款',
      }
    ],
  },
  {
    id: 'sale6',
    orderNo: 'SO20240402006',
    customerName: '天津电子科技有限公司',
    status: 'cancelled',
    productCount: 2,
    totalAmount: 120000.00,
    totalCost: 96000.00,
    profitMargin: 20.0,
    paymentProgress: 30,
    createTime: '2024-04-02 13:20:00',
    products: [
      {
        id: 'sp6-1',
        productName: '数据处理单元',
        model: 'DPU-A100',
        brand: 'SelecTech',
        category: '处理设备',
        quantity: 10,
        supplier: '上海芯片制造有限公司',
        supplierQuote: 6200.00,
        shippingCost: 200.00,
        shippedQuantity: 0,
        receivedQuantity: 0,
        cancelledQuantity: 0,
        unitPrice: 8000.00,
        totalPrice: 80000.00,
        logisticsStatus: '待发货',
        financialStatus: '退款中',
      },
      {
        id: 'sp6-2',
        productName: '信号放大器',
        model: 'SA-50',
        brand: 'SelecTech',
        category: '信号设备',
        quantity: 20,
        supplier: '深圳传感技术有限公司',
        supplierQuote: 1600.00,
        shippingCost: 100.00,
        shippedQuantity: 0,
        receivedQuantity: 0,
        cancelledQuantity: 0,
        unitPrice: 2000.00,
        totalPrice: 40000.00,
        logisticsStatus: '待发货',
        financialStatus: '退款中',
      }
    ],
  },
  {
    id: 'sale7',
    orderNo: 'SO20240328007',
    customerName: '重庆智能设备有限公司',
    status: 'pending_confirmation',
    productCount: 1,
    totalAmount: 50000.00,
    totalCost: 40000.00,
    profitMargin: 20.0,
    paymentProgress: 80,
    createTime: '2024-03-28 15:10:00',
    products: [
      {
        id: 'sp7-1',
        productName: '高性能控制器',
        model: 'HPC-800',
        brand: 'SelecTech',
        category: '控制设备',
        quantity: 5,
        supplier: '东莞电子科技有限公司',
        supplierQuote: 7800.00,
        shippingCost: 200.00,
        shippedQuantity: 0,
        receivedQuantity: 0,
        cancelledQuantity: 0,
        unitPrice: 10000.00,
        totalPrice: 50000.00,
        logisticsStatus: '待发货',
        financialStatus: '待处理',
      }
    ],
  },
  {
    id: 'sale8',
    orderNo: 'SO20240320008',
    customerName: '北京自动化设备有限公司',
    status: 'completed',
    productCount: 3,
    totalAmount: 180000.00,
    totalCost: 144000.00,
    profitMargin: 20.0,
    paymentProgress: 100,
    createTime: '2024-03-20 08:45:00',
    products: [
      {
        id: 'sp8-1',
        productName: '自动化生产线控制系统',
        model: 'APLCS-2000',
        brand: 'SelecTech',
        category: '控制系统',
        quantity: 1,
        supplier: '东莞电子科技有限公司',
        supplierQuote: 80000.00,
        shippingCost: 500.00,
        shippedQuantity: 1,
        receivedQuantity: 1,
        cancelledQuantity: 0,
        unitPrice: 100000.00,
        totalPrice: 100000.00,
        logisticsStatus: '已归档',
        financialStatus: '已归档',
      },
      {
        id: 'sp8-2',
        productName: '环境传感器组',
        model: 'ESG-100',
        brand: 'SelecTech',
        category: '传感设备',
        quantity: 10,
        supplier: '深圳传感技术有限公司',
        supplierQuote: 2400.00,
        shippingCost: 150.00,
        shippedQuantity: 10,
        receivedQuantity: 10,
        cancelledQuantity: 0,
        unitPrice: 3000.00,
        totalPrice: 30000.00,
        logisticsStatus: '已归档',
        financialStatus: '已归档',
      },
      {
        id: 'sp8-3',
        productName: '数据采集终端',
        model: 'DAT-5000',
        brand: 'SelecTech',
        category: '采集设备',
        quantity: 5,
        supplier: '上海芯片制造有限公司',
        supplierQuote: 8000.00,
        shippingCost: 250.00,
        shippedQuantity: 5,
        receivedQuantity: 5,
        cancelledQuantity: 0,
        unitPrice: 10000.00,
        totalPrice: 50000.00,
        logisticsStatus: '已归档',
        financialStatus: '已归档',
      }
    ],
  },
]);

// Event handlers
const handleExpandChange = (row, expandedRows) => {
  console.log('Expanded order:', row, 'All expanded rows:', expandedRows);
  // Update allRowsExpanded state based on the number of expanded rows
  if (orderTableRef.value) {
    const currentlyExpandedRows = orderData.value.filter(orderRow => 
      orderTableRef.value.store.states.expandedRows.value.includes(orderRow)
    );
    allRowsExpanded.value = currentlyExpandedRows.length === orderData.value.length;
  }
};

const toggleAllRows = () => {
  if (!orderTableRef.value) return;
  allRowsExpanded.value = !allRowsExpanded.value;
  orderData.value.forEach(row => {
    orderTableRef.value.toggleRowExpansion(row, allRowsExpanded.value);
  });
};

const handleOrderSelectionChange = (selection) => {
  selectedOrders.value = selection;
  console.log('Selected orders:', selection);
};

const handleBatchCommand = (command) => {
  if (command === 'confirm') {
    console.log('Batch confirm clicked');
  } else if (command === 'ship') {
    console.log('Batch ship clicked');
  }
};

const handleViewProduct = (product) => {
  console.log('View product details:', product);
};

const canShipProduct = (product) => {
  return product.preparingQuantity > 0;
};

const handleShipProduct = (product, order) => {
  console.log('Shipping product:', product, 'from order:', order);
};

const handleViewOrderDetails = (order) => {
  console.log('View order details:', order);
  router.push({ name: 'SODetail', params: { id: order.id } });
};

const canProcessOrder = (order) => {
  return ['pending_supplier_confirmation', 'in_progress'].includes(order.status);
};

const handleProcessOrder = (order) => {
  console.log('Process order:', order);
};

const handleConfirmOrder = (order) => {
  console.log('Confirm order:', order);
};

const canDownloadContract = (order) => {
  return ['pending_supplier_confirmation', 'in_progress', 'completed'].includes(order.status);
};

const handleDownloadContract = (order) => {
  console.log('Download contract for order:', order);
  // Here you would implement the actual contract download logic
};

const handleSizeChange = (val) => {
  pageSize.value = val;
  console.log('Page size changed to:', val);
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  console.log('Current page changed to:', val);
};

// Order status helper functions
const getOrderStatusLabel = (status) => {
  const statusMap = {
    'pending_confirmation': '待确认',
    'pending_supplier_confirmation': '待供应商确认',
    'in_progress': '执行中', 
    'completed': '已完成',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
};

const getOrderStatusType = (status) => {
  const typeMap = {
    'pending_confirmation': 'info',
    'pending_supplier_confirmation': 'warning',
    'in_progress': 'primary',
    'completed': 'success', 
    'cancelled': 'danger'
  };
  return typeMap[status] || '';
};
</script>

<style scoped>
.pagination-container {
  display: flex;
  justify-content: flex-end;
}
</style>