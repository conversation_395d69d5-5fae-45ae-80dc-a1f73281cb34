<template>
  <div class="so-detail-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <el-button @click="goBack" type="text" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回订单列表
        </el-button>
        <h2 class="page-title">订单详情 - {{ orderInfo.orderNo }}</h2>
      </div>
      <div class="header-actions">
        <el-button v-if="orderInfo.status === 'pending_confirmation'" type="primary" :disabled="!canConfirmOrder" @click="handleConfirmOrder"> 确认订单 </el-button>
        <el-button v-if="canDownloadContract(orderInfo)" type="success" @click="handleDownloadContract">下载合同</el-button>
        <el-button type="info" @click="handlePrint">打印</el-button>
        <el-button type="primary" @click="handleExport">导出</el-button>
      </div>
    </div>

    <!-- 订单流程 -->
    <div class="content-section">
      <div class="section-header">
        <h3>订单流程</h3>
      </div>
      <div class="process-container">
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step title="创建草稿" :description="processSteps.draft.time">
            <template #icon>
              <el-icon><Edit /></el-icon>
            </template>
          </el-step>
          <el-step title="提交订单" :description="processSteps.submitted.time">
            <template #icon>
              <el-icon><Upload /></el-icon>
            </template>
          </el-step>
          <el-step title="订单确认" :description="processSteps.confirmed.time">
            <template #icon>
              <el-icon><Check /></el-icon>
            </template>
          </el-step>
          <el-step title="完成订单" :description="processSteps.completed.time">
            <template #icon>
              <el-icon><CircleCheck /></el-icon>
            </template>
          </el-step>
        </el-steps>
      </div>
    </div>

    <!-- 基本信息 -->
    <div class="content-section">
      <div class="section-header">
        <h3>基本信息</h3>
      </div>
      <div class="basic-info">
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">订单号：</span>
              <span class="value">{{ orderInfo.orderNo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">订单状态：</span>
              <el-tag :type="getStatusType(orderInfo.status)" class="value">
                {{ getStatusLabel(orderInfo.status) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">下单时间：</span>
              <span class="value">{{ orderInfo.createTime }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">采购员：</span>
              <span class="value">{{ orderInfo.buyer }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">联系电话：</span>
              <span class="value">{{ orderInfo.contactPhone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">付款方式：</span>
              <span class="value">{{ orderInfo.paymentMethod }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">付款条件：</span>
              <span class="value">{{ orderInfo.paymentTerms }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">运费：</span>
              <span class="value">¥{{ formatPrice(orderInfo.customerFreight) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">总金额：</span>
              <span class="value amount">¥{{ formatPrice(orderInfo.totalAmount) }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 物料明细 -->
    <div class="content-section">
      <div class="section-header">
        <div class="section-title-wrapper">
          <h3>物料明细</h3>
        </div>
      </div>
      <div class="material-detail">
        <div class="material-search">
          <el-input v-model="supplierSearchKeyword" placeholder="按供应商名称搜索..." prefix-icon="Search" clearable style="width: 300px" @input="handleSupplierSearch" />
        </div>

        <div class="material-actions">
          <!-- 批量调整按钮 -->
          <div class="batch-actions" v-if="orderInfo.status === 'pending_confirmation'">
            <el-button type="danger" @click="handleBatchAdjustSalePrice" :disabled="selectedMaterials.length === 0"> 调整销售价 ({{ selectedMaterials.length }}) </el-button>
          </div>
          <div class="statistics-actions" v-if="orderInfo.status === 'pending_confirmation'">
            <el-button type="danger" @click="handleResetAllMaterials">全部重置</el-button>
          </div>
        </div>

        <!-- 选中物料统计信息 - 常驻显示 -->
        <div class="selected-statistics">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="选中物料" :value="selectedMaterials.length" suffix="项" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="选中销售总价" :value="selectedMaterialsSalesTotal" :precision="2" prefix="¥" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="选中供应商总价" :value="selectedMaterialsSupplierTotal" :precision="2" prefix="¥" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="选中平均利润率" :value="selectedMaterialsAvgProfitRate" :precision="2" suffix="%" />
            </el-col>
          </el-row>
        </div>

        <el-table :data="filteredMaterialList" border style="width: 100%" @selection-change="handleMaterialSelectionChange">
          <el-table-column v-if="orderInfo.status === 'pending_confirmation'" type="selection" width="55" />
          <el-table-column prop="materialName" label="物料名称" min-width="180" />
          <el-table-column prop="model" label="型号" min-width="120" />
          <el-table-column prop="brand" label="品牌" min-width="100" />
          <el-table-column prop="category" label="分类" min-width="100" />
          <el-table-column prop="supplierName" label="供应商名称" min-width="200">
            <template #default="scope">
              <el-select v-if="orderInfo.status === 'pending_confirmation'" v-model="scope.row.supplierId" filterable placeholder="请选择供应商" @change="(val) => onSupplierChange(val, scope.row)" style="width: 100%">
                <el-option v-for="supplier in supplierOptions" :key="supplier.id" :label="supplier.name" :value="supplier.id" />
              </el-select>
              <span v-else>{{ scope.row.supplierName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="需求数量" width="100" align="center" />
          <el-table-column prop="shippedQuantity" label="已发货数量" width="100" align="center" />
          <el-table-column prop="receivedQuantity" label="已收货数量" width="100" align="center" />
          <el-table-column prop="logisticsStatus" label="物流状态" min-width="120">
            <template #default="scope">
              <el-tag :type="getLogisticsStatusType(scope.row.logisticsStatus)">
                {{ scope.row.logisticsStatus }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="financialStatus" label="财务状态" min-width="120">
            <template #default="scope">
              <el-tag :type="getFinancialStatusType(scope.row.financialStatus)">
                {{ scope.row.financialStatus }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="supplierPrice" label="供应商报价" width="150" align="right">
            <template #default="scope">
              <el-input-number v-if="orderInfo.status === 'pending_confirmation'" v-model="scope.row.supplierPrice" :min="0" :precision="2" :controls="false" style="width: 100%" @change="updateMaterialCost" />
              <span v-else>
                <span v-if="scope.row.supplierPrice">¥{{ formatPrice(scope.row.supplierPrice) }}</span>
                <span v-else class="text-muted">-</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="freight" label="运费" width="120" align="right">
            <template #default="scope">
              <el-input-number v-if="orderInfo.status === 'pending_confirmation'" v-model="scope.row.freight" :min="0" :precision="2" :controls="false" style="width: 100%" @change="updateMaterialCost" />
              <span v-else>
                <span v-if="scope.row.freight">¥{{ formatPrice(scope.row.freight) }}</span>
                <span v-else>¥0.00</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="unitPrice" label="销售价" width="120" align="right">
            <template #default="scope">
              <el-input-number v-if="orderInfo.status === 'pending_confirmation'" v-model="scope.row.unitPrice" :min="0" :precision="2" :controls="false" style="width: 100%" @change="updateMaterialCost" />
              <span v-else>¥{{ formatPrice(scope.row.unitPrice) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="totalPrice" label="小计" width="120" align="right">
            <template #default="scope">
              <span v-if="scope.row.totalPrice">¥{{ formatPrice(scope.row.totalPrice) }}</span>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="expectedDeliveryDate" label="预计到货日期" min-width="120" />
        </el-table>

        <!-- 统计信息 -->
        <div class="material-summary">
          <div class="summary-content">
            <div class="summary-columns">
              <div class="summary-column">
                <!-- <div class="summary-item">
                  <span class="summary-label">物料总数：</span>
                  <span class="summary-value">{{ materialSummary.totalQuantity }} 件</span>
                </div> -->
                <div class="summary-item">
                  <span class="summary-label">销售总价：</span>
                  <span class="summary-value">¥{{ formatPrice(materialSummary.salesTotal) }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">客户运费：</span>
                  <span class="summary-value">¥{{ formatPrice(materialSummary.customerFreight) }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">订单总收入：</span>
                  <span class="summary-value">¥{{ formatPrice(materialSummary.totalRevenue) }}</span>
                </div>
              </div>
              <div class="summary-column">
                <div class="summary-item">
                  <span class="summary-label">供应商物料总价：</span>
                  <span class="summary-value">¥{{ formatPrice(materialSummary.supplierTotalPrice) }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">供应商总运费：</span>
                  <span class="summary-value">¥{{ formatPrice(materialSummary.supplierFreightTotal) }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">订单总支出：</span>
                  <span class="summary-value">¥{{ formatPrice(materialSummary.totalCost) }}</span>
                </div>
              </div>
            </div>
            <div class="summary-columns">
              <div class="summary-column">
                
              </div>
              <div class="summary-column">
                
                <div class="summary-item total">
                  <span class="summary-label">利润率：</span>
                  <span class="summary-value" :class="getProfitRateClass(materialSummary.grossProfitRate)"> {{ materialSummary.grossProfitRate }}% </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 收货与开票信息 -->
    <div class="content-section">
      <div class="section-header">
        <h3>收货与开票信息</h3>
      </div>
      <div class="delivery-invoice-info">
        <el-tabs v-model="activeTab" class="info-tabs">
          <el-tab-pane label="收货信息" name="delivery">
            <div class="delivery-info">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="联系人">
                  {{ deliveryInfo.contactName }}
                </el-descriptions-item>
                <el-descriptions-item label="手机号">
                  {{ deliveryInfo.phone }}
                </el-descriptions-item>
                <el-descriptions-item label="地区" :span="2">
                  {{ deliveryInfo.region }}
                </el-descriptions-item>
                <el-descriptions-item label="详细地址" :span="2">
                  {{ deliveryInfo.address }}
                </el-descriptions-item>
                <el-descriptions-item label="备注" :span="2">
                  {{ deliveryInfo.remark || '无' }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-tab-pane>
          <el-tab-pane label="开票信息" name="invoice">
            <div class="invoice-info">
              <el-empty description="开票信息功能开发中..." />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 批量调整销售价弹窗 -->
    <el-dialog v-model="batchAdjustVisible" title="批量调整销售价" width="800px">
      <!-- 价格信息对比区域 -->
      <div class="price-comparison-container">
        <h4 style="margin-bottom: 16px; color: #333; text-align: center">批量调整对比 ({{ selectedMaterials.length }} 项物料)</h4>
        <el-row :gutter="24">
          <!-- 当前价格信息 -->
          <el-col :span="12">
            <div class="current-price-section">
              <h5 class="section-title">当前汇总</h5>
              <div class="price-item">
                <span class="price-label">供应商报价总计:</span>
                <span class="price-value">¥{{ formatPrice(selectedMaterialsSupplierTotal) }}</span>
              </div>
              <div class="price-item">
                <span class="price-label">销售价总计:</span>
                <span class="price-value">¥{{ formatPrice(selectedMaterialsSalesTotal) }}</span>
              </div>
              <div class="price-item">
                <span class="price-label">平均利润率:</span>
                <span class="price-value" :class="getProfitRateClass(selectedMaterialsAvgProfitRate)"> {{ selectedMaterialsAvgProfitRate.toFixed(2) }}% </span>
              </div>
              <div class="price-item">
                <span class="price-label">总利润:</span>
                <span class="price-value">¥{{ formatPrice(selectedMaterialsSalesTotal - selectedMaterialsSupplierTotal) }}</span>
              </div>
            </div>
          </el-col>
          <!-- 调整后预览 -->
          <el-col :span="12">
            <div class="preview-price-section" :class="{ 'preview-active': batchAdjustForm.value !== 0 }">
              <h5 class="section-title">调整后预览</h5>
              <template v-if="batchAdjustForm.value !== 0">
                <div class="price-item">
                  <span class="price-label">供应商报价总计:</span>
                  <span class="price-value">¥{{ formatPrice(selectedMaterialsSupplierTotal) }}</span>
                </div>
                <div class="price-item">
                  <span class="price-label">销售价总计:</span>
                  <span class="price-value preview-value">¥{{ formatPrice(getBatchPreviewSalesTotal()) }}</span>
                </div>
                <div class="price-item">
                  <span class="price-label">平均利润率:</span>
                  <span class="price-value preview-value" :class="getProfitRateClass(getBatchPreviewAvgProfitRate())"> {{ getBatchPreviewAvgProfitRate().toFixed(2) }}% </span>
                </div>
                <div class="price-item">
                  <span class="price-label">利润变化:</span>
                  <span class="price-value preview-value" :style="{ color: getBatchPreviewProfitChange() >= 0 ? '#67c23a' : '#f56c6c' }"> {{ getBatchPreviewProfitChange() >= 0 ? '+' : '' }}¥{{ formatPrice(getBatchPreviewProfitChange()) }} </span>
                </div>
              </template>
              <template v-else>
                <div class="no-preview">请设置调整值查看预览</div>
              </template>
            </div>
          </el-col>
        </el-row>
      </div>

      <el-divider />

      <el-form :model="batchAdjustForm" label-width="120px">
        <el-form-item label="调整方式">
          <el-radio-group v-model="batchAdjustForm.type">
            <el-radio label="salePrice">销售总价</el-radio>
            <el-radio label="profitRate">按利润率</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="batchAdjustForm.type === 'salePrice' ? '销售总价' : '利润率'">
          <el-input-number v-model="batchAdjustForm.value" :min="0" :precision="2" />
          <span style="margin-left: 10px; color: #666">
            {{ batchAdjustForm.type === 'profitRate' ? '%' : '元' }}
          </span>
        </el-form-item>
      </el-form>
      <div v-if="batchAdjustForm.type === 'salePrice'" style="color: #f56c6c; margin-left: 50px; margin-top: 10px; width: 400px">⚠️ 按销售总价批量调整时，销售总价的变化将等比例分摊到每个选中的物料上。</div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseBatchAdjustDialog">取消</el-button>
          <el-button type="primary" @click="handleBatchAdjustConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, h } from 'vue';
import { useRouter } from 'vue-router';
import { ArrowLeft, Edit, Upload, Check, CircleCheck, Setting, Search } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const router = useRouter();

// 当前步骤 - 根据订单状态动态计算
const currentStep = computed(() => {
  const statusStepMap = {
    draft: 0,
    pending_confirmation: 0,
    pending_supplier_confirmation: 1,
    in_progress: 2,
    completed: 3,
    cancelled: 0,
  };
  return statusStepMap[orderInfo.status] || 0;
});

// 当前选中的tab
const activeTab = ref('delivery');

// 供应商选项
const supplierOptions = ref([]);

// 供应商搜索关键词
const supplierSearchKeyword = ref('');

// 保存物料的初始状态
const originalMaterialList = ref([]);

// 选中的物料
const selectedMaterials = ref([]);

// 批量调整弹窗显示状态
const batchAdjustVisible = ref(false);

// 批量调整表单
const batchAdjustForm = reactive({
  type: 'salePrice', // salePrice, profitRate
  value: 0,
});

// 模拟供应商数据
const mockSuppliers = [
  { id: 'supplier_001', name: 'SelecTech科技有限公司' },
  { id: 'supplier_002', name: '传感技术(深圳)有限公司' },
  { id: 'supplier_003', name: '显示科技股份有限公司' },
  { id: 'supplier_004', name: '智能设备制造有限公司' },
  { id: 'supplier_005', name: '精密仪器(上海)有限公司' },
];

// 判断是否可以确认订单
const canConfirmOrder = computed(() => {
  // 检查所有物料是否都有供应商和报价
  return materialList.value.every((material) => material.supplierId && material.supplierPrice >= 0);
});

// 过滤后的物料列表
const filteredMaterialList = computed(() => {
  if (!supplierSearchKeyword.value.trim()) {
    return materialList.value;
  }

  const keyword = supplierSearchKeyword.value.toLowerCase().trim();
  return materialList.value.filter((material) => material.supplierName.toLowerCase().includes(keyword));
});

// 流程步骤信息
const processSteps = reactive({
  draft: {
    time: '2024-05-01 09:30:00',
  },
  submitted: {
    time: '2024-05-01 10:30:00',
  },
  confirmed: {
    time: '2024-05-01 14:20:00',
  },
  completed: {
    time: '',
  },
});

// 订单基本信息
const orderInfo = reactive({
  orderNo: 'SO20240501001',
  status: 'pending_confirmation', // 使用与OrderView一致的状态值
  createTime: '2024-05-01 10:30:00',
  buyer: '张三',
  contactPhone: '13800138000',
  paymentMethod: '银行转账',
  paymentTerms: '款到发货',
  customerFreight: 500.0,
  totalAmount: 187500.0,
});

// 物料明细数据
const materialList = ref([
  {
    id: 1,
    materialName: '智能控制器',
    model: 'CT-X100',
    brand: 'SelecTech',
    category: '控制设备',
    supplierId: 'supplier_001',
    supplierName: 'SelecTech科技有限公司',
    quantity: 50,
    shippedQuantity: 30,
    receivedQuantity: 25,
    cancelledQuantity: 0,
    logisticsStatus: '部分发货',
    financialStatus: '已付款',
    supplierPrice: 2000.0,
    freight: 0,
    unitPrice: 2500.0,
    totalPrice: 125000.0,
    expectedDeliveryDate: '2024-05-15',
  },
  {
    id: 2,
    materialName: '传感器模块',
    model: 'SM-200',
    brand: 'TechSense',
    category: '传感设备',
    supplierId: 'supplier_002',
    supplierName: '传感技术(深圳)有限公司',
    quantity: 100,
    shippedQuantity: 0,
    receivedQuantity: 0,
    cancelledQuantity: 0,
    logisticsStatus: '待发货',
    financialStatus: '待付款',
    supplierPrice: 400.0,
    freight: 0,
    unitPrice: 500.0,
    totalPrice: 50000.0,
    expectedDeliveryDate: '2024-05-20',
  },
  {
    id: 3,
    materialName: '显示屏',
    model: 'DS-15',
    brand: 'DisplayTech',
    category: '显示设备',
    supplierId: 'supplier_003',
    supplierName: '显示科技股份有限公司',
    quantity: 25,
    shippedQuantity: 25,
    receivedQuantity: 25,
    cancelledQuantity: 0,
    logisticsStatus: '已签收',
    financialStatus: '已付款',
    supplierPrice: 400.0,
    freight: 0,
    unitPrice: 500.0,
    totalPrice: 12500.0,
    expectedDeliveryDate: '2024-05-10',
  },
]);

// 收货信息
const deliveryInfo = reactive({
  contactName: '李经理',
  phone: '13900139000',
  region: '广东省 广州市 天河区',
  address: '天河路123号科技大厦15楼',
  remark: '请在工作日送货，联系保安室',
});

// 物料统计信息
const materialSummary = computed(() => {
  const totalQuantity = materialList.value.reduce((sum, item) => sum + item.quantity, 0);
  const salesTotal = materialList.value.reduce((sum, item) => sum + item.quantity * (item.unitPrice || 0), 0);
  const customerFreight = 500.0; // 客户运费

  // 计算供应商物料总价
  const supplierTotalPrice = materialList.value.reduce((sum, item) => {
    return sum + item.quantity * (item.supplierPrice || 0);
  }, 0);

  // 计算供应商运费总计
  const supplierFreightTotal = materialList.value.reduce((sum, item) => {
    return sum + (item.freight || 0);
  }, 0);

  // 计算利润率
  const totalRevenue = salesTotal + customerFreight;
  const totalCost = supplierTotalPrice + supplierFreightTotal;
  const grossProfitRate = totalCost > 0 ? (((totalRevenue - totalCost) / totalCost) * 100).toFixed(1) : 0;

  return {
    totalQuantity,
    salesTotal,
    customerFreight,
    supplierTotalPrice,
    supplierFreightTotal,
    totalRevenue,
    totalCost,
    grossProfitRate,
  };
});

// 选中物料的统计信息
const selectedMaterialsSalesTotal = computed(() => {
  return selectedMaterials.value.reduce((sum, item) => sum + item.quantity * (item.unitPrice || 0), 0);
});

const selectedMaterialsSupplierTotal = computed(() => {
  return selectedMaterials.value.reduce((sum, item) => sum + item.quantity * (item.supplierPrice || 0), 0);
});

const selectedMaterialsAvgProfitRate = computed(() => {
  if (selectedMaterials.value.length === 0) return 0;

  const totalProfitRate = selectedMaterials.value.reduce((sum, item) => {
    const profitRate = item.supplierPrice > 0 ? ((item.unitPrice - item.supplierPrice) / item.supplierPrice) * 100 : 0;
    return sum + profitRate;
  }, 0);

  return totalProfitRate / selectedMaterials.value.length;
});

// 工具函数
const formatPrice = (price) => {
  if (price === null || price === undefined) return '0.00';
  return price.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

const getStatusType = (status) => {
  const statusMap = {
    pending_confirmation: 'info',
    pending_supplier_confirmation: 'warning',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'danger',
  };
  return statusMap[status] || 'info';
};

const getStatusLabel = (status) => {
  const statusMap = {
    pending_confirmation: '待确认',
    pending_supplier_confirmation: '待供应商确认',
    in_progress: '执行中',
    completed: '已完成',
    cancelled: '已取消',
  };
  return statusMap[status] || '未知';
};

const getLogisticsStatusType = (status) => {
  const statusMap = {
    待发货: 'info',
    部分发货: 'warning',
    已发货: 'primary',
    运输中: 'primary',
    已签收: 'success',
  };
  return statusMap[status] || 'info';
};

const getFinancialStatusType = (status) => {
  const statusMap = {
    待付款: 'danger',
    部分付款: 'warning',
    已付款: 'success',
    已退款: 'info',
  };
  return statusMap[status] || 'info';
};

const getProfitRateClass = (rate) => {
  const numRate = parseFloat(rate);
  if (numRate >= 20) return 'profit-high';
  if (numRate >= 10) return 'profit-medium';
  return 'profit-low';
};

const getProfitRateType = (rate) => {
  const numRate = parseFloat(rate);
  if (numRate >= 20) return 'success';
  if (numRate >= 10) return 'warning';
  return 'danger';
};

// 操作按钮条件判断
const canDownloadContract = (order) => {
  return ['pending_supplier_confirmation', 'in_progress', 'completed'].includes(order.status);
};

// 事件处理
const goBack = () => {
  router.go(-1);
};

const handleConfirmOrder = () => {
  if (!canConfirmOrder.value) {
    ElMessage.warning('请为所有物料选择供应商并设置报价');
    return;
  }

  // 检查是否有销售价被修改的物料
  const modifiedMaterials = getModifiedMaterials();

  if (modifiedMaterials.length > 0) {
    // 显示销售价修改确认弹窗
    showPriceModificationDialog(modifiedMaterials);
  } else {
    // 直接确认订单
    confirmOrderDirectly();
  }
};

// 获取销售价被修改的物料
const getModifiedMaterials = () => {
  const modified = [];
  materialList.value.forEach((material, index) => {
    const original = originalMaterialList.value[index];
    if (original && material.unitPrice !== original.unitPrice) {
      modified.push({
        materialName: material.materialName,
        model: material.model,
        brand: material.brand,
        originalPrice: original.unitPrice,
        modifiedPrice: material.unitPrice,
      });
    }
  });
  return modified;
};

// 显示销售价修改确认弹窗
const showPriceModificationDialog = (modifiedMaterials) => {
  // 创建表格HTML
  const createTableHTML = (materials) => {
    const tableRows = materials
      .map(
        (material) => `
      <tr>
        <td style="padding: 8px; border: 1px solid #dcdfe6;">${material.materialName}</td>
        <td style="padding: 8px; border: 1px solid #dcdfe6;">${material.model}</td>
        <td style="padding: 8px; border: 1px solid #dcdfe6;">${material.brand}</td>
        <td style="padding: 8px; border: 1px solid #dcdfe6; text-align: right;">¥${formatPrice(material.originalPrice)}</td>
        <td style="padding: 8px; border: 1px solid #dcdfe6; text-align: right;">¥${formatPrice(material.modifiedPrice)}</td>
      </tr>
    `
      )
      .join('');

    return `
      <div style="margin-bottom: 16px;">以下物料的销售价已被修改，本订单将交由客户进行最终确认，是否继续？</div>
      <table style="width: 100%; border-collapse: collapse; border: 1px solid #dcdfe6; font-size: 14px;">
        <thead>
          <tr style="background-color: #f5f7fa;">
            <th style="padding: 12px 8px; border: 1px solid #dcdfe6; text-align: left; font-weight: 600;">物料名称</th>
            <th style="padding: 12px 8px; border: 1px solid #dcdfe6; text-align: left; font-weight: 600;">型号</th>
            <th style="padding: 12px 8px; border: 1px solid #dcdfe6; text-align: left; font-weight: 600;">品牌</th>
            <th style="padding: 12px 8px; border: 1px solid #dcdfe6; text-align: right; font-weight: 600;">原销售价</th>
            <th style="padding: 12px 8px; border: 1px solid #dcdfe6; text-align: right; font-weight: 600;">修改后销售价</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
      </table>
    `;
  };

  ElMessageBox({
    title: '销售价修改确认',
    dangerouslyUseHTMLString: true,
    message: createTableHTML(modifiedMaterials),
    showCancelButton: true,
    confirmButtonText: '继续确认',
    cancelButtonText: '取消',
    type: 'warning',
    customStyle: {
      width: '800px',
    },
  })
    .then(() => {
      confirmOrderDirectly();
    })
    .catch(() => {
      // 用户取消
    });
};

// 直接确认订单
const confirmOrderDirectly = () => {
  ElMessage.success('确认订单成功！');
  // 这里可以添加实际的确认订单逻辑
  // 更新订单状态
  orderInfo.status = 'pending_supplier_confirmation';
  // 更新流程步骤时间
  processSteps.submitted.time = new Date().toLocaleString('zh-CN');
};

const handleDownloadContract = () => {
  ElMessage.success('合同下载中...');
  // 这里可以添加实际的下载合同逻辑
};

const handlePrint = () => {
  ElMessage.success('打印功能开发中...');
};

const handleExport = () => {
  ElMessage.success('导出功能开发中...');
};

const handlePreviewPurchaseOrder = () => {
  ElMessage.success('采购订单预览功能开发中...');
  // 这里可以添加实际的采购订单预览逻辑
};

// 供应商选择变化事件
const onSupplierChange = (supplierId, material) => {
  const supplier = mockSuppliers.find((s) => s.id === supplierId);
  if (supplier) {
    material.supplierName = supplier.name;
    ElMessage.success(`已选择供应商：${supplier.name}`);
  }
};

// 更新物料成本
const updateMaterialCost = () => {
  // 重新计算每个物料的小计
  materialList.value.forEach((material) => {
    material.totalPrice = material.quantity * (material.unitPrice || 0);
  });
};

// 处理供应商搜索
const handleSupplierSearch = () => {
  // 搜索功能由计算属性 filteredMaterialList 自动处理
  // 这里可以添加其他搜索相关的逻辑，比如搜索统计等
};

// 处理全部重置
const handleResetAllMaterials = () => {
  ElMessageBox.confirm('确定要重置所有物料的修改吗？此操作将恢复所有物料的供应商、报价、运费和销售价到初始状态。', '确认重置', {
    confirmButtonText: '确定重置',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      // 重置物料列表到初始状态
      materialList.value = JSON.parse(JSON.stringify(originalMaterialList.value));
      ElMessage.success('已重置所有物料到初始状态');
    })
    .catch(() => {
      // 用户取消
    });
};

// 物料选择变化
const handleMaterialSelectionChange = (selection) => {
  selectedMaterials.value = selection;
};

// 批量调整销售价
const handleBatchAdjustSalePrice = () => {
  if (selectedMaterials.value.length === 0) {
    ElMessage.warning('请先选择要调整的物料');
    return;
  }

  batchAdjustForm.type = 'salePrice';
  batchAdjustForm.value = 0;
  batchAdjustVisible.value = true;
};

// 关闭批量调整弹窗
const handleCloseBatchAdjustDialog = () => {
  batchAdjustVisible.value = false;
  batchAdjustForm.type = 'salePrice';
  batchAdjustForm.value = 0;
};

// 批量调整预览计算
const getBatchPreviewSalesTotal = () => {
  const { type, value } = batchAdjustForm;
  if (type === 'salePrice') {
    return value;
  } else if (type === 'profitRate') {
    return selectedMaterials.value.reduce((sum, item) => {
      const newUnitPrice = (item.supplierPrice || 0) * (1 + value / 100);
      return sum + newUnitPrice * item.quantity;
    }, 0);
  }
  return selectedMaterialsSalesTotal.value;
};

const getBatchPreviewAvgProfitRate = () => {
  if (selectedMaterials.value.length === 0) return 0;
  const { type, value } = batchAdjustForm;

  if (type === 'salePrice') {
    const newTotalSalesPrice = value;
    const currentTotalSalesPrice = selectedMaterialsSalesTotal.value;

    if (currentTotalSalesPrice === 0) return 0;

    let totalProfitRate = 0;
    selectedMaterials.value.forEach((item) => {
      const currentItemTotal = item.unitPrice * item.quantity;
      const proportion = currentItemTotal / currentTotalSalesPrice;
      const newItemTotal = newTotalSalesPrice * proportion;
      const newUnitPrice = newItemTotal / item.quantity;
      const newProfitRate = item.supplierPrice > 0 ? ((newUnitPrice - item.supplierPrice) / item.supplierPrice) * 100 : 0;
      totalProfitRate += newProfitRate;
    });

    return totalProfitRate / selectedMaterials.value.length;
  } else if (type === 'profitRate') {
    return value;
  }

  return selectedMaterialsAvgProfitRate.value;
};

const getBatchPreviewProfitChange = () => {
  const currentProfit = selectedMaterialsSalesTotal.value - selectedMaterialsSupplierTotal.value;
  const newProfit = getBatchPreviewSalesTotal() - selectedMaterialsSupplierTotal.value;
  return newProfit - currentProfit;
};

// 批量调整确认
const handleBatchAdjustConfirm = () => {
  const { type, value } = batchAdjustForm;

  if (value <= 0) {
    ElMessage.warning('请输入有效的调整值');
    return;
  }

  if (type === 'salePrice') {
    // 按销售总价调整 - 等比例分摊
    const newTotalSalesPrice = value;
    const currentTotalSalesPrice = selectedMaterialsSalesTotal.value;

    if (currentTotalSalesPrice === 0) {
      ElMessage.warning('当前销售总价为0，无法进行等比例调整');
      return;
    }

    selectedMaterials.value.forEach((item) => {
      const currentItemTotal = item.unitPrice * item.quantity;
      const proportion = currentItemTotal / currentTotalSalesPrice;
      const newItemTotal = newTotalSalesPrice * proportion;
      item.unitPrice = newItemTotal / item.quantity;
      item.totalPrice = item.unitPrice * item.quantity;
    });
  } else if (type === 'profitRate') {
    // 按利润率调整
    selectedMaterials.value.forEach((item) => {
      item.unitPrice = (item.supplierPrice || 0) * (1 + value / 100);
      item.totalPrice = item.unitPrice * item.quantity;
    });
  }

  batchAdjustVisible.value = false;
  ElMessage.success(`已调整 ${selectedMaterials.value.length} 项物料的销售价`);
};

// 初始化供应商选项
supplierOptions.value = mockSuppliers;

// 保存物料的初始状态
originalMaterialList.value = JSON.parse(JSON.stringify(materialList.value));
</script>

<style scoped>
.so-detail-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  text-decoration: none;
}

.back-button:hover {
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.content-section {
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.material-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.process-container {
  padding: 30px 20px;
}

.basic-info {
  padding: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.label {
  min-width: 100px;
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
  font-weight: 400;
}

.value.amount {
  color: var(--el-color-primary);
  font-weight: 600;
  font-size: 16px;
}

.material-detail {
  padding: 20px;
}

.material-search {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.material-actions {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.batch-actions {
  display: flex;
  justify-content: flex-start;
  gap: 8px;
}

.selected-statistics {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f0f9ff;
  border: 1px solid #c3d9ff;
  border-radius: 6px;
}

.statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.statistics-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.statistics-actions {
  display: flex;
  gap: 8px;
}

.material-summary {
  margin-top: 16px;
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
  text-align: right;
}

.summary-content {
  display: inline-block;
  width: 500px;
  padding: 0 16px;
}

.summary-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: start;
}

.summary-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.summary-item.total {
  font-weight: 600;
  font-size: 16px;
  color: var(--el-color-primary);
  border-top: 1px solid #e4e7ed;
  margin-top: 8px;
  padding-top: 16px;
}

.summary-label {
  color: #666;
  font-size: 14px;
  white-space: nowrap;
}

.summary-value {
  color: #333;
  font-size: 14px;
  font-weight: 500;
  text-align: right;
}

.summary-item.total .summary-value {
  color: var(--el-color-primary);
  font-weight: 600;
  font-size: 16px;
}

.summary-value.profit-high {
  color: #67c23a;
  font-weight: 600;
}

.summary-value.profit-medium {
  color: #e6a23c;
  font-weight: 600;
}

.summary-value.profit-low {
  color: #f56c6c;
  font-weight: 600;
}

.summary-actions {
  margin-top: 8px;
  text-align: right;
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.delivery-invoice-info {
  padding: 0 20px 20px;
}

.info-tabs {
  margin-top: 10px;
}

.delivery-info {
  padding: 20px 0;
}

.invoice-info {
  padding: 40px 0;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .so-detail-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-content {
    width: 100%;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .basic-info .el-col {
    margin-bottom: 8px;
  }

  .label {
    min-width: 80px;
    font-size: 14px;
  }

  .value {
    font-size: 14px;
  }

  .summary-columns {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .summary-item {
    font-size: 13px;
    padding: 6px 0;
  }

  .summary-item.total {
    font-size: 15px;
  }

  .summary-item.total .summary-value {
    font-size: 15px;
  }

  .batch-actions {
    justify-content: center;
  }

  .selected-statistics :deep(.el-row) {
    flex-direction: column;
  }

  .statistics-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .statistics-actions {
    justify-content: center;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

/* Steps 组件样式优化 */
:deep(.el-steps) {
  margin: 20px 0;
}

:deep(.el-step__title) {
  font-weight: 600;
}

/* Tabs 样式优化 */
:deep(.el-tabs__item) {
  font-weight: 600;
}

:deep(.el-tabs__content) {
  margin-top: 0;
}

.text-muted {
  color: #999;
}

/* 批量调整弹窗样式 */
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 价格对比区域样式 */
.price-comparison-container {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.current-price-section,
.preview-price-section {
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  background-color: #fff;
  min-height: 200px;
}

.current-price-section {
  border-color: #d0d7de;
}

.preview-price-section {
  background-color: #f8fafc;
  border-color: #c3d9ff;
}

.preview-price-section.preview-active {
  background-color: #f0f9ff;
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.section-title {
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  border-bottom: 1px solid #e4e7ed;
  color: #333;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f2f3f5;
}

.price-item:last-child {
  border-bottom: none;
}

.price-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.price-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.preview-value {
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

.no-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120px;
  color: #999;
  font-style: italic;
  background-color: #f9f9f9;
  border: 2px dashed #ddd;
  border-radius: 6px;
}
</style>
