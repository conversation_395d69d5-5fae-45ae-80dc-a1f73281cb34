<template>
  <div class="pay-detail-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <el-button @click="goBack" type="text" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回付款列表
        </el-button>
        <h2 class="page-title">付款详情 - {{ paymentInfo.paymentNo }}</h2>
      </div>
      <div class="header-actions">
        <el-button v-if="paymentInfo.status === 'pending'" type="primary" @click="handleCompletePayment">
          完成付款
        </el-button>
        <el-button type="info" @click="handlePrint">打印</el-button>
        <el-button type="primary" @click="handleExport">导出</el-button>
      </div>
    </div>

    <!-- 基本信息 -->
    <div class="content-section">
      <div class="section-header">
        <h3>基本信息</h3>
      </div>
      <div class="basic-info">
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">客户名称：</span>
              <span class="value">{{ paymentInfo.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">付款单号：</span>
              <span class="value">{{ paymentInfo.paymentNo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">付款方式：</span>
              <span class="value">{{ paymentInfo.paymentMethod }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">付款条件：</span>
              <span class="value">{{ paymentInfo.paymentTerms }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ paymentInfo.createTime }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">所属对账单：</span>
              <span class="value">{{ paymentInfo.statementNo || '无' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">付款完成日期：</span>
              <span class="value">{{ paymentInfo.completedDate || '未完成' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">应付总额：</span>
              <span class="value amount">¥{{ formatPrice(paymentInfo.totalAmount) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">实付总额：</span>
              <span class="value amount">¥{{ formatPrice(paymentInfo.paidAmount) }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">待付总额：</span>
              <span class="value amount pending">¥{{ formatPrice(paymentInfo.pendingAmount) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">付款状态：</span>
              <el-tag :type="getStatusType(paymentInfo.status)" class="value">
                {{ getStatusLabel(paymentInfo.status) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 物料信息 -->
    <div class="content-section">
      <div class="section-header">
        <div class="section-title-wrapper">
          <h3>物料信息</h3>
        </div>
      </div>
      <div class="material-detail">
        <el-table :data="materialList" border style="width: 100%">
          <el-table-column prop="materialName" label="物料名称" min-width="180" />
          <el-table-column prop="model" label="型号" min-width="120" />
          <el-table-column prop="brand" label="品牌" min-width="100" />
          <el-table-column prop="quantity" label="数量" width="100" align="center" />
          <el-table-column prop="unitPrice" label="单价" width="120" align="right">
            <template #default="scope">
              ¥{{ formatPrice(scope.row.unitPrice) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalPrice" label="小计" width="120" align="right">
            <template #default="scope">
              ¥{{ formatPrice(scope.row.totalPrice) }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 统计信息 -->
        <div class="material-summary">
          <div class="summary-content">
            <div class="summary-columns">
              <div class="summary-column">
                <div class="summary-item">
                  <span class="summary-label">运费：</span>
                  <span class="summary-value">¥{{ formatPrice(paymentSummary.freight) }}</span>
                </div>
              </div>
              <div class="summary-column">
                <div class="summary-item total">
                  <span class="summary-label">总金额：</span>
                  <span class="summary-value">¥{{ formatPrice(paymentSummary.totalAmount) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 附件与备注 -->
    <div class="content-section">
      <div class="section-header">
        <h3>附件与备注</h3>
      </div>
      <div class="attachment-notes">
        <el-tabs v-model="activeTab" class="info-tabs">
          <el-tab-pane label="附件" name="attachments">
            <div class="attachments-section">
              <div v-if="attachments.length > 0" class="attachment-list">
                <div v-for="attachment in attachments" :key="attachment.id" class="attachment-item">
                  <el-icon class="attachment-icon"><Document /></el-icon>
                  <span class="attachment-name">{{ attachment.name }}</span>
                  <span class="attachment-size">{{ attachment.size }}</span>
                  <el-button type="text" @click="handleDownloadAttachment(attachment)">下载</el-button>
                </div>
              </div>
              <el-empty v-else description="暂无附件" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="备注" name="notes">
            <div class="notes-section">
              <div v-if="paymentInfo.remarks" class="notes-content">
                {{ paymentInfo.remarks }}
              </div>
              <el-empty v-else description="暂无备注" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ArrowLeft, Document } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const router = useRouter();
const route = useRoute();

// 当前选中的tab
const activeTab = ref('attachments');

// 付款基本信息
const paymentInfo = reactive({
  paymentNo: 'PAY20240501001',
  customerName: '深圳科技有限公司',
  paymentMethod: '银行转账',
  paymentTerms: '月结30天',
  createTime: '2024-05-01 10:30:00',
  statementNo: 'STMT20240501001',
  completedDate: '',
  totalAmount: 187500.0,
  paidAmount: 100000.0,
  pendingAmount: 87500.0,
  status: 'pending', // pending, partial, completed
  remarks: '请注意付款时间，按合同约定执行。如有疑问请及时联系财务部门。'
});

// 物料明细数据
const materialList = ref([
  {
    id: 1,
    materialName: '智能控制器',
    model: 'CT-X100',
    brand: 'SelecTech',
    quantity: 50,
    unitPrice: 2500.0,
    totalPrice: 125000.0,
  },
  {
    id: 2,
    materialName: '传感器模块',
    model: 'SM-200',
    brand: 'TechSense',
    quantity: 100,
    unitPrice: 500.0,
    totalPrice: 50000.0,
  },
  {
    id: 3,
    materialName: '显示屏',
    model: 'DS-15',
    brand: 'DisplayTech',
    quantity: 25,
    unitPrice: 500.0,
    totalPrice: 12500.0,
  },
]);

// 附件数据
const attachments = ref([
  {
    id: 1,
    name: '付款凭证.pdf',
    size: '2.3MB',
    url: '/files/payment_voucher.pdf'
  },
  {
    id: 2,
    name: '合同扫描件.pdf',
    size: '5.1MB',
    url: '/files/contract_scan.pdf'
  }
]);

// 付款统计信息
const paymentSummary = computed(() => {
  const materialsTotal = materialList.value.reduce((sum, item) => sum + item.totalPrice, 0);
  const freight = 500.0;
  const totalAmount = materialsTotal + freight;

  return {
    materialsTotal,
    freight,
    totalAmount
  };
});

// 工具函数
const formatPrice = (price) => {
  if (price === null || price === undefined) return '0.00';
  return price.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    partial: 'info',
    completed: 'success',
    overdue: 'danger',
  };
  return statusMap[status] || 'info';
};

const getStatusLabel = (status) => {
  const statusMap = {
    pending: '待付款',
    partial: '部分付款',
    completed: '已完成',
    overdue: '逾期',
  };
  return statusMap[status] || '未知';
};

// 事件处理
const goBack = () => {
  router.push('/trade/sale/pay');
};

const handleCompletePayment = () => {
  ElMessage.success('付款完成功能开发中...');
};

const handlePrint = () => {
  ElMessage.success('打印功能开发中...');
};

const handleExport = () => {
  ElMessage.success('导出功能开发中...');
};

const handleDownloadAttachment = (attachment) => {
  ElMessage.success(`下载附件：${attachment.name}`);
  // 这里可以添加实际的下载逻辑
};

// 根据ID获取付款单数据
const getPaymentDetails = (id) => {
  // 这里应该调用API获取真实数据，目前使用模拟数据
  const mockData = {
    1: {
      paymentNo: 'PAY20240501001',
      customerName: '北京科技有限公司',
      paymentMethod: '现金/电汇',
      paymentTerms: '月结30天',
      createTime: '2024-05-01 10:00:00',
      statementNo: 'INV20240501001',
      completedDate: '',
      totalAmount: 58600.0,
      paidAmount: 0.0,
      pendingAmount: 58600.0,
      status: 'pending',
      remarks: '请按合同约定时间付款。'
    },
    2: {
      paymentNo: 'PAY20240501002',
      customerName: '上海工贸公司',
      paymentMethod: '银行承兑汇票',
      paymentTerms: '预付款',
      createTime: '2024-05-01 11:30:00',
      statementNo: 'INV20240501002',
      completedDate: '2024-05-15 14:20:00',
      totalAmount: 23250.0,
      paidAmount: 23250.0,
      pendingAmount: 0.0,
      status: 'completed',
      remarks: '付款已完成，感谢合作。'
    },
    3: {
      paymentNo: 'PAY20240501003',
      customerName: '深圳制造集团',
      paymentMethod: '现金/电汇',
      paymentTerms: '月结',
      createTime: '2024-05-01 14:15:00',
      statementNo: 'INV20240501003',
      completedDate: '2024-05-15 16:30:00',
      totalAmount: 42100.0,
      paidAmount: 42100.0,
      pendingAmount: 0.0,
      status: 'completed',
      remarks: '合作愉快。'
    },
    4: {
      paymentNo: 'PAY20240401001',
      customerName: '广州贸易公司',
      paymentMethod: '银行承兑汇票',
      paymentTerms: '月结15天',
      createTime: '2024-04-01 16:45:00',
      statementNo: 'INV20240401001',
      completedDate: '',
      totalAmount: 16500.0,
      paidAmount: 0.0,
      pendingAmount: 16500.0,
      status: 'overdue',
      remarks: '付款已逾期，请尽快处理。'
    },
    5: {
      paymentNo: 'PAY20240502001',
      customerName: '天津实业有限公司',
      paymentMethod: '现金/电汇',
      paymentTerms: '预付款',
      createTime: '2024-05-02 09:00:00',
      statementNo: 'INV20240502001',
      completedDate: '',
      totalAmount: 35800.0,
      paidAmount: 0.0,
      pendingAmount: 35800.0,
      status: 'pending',
      remarks: '新客户，请注意风险控制。'
    },
    6: {
      paymentNo: 'PAY20240502002',
      customerName: '杭州科技发展公司',
      paymentMethod: '银行承兑汇票',
      paymentTerms: '月结60天',
      createTime: '2024-05-02 11:20:00',
      statementNo: 'INV20240502002',
      completedDate: '2024-05-10 14:45:00',
      totalAmount: 28900.0,
      paidAmount: 28900.0,
      pendingAmount: 0.0,
      status: 'completed',
      remarks: '长期合作客户，信用良好。'
    }
  };
  
  return mockData[id] || mockData[1]; // 默认返回第一个
};

// 页面初始化
onMounted(() => {
  const paymentId = route.params.id;
  const data = getPaymentDetails(paymentId);
  
  // 更新付款信息
  Object.assign(paymentInfo, data);
});
</script>

<style scoped>
.pay-detail-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  text-decoration: none;
}

.back-button:hover {
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.content-section {
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.basic-info {
  padding: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.label {
  min-width: 100px;
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
  font-weight: 400;
}

.value.amount {
  color: var(--el-color-primary);
  font-weight: 600;
  font-size: 16px;
}

.value.amount.pending {
  color: #e6a23c;
}

.material-detail {
  padding: 20px;
}

.material-summary {
  margin-top: 16px;
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
  text-align: right;
}

.summary-content {
  display: inline-block;
  width: 300px;
  padding: 0 16px;
}

.summary-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: start;
}

.summary-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-column:not(:last-child) {
  border-right: 1px solid #e4e7ed;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.summary-item.total {
  font-weight: 600;
  font-size: 16px;
  color: var(--el-color-primary);
  margin-top: 8px;
  padding-top: 16px;
}

.summary-column:last-child {
  border-top: 1px solid #e4e7ed;
}

.summary-label {
  color: #666;
  font-size: 14px;
  white-space: nowrap;
}

.summary-value {
  color: #333;
  font-size: 14px;
  font-weight: 500;
  text-align: right;
}

.summary-item.total .summary-value {
  color: var(--el-color-primary);
  font-weight: 600;
  font-size: 16px;
}

.attachment-notes {
  padding: 0 20px 20px;
}

.info-tabs {
  margin-top: 10px;
}

.attachments-section {
  padding: 20px 0;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafbfc;
}

.attachment-icon {
  color: #409eff;
  font-size: 18px;
}

.attachment-name {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.attachment-size {
  color: #666;
  font-size: 14px;
}

.notes-section {
  padding: 20px 0;
}

.notes-content {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
  line-height: 1.6;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pay-detail-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-content {
    width: 100%;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .basic-info .el-col {
    margin-bottom: 8px;
  }

  .label {
    min-width: 80px;
    font-size: 14px;
  }

  .value {
    font-size: 14px;
  }

  .summary-columns {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .summary-item {
    font-size: 13px;
    padding: 6px 0;
  }

  .summary-item.total {
    font-size: 15px;
  }

  .summary-item.total .summary-value {
    font-size: 15px;
  }

  .attachment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

/* Tabs 样式优化 */
:deep(.el-tabs__item) {
  font-weight: 600;
}

:deep(.el-tabs__content) {
  margin-top: 0;
}
</style>