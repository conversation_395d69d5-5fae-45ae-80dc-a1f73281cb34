<template>
  <el-card class="payment-order-container">
    <!-- 搜索区 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="付款单号">
          <el-input v-model="searchForm.paymentId" placeholder="请输入付款单号" clearable></el-input>
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input v-model="searchForm.customerName" placeholder="请输入客户名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="待收款" value="pending"></el-option>
            <el-option label="已收款" value="completed"></el-option>
            <el-option label="已逾期" value="overdue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属对账单">
          <el-input v-model="searchForm.invoiceId" placeholder="请输入对账单号" clearable></el-input>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.creationDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="付款截止时间">
          <el-date-picker
            v-model="searchForm.dueDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleResetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区 -->
    <div class="table-content-container" v-loading="loading">
      <el-table :data="filteredPayments" style="width: 100%" border>
        <el-table-column prop="paymentId" label="付款单号" min-width="180">
          <template #default="scope">
            <el-link type="primary" @click="handleViewDetails(scope.row)">{{ scope.row.paymentId }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="客户名称" min-width="150">
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="120" align="center">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ formatStatus(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" label="应付总额" min-width="150" align="right">
          <template #default="scope">
            {{ formatCurrency(scope.row.totalAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="paidAmount" label="实付总额" min-width="150" align="right">
          <template #default="scope">
            {{ formatCurrency(scope.row.paidAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="remainingAmount" label="待付总额" min-width="150" align="right">
          <template #default="scope">
            {{ formatCurrency(scope.row.remainingAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="invoiceId" label="所属对账单" min-width="180">
          <template #default="scope">
            <el-link type="primary" @click="handleViewInvoiceDetails(scope.row)">{{ scope.row.invoiceId }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="creationTime" label="创建时间" min-width="160">
          <template #default="scope">
            {{ formatDate(scope.row.creationTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="completionTime" label="付款完成时间" min-width="160">
          <template #default="scope">
            {{ scope.row.completionTime ? formatDate(scope.row.completionTime) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="dueDate" label="付款截止时间" min-width="160">
          <template #default="scope">
            {{ formatDate(scope.row.dueDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="paymentMethod" label="付款方式" min-width="150" align="center">
          <template #default="scope">
            {{ formatPaymentMethod(scope.row.paymentMethod) }}
          </template>
        </el-table-column>
        <el-table-column prop="paymentTerms" label="付款条件" min-width="150">
          <template #default="scope">
            {{ scope.row.paymentTerms }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120" align="center" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              link 
              size="small" 
              v-if="scope.row.status === 'pending'" 
              @click="handleConfirmPayment(scope.row)"
            >确认收款</el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
      
      <el-pagination
        class="pagination-container"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalFilteredPayments"
        @size-change="handlePageSizeChange"
        @current-change="handleCurrentPageChange"
      >
      </el-pagination>
    </div>
  </el-card>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索表单
const searchForm = reactive({
  paymentId: '',
  customerName: '',
  status: '',
  invoiceId: '',
  creationDate: [],
  dueDate: []
})

// 模拟数据
const mockPayments = [
  {
    id: 1,
    paymentId: 'PAY20240501001',
    customerName: '北京科技有限公司',
    status: 'pending',
    totalAmount: 58600.00,
    paidAmount: 0.00,
    remainingAmount: 58600.00,
    invoiceId: 'INV20240501001',
    creationTime: '2024-05-01 10:00:00',
    completionTime: null,
    dueDate: '2024-05-31 23:59:59',
    paymentMethod: 'cash_wire',
    paymentTerms: '月结30天'
  },
  {
    id: 2,
    paymentId: 'PAY20240501002',
    customerName: '上海工贸公司',
    status: 'completed',
    totalAmount: 23250.00,
    paidAmount: 23250.00,
    remainingAmount: 0.00,
    invoiceId: 'INV20240501002',
    creationTime: '2024-05-01 11:30:00',
    completionTime: '2024-05-15 14:20:00',
    dueDate: '2024-05-31 23:59:59',
    paymentMethod: 'bank_acceptance',
    paymentTerms: '预付款'
  },
  {
    id: 3,
    paymentId: 'PAY20240501003',
    customerName: '深圳制造集团',
    status: 'completed',
    totalAmount: 42100.00,
    paidAmount: 42100.00,
    remainingAmount: 0.00,
    invoiceId: 'INV20240501003',
    creationTime: '2024-05-01 14:15:00',
    completionTime: '2024-05-15 16:30:00',
    dueDate: '2024-05-31 23:59:59',
    paymentMethod: 'cash_wire',
    paymentTerms: '月结'
  },
  {
    id: 4,
    paymentId: 'PAY20240401001',
    customerName: '广州贸易公司',
    status: 'overdue',
    totalAmount: 16500.00,
    paidAmount: 0.00,
    remainingAmount: 16500.00,
    invoiceId: 'INV20240401001',
    creationTime: '2024-04-01 16:45:00',
    completionTime: null,
    dueDate: '2024-04-30 23:59:59',
    paymentMethod: 'bank_acceptance',
    paymentTerms: '月结15天'
  },
  {
    id: 5,
    paymentId: 'PAY20240502001',
    customerName: '天津实业有限公司',
    status: 'pending',
    totalAmount: 35800.00,
    paidAmount: 0.00,
    remainingAmount: 35800.00,
    invoiceId: 'INV20240502001',
    creationTime: '2024-05-02 09:00:00',
    completionTime: null,
    dueDate: '2024-06-01 23:59:59',
    paymentMethod: 'cash_wire',
    paymentTerms: '预付款'
  },
  {
    id: 6,
    paymentId: 'PAY20240502002',
    customerName: '杭州科技发展公司',
    status: 'completed',
    totalAmount: 28900.00,
    paidAmount: 28900.00,
    remainingAmount: 0.00,
    invoiceId: 'INV20240502002',
    creationTime: '2024-05-02 11:20:00',
    completionTime: '2024-05-10 14:45:00',
    dueDate: '2024-06-01 23:59:59',
    paymentMethod: 'bank_acceptance',
    paymentTerms: '月结60天'
  }
]

// 过滤后的数据
const filteredPayments = computed(() => {
  let result = [...mockPayments]
  
  // 按付款单号过滤
  if (searchForm.paymentId) {
    result = result.filter(payment => payment.paymentId.includes(searchForm.paymentId))
  }
  
  // 按客户名称过滤
  if (searchForm.customerName) {
    result = result.filter(payment => payment.customerName.includes(searchForm.customerName))
  }
  
  // 按状态过滤
  if (searchForm.status) {
    result = result.filter(payment => payment.status === searchForm.status)
  }
  
  // 按所属对账单过滤
  if (searchForm.invoiceId) {
    result = result.filter(payment => payment.invoiceId.includes(searchForm.invoiceId))
  }
  
  // 按创建时间过滤
  if (searchForm.creationDate && searchForm.creationDate.length === 2) {
    const startDate = new Date(searchForm.creationDate[0])
    const endDate = new Date(searchForm.creationDate[1])
    endDate.setHours(23, 59, 59)
    result = result.filter(payment => {
      const createTime = new Date(payment.creationTime)
      return createTime >= startDate && createTime <= endDate
    })
  }
  
  // 按付款截止时间过滤
  if (searchForm.dueDate && searchForm.dueDate.length === 2) {
    const startDate = new Date(searchForm.dueDate[0])
    const endDate = new Date(searchForm.dueDate[1])
    endDate.setHours(23, 59, 59)
    result = result.filter(payment => {
      const dueDateTime = new Date(payment.dueDate)
      return dueDateTime >= startDate && dueDateTime <= endDate
    })
  }
  
  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return result.slice(start, end)
})

// 总记录数
const totalFilteredPayments = computed(() => {
  let result = [...mockPayments]
  
  // 按付款单号过滤
  if (searchForm.paymentId) {
    result = result.filter(payment => payment.paymentId.includes(searchForm.paymentId))
  }
  
  // 按客户名称过滤
  if (searchForm.customerName) {
    result = result.filter(payment => payment.customerName.includes(searchForm.customerName))
  }
  
  // 按状态过滤
  if (searchForm.status) {
    result = result.filter(payment => payment.status === searchForm.status)
  }
  
  // 按所属对账单过滤
  if (searchForm.invoiceId) {
    result = result.filter(payment => payment.invoiceId.includes(searchForm.invoiceId))
  }
  
  // 按创建时间过滤
  if (searchForm.creationDate && searchForm.creationDate.length === 2) {
    const startDate = new Date(searchForm.creationDate[0])
    const endDate = new Date(searchForm.creationDate[1])
    endDate.setHours(23, 59, 59)
    result = result.filter(payment => {
      const createTime = new Date(payment.creationTime)
      return createTime >= startDate && createTime <= endDate
    })
  }
  
  // 按付款截止时间过滤
  if (searchForm.dueDate && searchForm.dueDate.length === 2) {
    const startDate = new Date(searchForm.dueDate[0])
    const endDate = new Date(searchForm.dueDate[1])
    endDate.setHours(23, 59, 59)
    result = result.filter(payment => {
      const dueDateTime = new Date(payment.dueDate)
      return dueDateTime >= startDate && dueDateTime <= endDate
    })
  }
  
  return result.length
})

// 查询
const handleSearch = () => {
  currentPage.value = 1
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

// 重置查询条件
const handleResetSearch = () => {
  // 重置搜索表单
  searchForm.paymentId = ''
  searchForm.customerName = ''
  searchForm.status = ''
  searchForm.invoiceId = ''
  searchForm.creationDate = []
  searchForm.dueDate = []
  
  // 重置分页
  currentPage.value = 1
  
  // 执行查询
  handleSearch()
}

// 分页操作
const handlePageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentPageChange = (page) => {
  currentPage.value = page
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 格式化金额
const formatCurrency = (amount) => {
  return `¥ ${amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
}

// 格式化状态
const formatStatus = (status) => {
  const statusMap = {
    'pending': '待收款',
    'completed': '已收款',
    'overdue': '已逾期'
  }
  return statusMap[status] || status
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'completed': 'success',
    'overdue': 'danger'
  }
  return typeMap[status] || ''
}

// 格式化付款方式
const formatPaymentMethod = (method) => {
  const methodMap = {
    'cash_wire': '现金/电汇',
    'bank_acceptance': '银行承兑汇票'
  }
  return methodMap[method] || method
}

// 查看付款单详情
const handleViewDetails = (row) => {
  // 跳转到付款单详情页
  router.push(`/trade/sale/pay/detail/${row.id}`)
}

// 查看对账单详情
const handleViewInvoiceDetails = (row) => {
  // 跳转到对账单详情页
  router.push(`/trade/so/inv-detail/${row.invoiceId}`)
}

// 确认收款
const handleConfirmPayment = (row) => {
  ElMessageBox.confirm(
    `确认收款单号为 ${row.paymentId} 的付款吗？`,
    '确认收款',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success(`已确认收款：${row.paymentId}`)
    // 这里可以调用API更新付款状态
    // 模拟更新状态
    row.status = 'completed'
    row.completionTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
    row.paidAmount = row.totalAmount
    row.remainingAmount = 0
  }).catch(() => {
    ElMessage.info('已取消操作')
  })
}

// 页面初始化
onMounted(() => {
  // 初始化数据，可以在这里调用API获取真实数据
  handleSearch()
})
</script>

<style scoped>
.payment-order-container {
  margin: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.table-content-container {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
