<template>
  <el-card class="invoice-reconciliation-container">
    <!-- 搜索区 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="对账单号">
          <el-input v-model="searchForm.invoiceId" placeholder="请输入对账单号" clearable></el-input>
        </el-form-item>
        <el-form-item label="对账周期">
          <el-date-picker
            v-model="searchForm.periodRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.creationDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="生成中" value="generating"></el-option>
            <el-option label="待对账" value="pending"></el-option>
            <el-option label="已锁定" value="locked"></el-option>
            <el-option label="已结算" value="settled"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleResetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区 -->
    <div class="table-content-container" v-loading="loading">
      <el-table :data="filteredInvoices" style="width: 100%" border>
        <el-table-column prop="invoiceId" label="对账单号" min-width="180">
          <template #default="scope">
            <el-link type="primary" @click="handleViewDetails(scope.row)">{{ scope.row.invoiceId }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="periodStart" label="对账周期" min-width="200">
          <template #default="scope">
            {{ formatDate(scope.row.periodStart) }} 至 {{ formatDate(scope.row.periodEnd) }}
          </template>
        </el-table-column>
        <el-table-column prop="creationTime" label="创建时间" min-width="160">
          <template #default="scope">
            {{ formatDate(scope.row.creationTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" label="总金额" min-width="150" align="right">
          <template #default="scope">
            {{ formatCurrency(scope.row.totalAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="120" align="center">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ formatStatus(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceStatus" label="开票状态" min-width="120" align="center">
          <template #default="scope">
            <el-tag :type="getInvoiceStatusTagType(scope.row.invoiceStatus)">
              {{ formatInvoiceStatus(scope.row.invoiceStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              link 
              size="small" 
              v-if="scope.row.status === 'locked' || scope.row.status === 'settled'" 
              @click="handlePaymentDetails(scope.row)"
            >付款详情</el-button>
            <el-button 
              type="primary" 
              link 
              size="small" 
              v-if="scope.row.invoiceStatus === 'invoicing'" 
              @click="handleUploadInvoice(scope.row)"
            >上传发票</el-button>
            <el-button 
              type="primary" 
              link 
              size="small" 
              v-if="scope.row.invoiceStatus === 'invoicing'" 
              @click="handleExport(scope.row)"
            >导出开票信息</el-button>
            <el-button 
              type="primary" 
              link 
              size="small" 
              v-if="scope.row.invoiceStatus === 'invoiced'" 
              @click="handleDownloadInvoice(scope.row)"
            >下载发票</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <el-pagination
        class="pagination-container"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalFilteredInvoices"
        @size-change="handlePageSizeChange"
        @current-change="handleCurrentPageChange"
      >
      </el-pagination>
    </div>




  </el-card>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索表单
const searchForm = reactive({
  invoiceId: '',
  periodRange: [],
  creationDate: [],
  status: ''
})





// 模拟数据
const mockInvoices = [
  {
    id: 1,
    invoiceId: 'INV20240501001',
    periodStart: '2024-04-01',
    periodEnd: '2024-04-30',
    creationTime: '2024-05-01 10:00:00',
    totalAmount: 58600.00,
    status: 'pending',
    invoiceStatus: 'not_invoiced',
    items: [
      {
        id: 101,
        materialCode: 'MT001',
        materialName: '钢板A型',
        model: 'STA-001',
        specification: '1000x2000x2mm',
        brand: '宝钢',
        unit: '块',
        unitPrice: 200.00,
        quantity: 20,
        amount: 4000.00,
        deliveryNo: 'DN20240410001',
        receiveDate: '2024-04-10',
        disputed: false,
        remarks: ''
      },
      {
        id: 102,
        materialCode: 'MT002',
        materialName: '铝合金型材',
        model: 'AL-002',
        specification: '50x50x5mm',
        brand: '明泰铝业',
        unit: '根',
        unitPrice: 120.00,
        quantity: 50,
        amount: 6000.00,
        deliveryNo: 'DN20240415002',
        receiveDate: '2024-04-15',
        disputed: false,
        remarks: ''
      }
    ]
  },
  {
    id: 2,
    invoiceId: 'INV20240501002',
    periodStart: '2024-04-01',
    periodEnd: '2024-04-30',
    creationTime: '2024-05-01 11:30:00',
    totalAmount: 23250.00,
    status: 'locked',
    invoiceStatus: 'invoicing',
    items: [
      {
        id: 201,
        materialCode: 'MT006',
        materialName: '螺丝螺母套装',
        model: 'SC-006',
        specification: 'M8',
        brand: '力杰',
        unit: '套',
        unitPrice: 15.00,
        quantity: 50,
        amount: 750.00,
        deliveryNo: 'DN20240405006',
        receiveDate: '2024-04-05',
        disputed: false,
        remarks: ''
      }
    ]
  },
  {
    id: 3,
    invoiceId: 'INV20240501003',
    periodStart: '2024-04-01',
    periodEnd: '2024-04-30',
    creationTime: '2024-05-01 14:15:00',
    totalAmount: 42100.00,
    status: 'generating',
    invoiceStatus: 'not_invoiced',
    items: [
      {
        id: 301,
        materialCode: 'MT009',
        materialName: '伺服电机',
        model: 'SVM-009',
        specification: '2kW',
        brand: '安川',
        unit: '台',
        unitPrice: 2200.00,
        quantity: 5,
        amount: 11000.00,
        deliveryNo: 'DN20240408009',
        receiveDate: '2024-04-08',
        disputed: false,
        remarks: ''
      }
    ]
  },
  {
    id: 4,
    invoiceId: 'INV20240501004',
    periodStart: '2024-04-01',
    periodEnd: '2024-04-30',
    creationTime: '2024-05-01 16:45:00',
    totalAmount: 16500.00,
    status: 'settled',
    invoiceStatus: 'invoiced',
    items: [
      {
        id: 401,
        materialCode: 'MT013',
        materialName: '触摸屏',
        model: 'HMI-013',
        specification: '10英寸',
        brand: '威纶通',
        unit: '台',
        unitPrice: 1200.00,
        quantity: 10,
        amount: 12000.00,
        deliveryNo: 'DN20240407013',
        receiveDate: '2024-04-07',
        disputed: false,
        remarks: ''
      }
    ]
  }
]

// 过滤后的数据
const filteredInvoices = computed(() => {
  let result = [...mockInvoices]
  
  // 按对账单号过滤
  if (searchForm.invoiceId) {
    result = result.filter(invoice => invoice.invoiceId.includes(searchForm.invoiceId))
  }
  
  // 按对账周期过滤
  if (searchForm.periodRange && searchForm.periodRange.length === 2) {
    const startDate = new Date(searchForm.periodRange[0])
    const endDate = new Date(searchForm.periodRange[1])
    result = result.filter(invoice => {
      const invoiceStart = new Date(invoice.periodStart)
      const invoiceEnd = new Date(invoice.periodEnd)
      return invoiceStart >= startDate && invoiceEnd <= endDate
    })
  }
  
  // 按创建时间过滤
  if (searchForm.creationDate && searchForm.creationDate.length === 2) {
    const startDate = new Date(searchForm.creationDate[0])
    const endDate = new Date(searchForm.creationDate[1])
    endDate.setHours(23, 59, 59)
    result = result.filter(invoice => {
      const createTime = new Date(invoice.creationTime)
      return createTime >= startDate && createTime <= endDate
    })
  }
  
  // 按状态过滤
  if (searchForm.status) {
    result = result.filter(invoice => invoice.status === searchForm.status)
  }
  
  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return result.slice(start, end)
})

// 总记录数
const totalFilteredInvoices = computed(() => {
  let result = [...mockInvoices]
  
  // 按对账单号过滤
  if (searchForm.invoiceId) {
    result = result.filter(invoice => invoice.invoiceId.includes(searchForm.invoiceId))
  }
  
  // 按对账周期过滤
  if (searchForm.periodRange && searchForm.periodRange.length === 2) {
    const startDate = new Date(searchForm.periodRange[0])
    const endDate = new Date(searchForm.periodRange[1])
    result = result.filter(invoice => {
      const invoiceStart = new Date(invoice.periodStart)
      const invoiceEnd = new Date(invoice.periodEnd)
      return invoiceStart >= startDate && invoiceEnd <= endDate
    })
  }
  
  // 按创建时间过滤
  if (searchForm.creationDate && searchForm.creationDate.length === 2) {
    const startDate = new Date(searchForm.creationDate[0])
    const endDate = new Date(searchForm.creationDate[1])
    endDate.setHours(23, 59, 59)
    result = result.filter(invoice => {
      const createTime = new Date(invoice.creationTime)
      return createTime >= startDate && createTime <= endDate
    })
  }
  
  // 按状态过滤
  if (searchForm.status) {
    result = result.filter(invoice => invoice.status === searchForm.status)
  }
  
  return result.length
})

// 查询
const handleSearch = () => {
  currentPage.value = 1
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

// 重置查询条件
const handleResetSearch = () => {
  // 重置搜索表单
  searchForm.invoiceId = ''
  searchForm.periodRange = []
  searchForm.creationDate = []
  searchForm.status = ''
  
  // 重置分页
  currentPage.value = 1
  
  // 执行查询
  handleSearch()
}

// 分页操作
const handlePageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentPageChange = (page) => {
  currentPage.value = page
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 格式化金额
const formatCurrency = (amount) => {
  return `¥ ${amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
}

// 格式化状态
const formatStatus = (status) => {
  const statusMap = {
    'generating': '生成中',
    'pending': '待对账',
    'locked': '已锁定',
    'settled': '已结算'
  }
  return statusMap[status] || status
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    'generating': 'info',
    'pending': 'warning',
    'locked': 'success',
    'settled': 'primary'
  }
  return typeMap[status] || ''
}

// 格式化开票状态
const formatInvoiceStatus = (status) => {
  const statusMap = {
    'invoicing': '开票中',
    'not_invoiced': '未开票',
    'invoiced': '已开票'
  }
  return statusMap[status] || status
}

// 获取开票状态标签类型
const getInvoiceStatusTagType = (status) => {
  const typeMap = {
    'invoicing': 'warning',
    'not_invoiced': 'info',
    'invoiced': 'success'
  }
  return typeMap[status] || ''
}

// 查看对账单详情
const handleViewDetails = (row) => {
  // 跳转到对账单详情页
  router.push(`/trade/so/inv-detail/${row.id}`)
}

// 付款详情
const handlePaymentDetails = (row) => {
  ElMessage.info(`查看付款详情：${row.invoiceId}`)
  // 这里可以跳转到付款详情页或者打开付款详情弹窗
  // router.push(`/trade/so/payment-detail/${row.id}`)
}

// 上传发票
const handleUploadInvoice = (row) => {
  ElMessage.info(`上传发票：${row.invoiceId}`)
  // 这里可以打开文件上传对话框
}

// 下载发票
const handleDownloadInvoice = (row) => {
  ElMessage.success(`下载发票：${row.invoiceId}`)
  // 这里可以触发文件下载
}



// 页面初始化
onMounted(() => {
  // 初始化数据，可以在这里调用API获取真实数据
  handleSearch()
})
</script>

<style scoped>
.invoice-reconciliation-container {
  margin: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.table-content-container {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}


</style>