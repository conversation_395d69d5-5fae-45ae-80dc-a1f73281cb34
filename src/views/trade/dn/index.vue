<template>
  <el-card class="delivery-note-container">
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="送货单号">
          <el-input v-model="searchForm.deliveryNo" placeholder="请输入送货单号" clearable />
        </el-form-item>
        <el-form-item label="客户">
          <el-select v-model="searchForm.customer" placeholder="请选择客户" clearable filterable>
            <el-option 
              v-for="customer in customerOptions" 
              :key="customer.value"
              :label="customer.label" 
              :value="customer.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="供应商">
          <el-select v-model="searchForm.supplier" placeholder="请选择供应商" clearable filterable>
            <el-option 
              v-for="supplier in supplierOptions" 
              :key="supplier.value"
              :label="supplier.label" 
              :value="supplier.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option 
              v-for="status in statusOptions" 
              :key="status.value"
              :label="status.label" 
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-container" v-loading="loading">
      <!-- 表格操作栏 -->
      <div class="table-header-container">
        <div class="table-actions">
          <el-button 
            type="primary" 
            @click="toggleAllExpand"
          >
            {{ isAllExpanded ? '全部折叠' : '全部展开' }}
          </el-button>
        </div>
      </div>
      
      <!-- 主表格 -->
      <el-table
        :data="tableData"
        row-key="id"
        :expand-row-keys="expandedRows"
        @expand-change="handleExpandChange"
        class="delivery-table"
        stripe
        border
      >
        <!-- 展开列 -->
        <el-table-column type="expand" width="50" fixed="left">
          <template #default="{ row }">
            <div class="material-table-container">
              <el-table
                :data="row.materials"
                class="material-table"
                size="small"
                border
              >
                <el-table-column prop="materialName" label="物料名称" min-width="120" />
                <el-table-column prop="model" label="型号" min-width="100" />
                <el-table-column prop="brand" label="品牌" min-width="100" />
                <el-table-column prop="quantity" label="数量" width="80" align="center">
                
                </el-table-column>
                <el-table-column prop="purchaseOrder" label="所属采购单" min-width="120">
                  <template #default="{ row }">
                    <el-button 
                      type="text" 
                      @click="goToPurchaseOrder(row.purchaseOrder)"
                    >
                      {{ row.purchaseOrder }}
                    </el-button>
                  </template>
                </el-table-column>
                <el-table-column prop="salesOrder" label="所属订单" min-width="120">
                  <template #default="{ row }">
                    <el-button 
                      type="text" 
                      @click="goToSalesOrder(row.salesOrder)"
                    >
                      {{ row.salesOrder }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>

        <!-- 送货单号 -->
        <el-table-column prop="deliveryNo" label="送货单号" min-width="140">
          <template #default="{ row }">
            <el-button 
              type="text" 
              @click="goToDeliveryDetail(row.deliveryNo)"
            >
              {{ row.deliveryNo }}
            </el-button>
          </template>
        </el-table-column>

        <!-- 客户 -->
        <el-table-column prop="customer" label="客户" min-width="120" />

        <!-- 供应商 -->
        <el-table-column prop="supplier" label="供应商" min-width="120" />

        <!-- 状态 -->
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 物料总数量 -->
        <el-table-column prop="totalQuantity" label="物料总数量" width="120" align="center">
          
        </el-table-column>

        <!-- 物流单数 -->
        <el-table-column prop="logisticsCount" label="物流单数" width="100" align="center">
        </el-table-column>

        <!-- 创建时间 -->
        <el-table-column prop="createTime" label="创建时间" width="160" />

        <!-- 收货时间 -->
        <el-table-column prop="receiveTime" label="收货时间" width="160">
          <template #default="{ row }">
            <span v-if="row.receiveTime">{{ row.receiveTime }}</span>
          </template>
        </el-table-column>

       
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const expandedRows = ref([])
const dateRange = ref([])
const total = ref(0)
const isAllExpanded = ref(false)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
})

// 搜索表单
const searchForm = reactive({
  deliveryNo: '',
  customer: '',
  supplier: '',
  status: ''
})

// 选项数据
const customerOptions = ref([
  { value: 'customer1', label: '北京科技有限公司' },
  { value: 'customer2', label: '上海制造企业' },
  { value: 'customer3', label: '深圳电子公司' },
  { value: 'customer4', label: '广州贸易公司' }
])

const supplierOptions = ref([
  { value: 'supplier1', label: '华为技术有限公司' },
  { value: 'supplier2', label: '小米科技有限公司' },
  { value: 'supplier3', label: '联想集团有限公司' },
  { value: 'supplier4', label: '海康威视股份有限公司' }
])

const statusOptions = ref([
  { value: 'pending', label: '待发货' },
  { value: 'shipped', label: '已发货' },
  { value: 'delivered', label: '已送达' },
  { value: 'received', label: '已收货' },
  { value: 'cancelled', label: '已取消' }
])

// 表格数据
const tableData = ref([
  {
    id: 1,
    deliveryNo: 'DN202401001',
    customer: '北京科技有限公司',
    supplier: '华为技术有限公司',
    status: 'delivered',
    totalQuantity: 150,
    logisticsCount: 2,
    createTime: '2024-01-15 09:30:00',
    receiveTime: '2024-01-17 14:20:00',
    materials: [
      {
        materialName: '华为路由器',
        model: 'AR2220',
        brand: '华为',
        quantity: 50,
        purchaseOrder: 'PO202401001',
        salesOrder: 'SO202401001'
      },
      {
        materialName: '交换机',
        model: 'S5720-28P-SI',
        brand: '华为',
        quantity: 100,
        purchaseOrder: 'PO202401002',
        salesOrder: 'SO202401001'
      }
    ]
  },
  {
    id: 2,
    deliveryNo: 'DN202401002',
    customer: '上海制造企业',
    supplier: '小米科技有限公司',
    status: 'shipped',
    totalQuantity: 200,
    logisticsCount: 1,
    createTime: '2024-01-16 10:15:00',
    receiveTime: null,
    materials: [
      {
        materialName: '小米手机',
        model: 'Mi 14',
        brand: '小米',
        quantity: 100,
        purchaseOrder: 'PO202401003',
        salesOrder: 'SO202401002'
      },
      {
        materialName: '小米平板',
        model: 'Pad 6',
        brand: '小米',
        quantity: 100,
        purchaseOrder: 'PO202401004',
        salesOrder: 'SO202401002'
      }
    ]
  },
  {
    id: 3,
    deliveryNo: 'DN202401003',
    customer: '深圳电子公司',
    supplier: '联想集团有限公司',
    status: 'pending',
    totalQuantity: 75,
    logisticsCount: 3,
    createTime: '2024-01-17 11:45:00',
    receiveTime: null,
    materials: [
      {
        materialName: 'ThinkPad笔记本',
        model: 'X1 Carbon',
        brand: '联想',
        quantity: 25,
        purchaseOrder: 'PO202401005',
        salesOrder: 'SO202401003'
      },
      {
        materialName: '联想显示器',
        model: 'L24q-30',
        brand: '联想',
        quantity: 50,
        purchaseOrder: 'PO202401006',
        salesOrder: 'SO202401003'
      }
    ]
  }
])

// 设置分页总数
total.value = tableData.value.length

// 获取送货单列表
const getDeliveryList = () => {
  loading.value = true
  console.log('Fetching delivery list with params:', JSON.parse(JSON.stringify(queryParams)))
  // 模拟API调用
  setTimeout(() => {
    console.log('Data fetching completed')
    loading.value = false
  }, 300)
}

// 查询按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getDeliveryList()
}

// 重置按钮操作
const resetQuery = () => {
  searchForm.deliveryNo = ''
  searchForm.customer = ''
  searchForm.supplier = ''
  searchForm.status = ''
  dateRange.value = []
  handleQuery()
}

const handleEdit = (row) => {
  ElMessage.success(`编辑送货单: ${row.deliveryNo}`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除送货单 ${row.deliveryNo} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    ElMessage.success('删除成功')
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleExpandChange = (row, expandedRowsData) => {
  console.log('展开行变化:', row, expandedRowsData)
  expandedRows.value = expandedRowsData.map(item => item.id)
  // 更新全部展开状态
  isAllExpanded.value = expandedRows.value.length === tableData.value.length
}

// 分页条数变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getDeliveryList()
}

// 分页页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getDeliveryList()
}

// 状态相关方法
const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    shipped: 'primary',
    delivered: 'success',
    received: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待发货',
    shipped: '已发货',
    delivered: '已送达',
    received: '已收货',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

// 跳转方法
const goToDeliveryDetail = (deliveryNo) => {
  ElMessage.success(`跳转到送货单详情: ${deliveryNo}`)
  // 这里应该使用 router.push 跳转到详情页
}

const goToPurchaseOrder = (purchaseOrder) => {
  ElMessage.success(`跳转到采购单详情: ${purchaseOrder}`)
  // 这里应该使用 router.push 跳转到采购单详情页
}

const goToSalesOrder = (salesOrder) => {
  ElMessage.success(`跳转到订单详情: ${salesOrder}`)
  // 这里应该使用 router.push 跳转到订单详情页
}

// 生命周期
onMounted(() => {
  getDeliveryList()
})

// 展开/折叠全部
const toggleAllExpand = () => {
  isAllExpanded.value = !isAllExpanded.value
  if (isAllExpanded.value) {
    expandedRows.value = tableData.value.map(item => item.id)
  } else {
    expandedRows.value = []
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
  background-color: #f7f8fa;
  padding: 20px;
  border-radius: 4px;
}

.table-container {
  margin-bottom: 20px;
}

.table-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.delivery-no-link {
  font-weight: 600;
  color: #409eff;
}

.delivery-no-link:hover {
  color: #66b1ff;
}

.material-table-container {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  margin: 10px 0;
}

.material-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
}

.material-table {
  background: white;
  border-radius: 4px;
}

.link-button {
  color: #409eff;
  font-weight: 500;
}

.link-button:hover {
  color: #66b1ff;
}

.danger-text {
  color: #f56c6c;
}

.danger-text:hover {
  color: #f78989;
}
</style>