<template>
  <el-card class="order-list-container">
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="PO编号">
          <el-input v-model="queryParams.poNo" placeholder="请输入PO编号" clearable />
        </el-form-item>
        <el-form-item label="物料名称">
          <el-input v-model="queryParams.productName" placeholder="请输入物料名称" clearable />
        </el-form-item>
        <el-form-item label="物料型号">
          <el-input v-model="queryParams.model" placeholder="请输入物料型号" clearable />
        </el-form-item>
        <el-form-item label="物料分类">
          <el-input v-model="queryParams.category" placeholder="请输入物料分类" clearable />
        </el-form-item>
        <el-form-item label="品牌">
          <el-input v-model="queryParams.brand" placeholder="请输入品牌" clearable />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="待付款" value="pending_payment" />
            <el-option label="待发货" value="pending_shipment" />
            <el-option label="已发货" value="shipped" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="下单时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- View Switcher -->
    <div style="margin-bottom: 20px; display: flex; justify-content: flex-end;">
      <el-radio-group v-model="currentView" @change="handleViewChange">
        <el-radio-button label="order">订单视图</el-radio-button>
        <el-radio-button label="product">物料视图</el-radio-button>
      </el-radio-group>
    </div>

    <!-- Dynamically rendered view -->
    <div class="view-content-container" v-loading="loading">
      <OrderView v-if="currentView === 'order'" :filters="queryParams" />
      <ProductView v-if="currentView === 'product'" :filters="queryParams" />
      <!-- 
        Note: The old table and pagination are removed. 
        OrderView and ProductView are expected to manage their own data display and pagination based on the passed 'filters' prop.
        The parent 'loading' state is applied to the container; child components might have their own internal loading states too.
      -->
    </div>

  </el-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import OrderView from './OrderView.vue'
import ProductView from './ProductView.vue'

// 查询参数
const queryParams = reactive({
  poNo: '',
  status: '',
  productName: '',
  model: '',
  category: '',
  brand: '',
  pageNum: 1,
  pageSize: 10
})

const dateRange = ref([])
const loading = ref(false)
const total = ref(0)
const orderList = ref([])

const currentView = ref('order')

// 模拟数据
const mockOrderList = [
  {
    id: 1,
    poNo: 'PO20240501001',
    supplier: '上海机械制造有限公司',
    totalAmount: 25800.00,
    createTime: '2024-05-01 10:23:45',
    status: 'pending_payment',
    paymentMethod: '银行转账'
  },
  {
    id: 2,
    poNo: 'PO20240428002',
    supplier: '北京智能设备科技有限公司',
    totalAmount: 49990.00,
    createTime: '2024-04-28 14:35:12',
    status: 'pending_shipment',
    paymentMethod: '银行转账'
  },
  {
    id: 3,
    poNo: 'PO20240422003',
    supplier: '深圳电子元器件有限公司',
    totalAmount: 17900.00,
    createTime: '2024-04-22 09:15:30',
    status: 'shipped',
    paymentMethod: '在线支付'
  },
  {
    id: 4,
    poNo: 'PO20240415004',
    supplier: '广州自动化设备有限公司',
    totalAmount: 64500.00,
    createTime: '2024-04-15 16:40:22',
    status: 'completed',
    paymentMethod: '银行转账'
  },
  {
    id: 5,
    poNo: 'PO20240410005',
    supplier: '上海机械制造有限公司',
    totalAmount: 8765.50,
    createTime: '2024-04-10 11:20:18',
    status: 'cancelled',
    paymentMethod: '在线支付'
  }
]

// 获取订单状态标签
const getOrderStatusLabel = (status) => {
  const statusMap = {
    'pending_payment': '待付款',
    'pending_shipment': '待发货',
    'shipped': '已发货',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

// 获取订单状态样式类型
const getOrderStatusType = (status) => {
  const typeMap = {
    'pending_payment': 'warning',
    'pending_shipment': 'info',
    'shipped': 'primary',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status] || ''
}

// 判断订单是否可处理
const canProcess = (status) => {
  return ['pending_payment', 'pending_shipment', 'shipped'].includes(status)
}

// 判断订单是否可取消
const canCancel = (status) => {
  return ['pending_payment', 'pending_shipment'].includes(status)
}

// 获取订单列表
const getOrderList = () => {
  loading.value = true
  console.log('Fetching list with params:', JSON.parse(JSON.stringify(queryParams)), 'Current view:', currentView.value)
  // Simulate API call
  setTimeout(() => {
    // If OrderView/ProductView fetch their own data based on props,
    // this function might not need to set a local orderList.
    // For now, we can keep it to simulate the end of a query operation.
    // orderList.value = mockOrderList; // Child components have their own data
    // total.value = mockOrderList.length;
    console.log(`Data fetching for ${currentView.value} view would happen here.`);
    loading.value = false
  }, 300)
}

// 查询按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getOrderList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.poNo = ''
  queryParams.status = ''
  queryParams.productName = ''
  queryParams.model = ''
  queryParams.category = ''
  queryParams.brand = ''
  dateRange.value = []
  handleQuery()
}

// 新增按钮操作
const handleAdd = () => {
  console.log('新增订单')
}

// 查看订单详情
const handleView = (row) => {
  console.log('查看订单详情', row)
}

// 处理订单
const handleProcessOrder = (row) => {
  console.log('处理订单', row)
}

// 取消订单
const handleCancelOrder = (row) => {
  console.log('取消订单', row)
}

// 分页条数变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getOrderList()
}

// 分页页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getOrderList()
}

// Added: Function to handle view change
const handleViewChange = (view) => {
  // currentView is already updated by v-model from el-radio-group
  console.log('Switched to view:', view)
  // Trigger a query to reload/refilter data for the new view
  // This assumes OrderView/ProductView will use the queryParams
  handleQuery()
}

// 组件挂载时获取订单列表
onMounted(() => {
  getOrderList() // Initial load based on default view and filters
})
</script>

<style scoped>

.filter-container {
  margin-bottom: 20px;
  background-color: #f7f8fa;
  padding: 20px;
  border-radius: 4px;
}

/* Removed .table-container and .pagination-container styles as the old table is gone */
/* .table-container {
  margin-bottom: 20px;
} */

.table-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* .pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
} */

.view-content-container {
  /* Add any specific styling for the container of OrderView/ProductView if needed */
}
</style> 