<template>
  <div>
    <!-- Add button to trigger column configuration drawer -->
    <div style="display: flex; justify-content: space-between; margin-bottom: 16px;">
      <el-dropdown @command="handleBatchCommand">
        <el-button type="primary">
          批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="confirm">确认</el-dropdown-item>
            <el-dropdown-item command="delivery">送货</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-button @click="drawer = true" type="primary"> 配置列 </el-button>
    </div>

    <el-drawer title="配置列" v-model="drawer" direction="rtl" size="300px">
      <!-- Add checkboxes for each column (for both outer and inner tables) -->
      <div style="padding: 0 20px">
        <h4>订单列</h4>
        <div v-for="column in availableOrderColumns" :key="column.prop">
          <el-checkbox v-model="column.visible">{{ column.label }}</el-checkbox>
        </div>
        <el-divider />
        <h4>商品列</h4>
        <div v-for="column in availableProductColumns" :key="column.prop">
          <el-checkbox v-model="column.visible">{{ column.label }}</el-checkbox>
        </div>
      </div>
    </el-drawer>

    <el-table :data="orderData" style="width: 100%" border row-key="id" @expand-change="handleExpandChange">
      <el-table-column type="expand">
        <template #default="props">
          <div style="padding: 10px 20px">
            <el-table :data="props.row.products" border style="width: 100%" @selection-change="(selection) => handleProductSelectionChange(selection, props.row)">
              <el-table-column type="selection" width="55" />
              <template v-for="column in visibleProductColumns" :key="column.prop">
                <el-table-column v-if="column.visible" :prop="column.prop" :label="column.label" :min-width="column.minWidth">
                  <template v-if="column.prop === 'unitPrice' || column.prop === 'totalPrice'" #default="scope"> ¥{{ formatPrice(scope.row[column.prop]) }} </template>
                </el-table-column>
              </template>
              <el-table-column label="操作" min-width="100" fixed="right">
                <template #default="scope">
                  <el-button size="small" link type="danger" @click="handleCancelProduct(scope.row, props.row)">取消</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>

      <template v-for="column in visibleOrderColumns" :key="column.prop">
        <el-table-column v-if="column.visible" :prop="column.prop" :label="column.label" :min-width="column.minWidth">
          <template v-if="column.prop === 'totalAmount'" #default="scope"> ¥{{ formatPrice(scope.row[column.prop]) }} </template>
          <template v-else-if="column.prop === 'orderStatus'" #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
              {{ getOrderStatusLabel(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
      </template>

      <el-table-column label="操作" min-width="100" fixed="right">
        <template #default="scope">
          <el-button size="small" type="danger" link @click="handleCancelOrder(scope.row)">取消</el-button>
          <!-- <el-button size="small" type="primary" link @click="handleViewOrderDetails(scope.row)">订单详情</el-button>
          <el-button v-if="scope.row.orderStatus === 'pending_confirmation'" size="small" type="primary" link @click="handleConfirmOrder(scope.row)" style="margin-left: 5px">确认</el-button> -->
          <!-- Add other order-level actions if needed -->
        </template>
      </el-table-column>
    </el-table>

    <!-- Add pagination if needed -->
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { ArrowDown } from '@element-plus/icons-vue';

const drawer = ref(false);
const selectedOrderProducts = ref({}); // Store selected products per order

// Helper function to format price
const formatPrice = (price) => {
  if (price === null || price === undefined) return '0.00';
  return price.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// Columns for the outer (Order) table
const availableOrderColumns = reactive([
  { prop: 'poNo', label: 'PO号', visible: true, minWidth: 180 },
  { prop: 'customer', label: '客户', visible: true, minWidth: 120 },
  { prop: 'supplier', label: '供应商', visible: true, minWidth: 150 },
  { prop: 'orderTime', label: '下单时间', visible: true, minWidth: 160 },
  { prop: 'endTime', label: '结束时间', visible: true, minWidth: 160 },
  { prop: 'orderStatus', label: '状态', visible: true, minWidth: 120 },
  { prop: 'salesOrderNo', label: '所属销售单号', visible: true, minWidth: 180 },
  { prop: 'productCount', label: '商品数量', visible: true, minWidth: 100 },
  { prop: 'totalAmount', label: '总金额', visible: true, minWidth: 120 },
]);

// Columns for the inner (Product) table
const availableProductColumns = reactive([
  { prop: 'productName', label: '商品名称', visible: true, minWidth: 150 },
  { prop: 'model', label: '型号', visible: true, minWidth: 120 },
  { prop: 'brand', label: '品牌', visible: true, minWidth: 100 },
  { prop: 'category', label: '分类', visible: true, minWidth: 100 },
  { prop: 'totalQuantity', label: '总数量', visible: true, minWidth: 100 },
  { prop: 'shippedQuantity', label: '已发货数量', visible: true, minWidth: 120 },
  { prop: 'acceptedQuantity', label: '已验收数量', visible: true, minWidth: 120 },
  { prop: 'cancelledQuantity', label: '已取消数量', visible: true, minWidth: 120 },
  { prop: 'logisticsStatus', label: '物流状态', visible: true, minWidth: 100 },
  { prop: 'unitPrice', label: '单价（¥）', visible: true, minWidth: 100 },
  { prop: 'totalPrice', label: '总价（¥）', visible: true, minWidth: 100 },
  // '所属订单号' and '所属订单状态' are intentionally omitted as per requirements
]);

const visibleOrderColumns = computed(() => availableOrderColumns.filter((col) => col.visible));
const visibleProductColumns = computed(() => availableProductColumns.filter((col) => col.visible));

// Mock data for order view
const orderData = ref([
  {
    id: 'ord1',
    poNo: 'PO20240501001',
    customer: '客户A',
    supplier: '供应商X',
    orderTime: '2024-05-01 10:00:00',
    endTime: '2024-05-10 18:00:00',
    orderStatus: 'pending_shipment',
    salesOrderNo: 'SO20240501A',
    productCount: 2,
    totalAmount: 205000.0,
    products: [
      {
        id: 'p1-1',
        productName: '高性能CPU',
        model: 'CPU-INTEL-I9',
        brand: 'Intel',
        category: '处理器',
        totalQuantity: 50,
        shippedQuantity: 15,
        acceptedQuantity: 15,
        logisticsStatus: '部分发货',
        cancelledQuantity: 5,
        unitPrice: 2500.0,
        totalPrice: 125000.0,
      },
      {
        id: 'p1-2',
        productName: '超高速SSD',
        model: 'SSD-SAM-2TB',
        brand: 'Samsung',
        category: '存储',
        totalQuantity: 20,
        shippedQuantity: 5,
        acceptedQuantity: 3,
        logisticsStatus: '待发货',
        cancelledQuantity: 2,
        unitPrice: 4000.0,
        totalPrice: 80000.0,
      },
    ],
  },
  {
    id: 'ord2',
    poNo: 'PO20240428002',
    customer: '客户B',
    supplier: '供应商Y',
    orderTime: '2024-04-28 14:30:00',
    endTime: '2024-05-05 12:00:00',
    orderStatus: 'shipped',
    salesOrderNo: 'SO20240428B',
    productCount: 1,
    totalAmount: 80000.0,
    products: [
      {
        id: 'p2-1',
        productName: '大容量内存条',
        model: 'MEM-KING-32G',
        brand: 'Kingston',
        category: '内存',
        totalQuantity: 100,
        shippedQuantity: 30,
        acceptedQuantity: 20,
        logisticsStatus: '全部发货',
        cancelledQuantity: 10,
        unitPrice: 800.0,
        totalPrice: 80000.0,
      },
    ],
  },
  {
    id: 'ord3',
    poNo: 'PO20240502003',
    customer: '客户C',
    supplier: '供应商Z',
    orderTime: '2024-05-02 09:00:00',
    endTime: '2024-05-12 17:00:00',
    orderStatus: 'pending_confirmation',
    salesOrderNo: 'SO20240502C',
    productCount: 1,
    totalAmount: 5000.0,
    products: [
      {
        id: 'p3-1',
        productName: '标准键盘',
        model: 'KBD-LOGI-K120',
        brand: 'Logitech',
        category: '外设',
        totalQuantity: 10,
        shippedQuantity: 0,
        acceptedQuantity: 0,
        logisticsStatus: '未发货',
        cancelledQuantity: 0,
        unitPrice: 500.0,
        totalPrice: 5000.0,
      },
    ],
  },
  // Add more mock orders as needed
]);

const handleExpandChange = (row, expandedRows) => {
  console.log('Expanded order:', row, 'All expanded rows:', expandedRows);
  // Potentially load product details dynamically here if not already loaded
};

const handleProductSelectionChange = (selection, orderRow) => {
  selectedOrderProducts.value[orderRow.id] = selection;
  console.log(`Selected products for order ${orderRow.poNo}:`, selection);
};

const handleBatchCommand = (command) => {
  if (command === 'confirm') {
    // Handle batch confirm logic
    console.log('Batch confirm clicked');
    // You'll need to identify which orders to confirm, likely using a selection mechanism
    // For now, let's assume we operate on selectedOrderProducts or add a new selection for orders
  }
};

const handleViewProduct = (product) => {
  console.log('View product details:', product);
  // Implement navigation or modal for product details
};

const handleShipProduct = (product, order) => {
  console.log('Shipping product:', product, 'from order:', order);
  // Implement shipping logic
};

const handleViewOrderDetails = (order) => {
  console.log('View order details:', order);
  // Implement navigation or modal for order details
};

const handleConfirmOrder = (order) => {
  console.log('Confirm order:', order);
  // Implement order confirmation logic, e.g., change status
  // order.orderStatus = 'pending_payment'; // Example: change status after confirmation
};

const handleCancelProduct = (product, order) => {
  console.log('Cancelling product:', product, 'from order:', order);
  // Implement product cancellation logic
};

const handleCancelOrder = (order) => {
  console.log('Cancel order:', order);
  // Implement order cancellation logic
};

// Get order status label (copied for simplicity)
const getOrderStatusLabel = (status) => {
  const statusMap = {
    pending_payment: '待付款',
    pending_shipment: '待发货',
    pending_confirmation: '待确认',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消',
  };
  return statusMap[status] || status;
};

// Get order status type (copied for simplicity)
const getOrderStatusType = (status) => {
  const typeMap = {
    pending_payment: 'warning',
    pending_shipment: 'info',
    pending_confirmation: 'primary',
    shipped: '', // Element Plus default for primary-like or plain
    completed: 'success',
    cancelled: 'danger',
  };
  return typeMap[status] || 'default';
};
</script>

<style scoped>
/* Add any specific styles for OrderView here */
.el-table__expanded-cell {
  padding: 0px !important;
}
</style>
