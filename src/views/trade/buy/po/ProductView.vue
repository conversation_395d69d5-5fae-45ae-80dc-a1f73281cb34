<template>
  <div>
    <!-- Button container -->
    <div style="display: flex; justify-content: space-between; margin-bottom: 16px;">
      <!-- Bulk actions dropdown -->
      <el-dropdown @command="handleBulkCommand">
        <el-button type="primary">
          批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="shipSelected">送货</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <!-- Column configuration button -->
      <el-button @click="drawer = true" type="primary">
        配置列
      </el-button>
    </div>

    <el-drawer
      title="配置列"
      v-model="drawer"
      direction="rtl"
      size="300px">
      <!-- Add checkboxes for each column -->
      <div v-for="column in availableColumns" :key="column.prop" style="padding: 0 20px;">
        <el-checkbox v-model="column.visible">{{ column.label }}</el-checkbox>
      </div>
    </el-drawer>

    <el-table :data="productData" style="width: 100%" border @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <template v-for="column in visibleColumns" :key="column.prop">
        <el-table-column
          v-if="column.visible"
          :prop="column.prop"
          :label="column.label"
          :min-width="column.minWidth"
        >
          <template v-if="column.prop === 'unitPrice' || column.prop === 'totalPrice'" #default="scope">
            ¥{{ formatPrice(scope.row[column.prop]) }}
          </template>
          <template v-else-if="column.prop === 'orderStatus'" #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
              {{ getOrderStatusLabel(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" min-width="180" fixed="right">
        <template #default="scope">
          <el-button size="small" type="primary" link @click="handleViewOrder(scope.row)">查看订单</el-button>
          <el-button
            v-if="scope.row.pendingStockQuantity > 0"
            size="small"
            type="primary"
            link
            @click="handleShipProduct(scope.row)"
            style="margin-left: 5px;"
          >
            送货
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- Add DeliveryModal component -->
    <DeliveryModal
      v-model:visible="deliveryModalVisible"
      :products-to-ship="productsForDelivery"
      @confirm-shipment="handleConfirmShipmentLogic"
    />

    <!-- Add pagination if needed -->
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import DeliveryModal from './components/DeliveryModal.vue'; // Import the modal

const drawer = ref(false);
const selectedProducts = ref([]);
const deliveryModalVisible = ref(false); // For DeliveryModal visibility
const productsForDelivery = ref([]); // Products to pass to DeliveryModal

const availableColumns = reactive([
  { prop: 'productName', label: '商品名称', visible: true, minWidth: 150 },
  { prop: 'model', label: '型号', visible: true, minWidth: 120 },
  { prop: 'brand', label: '品牌', visible: true, minWidth: 100 },
  { prop: 'category', label: '分类', visible: true, minWidth: 100 },
  { prop: 'customer', label: '客户', visible: true, minWidth: 120 },
  { prop: 'totalQuantity', label: '总数量', visible: true, minWidth: 100 },
  { prop: 'pendingStockQuantity', label: '备货中数量', visible: true, minWidth: 120 },
  { prop: 'inTransitQuantity', label: '在途数量', visible: true, minWidth: 100 },
  { prop: 'acceptedQuantity', label: '已验收数量', visible: true, minWidth: 120 },
  { prop: 'cancelledQuantity', label: '已取消数量', visible: true, minWidth: 120 },
  { prop: 'unitPrice', label: '单价（¥）', visible: true, minWidth: 100 },
  { prop: 'totalPrice', label: '总价（¥）', visible: true, minWidth: 100 },
  { prop: 'poNo', label: '所属PO号', visible: true, minWidth: 150 },
  { prop: 'orderStatus', label: '所属订单状态', visible: true, minWidth: 120 },
]);

const visibleColumns = computed(() => availableColumns.filter(col => col.visible));

// Mock data for product view
const productData = ref([
  {
    id: 'p1',
    productName: '高性能CPU',
    model: 'CPU-INTEL-I9',
    brand: 'Intel',
    category: '处理器',
    customer: '客户A',
    totalQuantity: 50,
    pendingStockQuantity: 10,
    inTransitQuantity: 20,
    acceptedQuantity: 15,
    cancelledQuantity: 5,
    unitPrice: 2500.00,
    totalPrice: 125000.00,
    poNo: 'PO20240501001',
    orderStatus: 'pending_shipment', // Changed for testing
  },
  {
    id: 'p2',
    productName: '大容量内存条',
    model: 'MEM-KING-32G',
    brand: 'Kingston',
    category: '内存',
    customer: '客户B',
    totalQuantity: 100,
    pendingStockQuantity: 0, // Changed for testing
    inTransitQuantity: 40,
    acceptedQuantity: 20,
    cancelledQuantity: 10,
    unitPrice: 800.00,
    totalPrice: 80000.00,
    poNo: 'PO20240428002',
    orderStatus: 'shipped',
  },
  // Add more mock products as needed
]);

const handleSelectionChange = (selection) => {
  selectedProducts.value = selection;
  console.log('Selected products:', selection);
};

const handleViewOrder = (row) => {
  console.log('View order details for:', row.poNo);
  // Implement navigation or modal display for order details
};

const handleShipProduct = (row) => {
  console.log('Shipping product:', row);
  if (row.pendingStockQuantity > 0) {
    productsForDelivery.value = [row];
    deliveryModalVisible.value = true;
  } else {
    // Consider using ElMessage for user feedback
    console.warn('Product has no pending stock to ship.');
    // Optionally, import and use ElMessage
    // import { ElMessage } from 'element-plus';
    // ElMessage.warning('该商品没有待发货库存。');
  }
};

const handleBulkCommand = (command) => {
  if (command === 'shipSelected') {
    if (selectedProducts.value.length === 0) {
      console.warn('No products selected for bulk shipping.');
      // ElMessage.warning('请至少选择一个商品进行批量发货。');
      return;
    }

    const shippableProducts = selectedProducts.value.filter(p => p.pendingStockQuantity > 0);

    if (shippableProducts.length === 0) {
      console.warn('None of the selected products have pending stock for shipping.');
      // ElMessage.warning('所选商品均无待发货库存。');
      return;
    }

    productsForDelivery.value = shippableProducts;
    deliveryModalVisible.value = true;
    console.log('Bulk shipping selected shippable products:', shippableProducts);
  }
};

const handleConfirmShipmentLogic = (shipmentData) => {
  console.log('Shipment confirmed in ProductView:', shipmentData);
  // Here you would typically:
  // 1. Make an API call to your backend to record the shipment
  // 2. Update local data (e.g., productData quantities) if necessary
  // 3. Provide user feedback (e.g., success/error messages)

  // Example: Update pendingStockQuantity for shipped items (rudimentary)
  shipmentData.items.forEach(shippedItem => {
    const productInTable = productData.value.find(p => p.id === shippedItem.productId);
    if (productInTable) {
      productInTable.pendingStockQuantity -= shippedItem.shippedQuantity;
      // You might also want to update inTransitQuantity, etc.
      // productInTable.inTransitQuantity += shippedItem.shippedQuantity; 
    }
  });
  // Note: This local update is for demonstration. 
  // A real app would likely refetch data or receive updated data from the API response.
};

const formatPrice = (price) => {
  if (price === null || price === undefined) return '0.00';
  return price.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// Get order status label (copied from TradeOrder.vue for simplicity, ideally import or use a utility)
const getOrderStatusLabel = (status) => {
  const statusMap = {
    'pending_payment': '待付款',
    'pending_shipment': '待发货',
    'shipped': '已发货',
    'completed': '已完成',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
};

// Get order status type (copied from TradeOrder.vue for simplicity, ideally import or use a utility)
const getOrderStatusType = (status) => {
  const typeMap = {
    'pending_payment': 'warning',
    'pending_shipment': 'info',
    'shipped': '', // Element Plus default for primary-like or plain
    'completed': 'success',
    'cancelled': 'danger'
  };
  return typeMap[status] || 'default'; // Ensure a fallback
};

</script>

<style scoped>
/* Add any specific styles for ProductView here */
</style> 