<template>
  <el-dialog
    :model-value="visible"
    title="生成送货单"
    width="70%"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form ref="deliveryFormRef" :model="deliveryForm" label-width="120px">
      <el-divider content-position="left"> 客户信息 </el-divider>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="客户名称">{{ customerInfoData.name }}</el-descriptions-item>
        <el-descriptions-item label="联系方式">{{ customerInfoData.contact }}</el-descriptions-item>
        <el-descriptions-item label="收货地址" :span="2">{{ customerInfoData.address }}</el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="left"> 商品列表 </el-divider>

      <el-table :data="shipmentItems" style="width: 100%" border>
        <el-table-column prop="productName" label="商品名称" min-width="150"></el-table-column>
        <el-table-column prop="model" label="型号" min-width="120"></el-table-column>
        <el-table-column prop="brand" label="品牌" min-width="100"></el-table-column>
        <el-table-column prop="category" label="分类" min-width="100"></el-table-column>
        <el-table-column prop="unitPrice" label="单价" min-width="100">
          <template #default="scope">
            {{ scope.row.unitPrice != null ? `¥${scope.row.unitPrice.toFixed(2)}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="orderId" label="所属订单号" min-width="120"></el-table-column>
        <el-table-column prop="pendingStockQuantity" label="可发货数量" min-width="100">
          <template #default="scope">
            {{ scope.row.pendingStockQuantity }}
          </template>
        </el-table-column>
        <el-table-column label="本次发货数量" min-width="150">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.quantityToShip"
              :min="0"
              :max="scope.row.pendingStockQuantity"
              size="small"
              @change="handleQuantityChange(scope.row)"
            ></el-input-number>
          </template>
        </el-table-column>
      </el-table>

      <div style="margin-top: 20px; text-align: right;">
        <strong>总发货商品种类: {{ totalProductTypes }}</strong> |
        <strong>总发货数量: {{ totalShipmentQuantity }}</strong>
      </div>

      <el-divider content-position="left"> 其他信息 </el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="送货日期" prop="deliveryDate">
            <el-date-picker
              v-model="deliveryForm.deliveryDate"
              type="date"
              placeholder="选择送货日期"
              style="width: 100%;"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="备注" prop="notes">
        <el-input type="textarea" v-model="deliveryForm.notes" placeholder="请输入备注信息"></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirmShipment">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  productsToShip: {
    type: Array,
    // Example: [{ id: 'p1', productName: 'CPU', model: 'I9', brand: 'Intel', category: 'Electronics', unitPrice: 300, orderId: 'O123', pendingStockQuantity: 10 }]
    default: () => [],
  },
  initialCustomerInfo: {
    type: Object,
    default: () => ({ name: '', contact: '', address: '' }),
  }
});

const emit = defineEmits(['update:visible', 'confirm-shipment']);

const deliveryFormRef = ref(null);
const deliveryForm = reactive({
  deliveryDate: null,
  notes: '',
});

const customerInfoData = reactive({
  name: '',
  contact: '',
  address: '',
});

const shipmentItems = ref([]);

// Watch for changes in initialCustomerInfo to update local state
watch(() => props.initialCustomerInfo, (newInfo) => {
  if (newInfo) {
    customerInfoData.name = newInfo.name || '';
    customerInfoData.contact = newInfo.contact || '';
    customerInfoData.address = newInfo.address || '';
  }
}, { immediate: true, deep: true });

// Initialize shipmentItems when productsToShip changes
watch(() => props.productsToShip, (newProducts) => {
  if (newProducts && newProducts.length > 0) {
    shipmentItems.value = newProducts.map(p => ({
      ...p,
      brand: p.brand || '',
      category: p.category || '',
      unitPrice: p.unitPrice != null ? p.unitPrice : null,
      orderId: p.orderId || '',
      quantityToShip: Math.min(p.pendingStockQuantity > 0 ? 1 : 0, p.pendingStockQuantity)
    }));
  } else {
    shipmentItems.value = [];
  }
  // Reset form for new set of products (excluding customer info which is handled by its own watch)
  if (deliveryFormRef.value) {
    // deliveryFormRef.value.resetFields(); // This would reset deliveryDate and notes.
    // Let's reset them manually to avoid issues if some fields are not props of el-form-item
  }
  deliveryForm.deliveryDate = null;
  deliveryForm.notes = '';

}, { immediate: true, deep: true });

const handleQuantityChange = (item) => {
  if (item.quantityToShip < 0) {
    item.quantityToShip = 0;
  }
  if (item.quantityToShip > item.pendingStockQuantity) {
    item.quantityToShip = item.pendingStockQuantity;
    ElMessage.warning(`发货数量不能超过可发货数量 ${item.pendingStockQuantity}`);
  }
};

const totalProductTypes = computed(() => {
  return shipmentItems.value.filter(item => item.quantityToShip > 0).length;
});

const totalShipmentQuantity = computed(() => {
  return shipmentItems.value.reduce((sum, item) => sum + (item.quantityToShip || 0), 0);
});

const handleClose = () => {
  emit('update:visible', false);
};

const handleConfirmShipment = async () => {
  if (!deliveryFormRef.value) return;

  // Basic validation
  if (totalShipmentQuantity.value === 0) {
    ElMessage.error('总发货数量不能为0，请至少为一个商品指定发货数量。');
    return;
  }
  if (!deliveryForm.deliveryDate) {
    ElMessage.error('请选择送货日期。');
    return;
  }
  if (!customerInfoData.name || !customerInfoData.address || !customerInfoData.contact) {
    ElMessage.error('请填写完整的客户信息。');
    return;
  }

  //   deliveryFormRef.value.validate((valid) => { // ElForm validation can be added here if rules are defined
  //     if (valid) {
        const shipmentData = {
          customerInfo: { ...customerInfoData },
          deliveryDate: deliveryForm.deliveryDate,
          notes: deliveryForm.notes,
          items: shipmentItems.value.filter(item => item.quantityToShip > 0).map(item => ({
            productId: item.id, // Assuming 'id' is the product identifier
            productName: item.productName,
            model: item.model,
            brand: item.brand,
            category: item.category,
            unitPrice: item.unitPrice,
            orderId: item.orderId,
            shippedQuantity: item.quantityToShip,
          })),
          totalItems: totalProductTypes.value,
          totalQuantity: totalShipmentQuantity.value,
        };
        console.log('Confirming shipment:', shipmentData);
        emit('confirm-shipment', shipmentData);
        handleClose();
        ElMessage.success('发货指令已发送');
//     } else {
//       console.log('error submit!!');
//       ElMessage.error('请检查表单输入。');
//       return false;
//     }
//   });
};

</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
/* Add any specific styles for DeliveryModal here */
</style>