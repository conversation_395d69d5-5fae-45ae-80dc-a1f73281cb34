<template>
  <el-card class="rfq-adjust-container">
    <!-- 搜索区 -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="客户">
          <el-input v-model="queryParams.customer" placeholder="请输入客户名称" clearable />
        </el-form-item>
        <el-form-item label="物料型号">
          <el-input v-model="queryParams.model" placeholder="请输入物料型号" clearable />
        </el-form-item>
        <el-form-item label="品牌">
          <el-input v-model="queryParams.brand" placeholder="请输入品牌" clearable />
        </el-form-item>
        <el-form-item label="物料分类">
          <el-input v-model="queryParams.category" placeholder="请输入物料分类" clearable />
        </el-form-item>
        <el-form-item label="询价单号">
          <el-input v-model="queryParams.rfqNo" placeholder="请输入询价单号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 按钮区 -->
    <div class="button-container">
      <div class="left-buttons">
        <el-button-group>
          <el-button type="primary" :icon="Upload">导入报价单</el-button>
          <el-button type="primary" @click="handleExportTemplate" :icon="Download"></el-button>
        </el-button-group>
        <el-button type="primary" @click="handleBatchAdjust" >批量调整</el-button>
      </div>
      <div class="right-buttons">
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="!hasSelection">确定</el-button>
      </div>
    </div>

    <!-- 统计区 -->
    <div class="statistics-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="选中条数" :value="selectedCount" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="供应商报价总计" :value="totalSupplierPrice" :precision="2" prefix="¥" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="销售价总计" :value="totalSalePrice" :precision="2" prefix="¥" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="平均利润率" :value="avgProfitRate" :precision="2" suffix="%" />
        </el-col>
      </el-row>
    </div>

    <!-- 表格区 -->
    <div class="table-container" v-loading="loading">
      <el-table :data="tableData" border @selection-change="handleSelectionChange" style="width: 100%">
        <el-table-column type="selection" width="55" fixed="left" />
        <el-table-column prop="customer" label="客户" width="120" />
        <el-table-column prop="productName" label="物料名称" width="150" show-overflow-tooltip />
        <el-table-column prop="model" label="型号" width="120" />
        <el-table-column prop="brand" label="品牌" width="100" />
        <el-table-column prop="category" label="物料分类" width="120" />
        <el-table-column prop="quantity" label="数量" width="80" align="right" />
        <el-table-column prop="supplierPrice" label="供应商报价（¥）" width="140" align="right">
          <template #default="scope">
            {{ scope.row.supplierPrice.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="deliveryTime" label="交期" width="100" />
        <el-table-column prop="salePrice" label="销售价（¥）" width="130" align="right">
          <template #default="scope">
            <el-input-number v-model="scope.row.salePrice" :min="0" :precision="2" size="small" @change="handleSalePriceChange(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="profitRate" label="利润率" width="100" align="right">
          <template #default="scope">
            <span :class="getProfitRateClass(scope.row.profitRate)"> {{ scope.row.profitRate.toFixed(2) }}% </span>
          </template>
        </el-table-column>
        <el-table-column prop="totalPrice" label="总价" width="120" align="right">
          <template #default="scope"> ¥{{ scope.row.totalPrice.toLocaleString() }} </template>
        </el-table-column>
        <el-table-column prop="rfqNo" label="询价单号" width="150" />
        <el-table-column prop="rfqTime" label="询价时间" width="160" />
        <el-table-column prop="updateTime" label="更新时间" width="160" />

        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <!-- <el-button link type="primary" size="small" @click="handleViewDetail(scope.row)"> 查看详情 </el-button> -->
            <el-button link type="primary" size="small" @click="handleSingleAdjust(scope.row)"> 调整 </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="queryParams.pageNum" v-model:page-size="queryParams.pageSize" :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 快速调整弹窗 -->
    <el-dialog v-model="quickAdjustVisible" :title="adjustMode === 'single' ? '单行价格调整' : '批量价格调整'" width="800px">
      <!-- 价格信息对比区域 -->
      <div class="price-comparison-container">
        <!-- 单行调整 -->
        <div v-if="adjustMode === 'single'">
          <h4 style="margin-bottom: 16px; color: #333; text-align: center;">价格调整对比</h4>
          <el-row :gutter="24">
            <!-- 当前价格信息 -->
            <el-col :span="12">
              <div class="current-price-section">
                <h5 class="section-title">当前价格</h5>
                <div class="price-item">
                  <span class="price-label">供应商报价:</span>
                  <span class="price-value">¥{{ currentAdjustRow.supplierPrice.toLocaleString() }}</span>
                </div>
                <div class="price-item">
                  <span class="price-label">销售价:</span>
                  <span class="price-value">¥{{ currentAdjustRow.salePrice.toLocaleString() }}</span>
                </div>
                <div class="price-item">
                  <span class="price-label">利润率:</span>
                  <span class="price-value" :class="getProfitRateClass(currentAdjustRow.profitRate)">
                    {{ currentAdjustRow.profitRate.toFixed(2) }}%
                  </span>
                </div>
                <div class="price-item">
                  <span class="price-label">总价:</span>
                  <span class="price-value">¥{{ (currentAdjustRow.salePrice * currentAdjustRow.quantity).toLocaleString() }}</span>
                </div>
              </div>
            </el-col>
            <!-- 调整后预览 -->
            <el-col :span="12">
              <div class="preview-price-section" :class="{ 'preview-active': adjustForm.value !== 0 }">
                <h5 class="section-title">调整后预览</h5>
                <template v-if="adjustForm.value !== 0">
                  <div class="price-item">
                    <span class="price-label">供应商报价:</span>
                    <span class="price-value">¥{{ currentAdjustRow.supplierPrice.toLocaleString() }}</span>
                  </div>
                  <div class="price-item">
                    <span class="price-label">销售价:</span>
                    <span class="price-value preview-value">¥{{ getPreviewSalePrice(currentAdjustRow).toLocaleString() }}</span>
                  </div>
                  <div class="price-item">
                    <span class="price-label">利润率:</span>
                    <span class="price-value preview-value" :class="getProfitRateClass(getPreviewProfitRate(currentAdjustRow))">
                      {{ getPreviewProfitRate(currentAdjustRow).toFixed(2) }}%
                    </span>
                  </div>
                  <div class="price-item">
                    <span class="price-label">总价:</span>
                    <span class="price-value preview-value">¥{{ getPreviewTotalPrice(currentAdjustRow).toLocaleString() }}</span>
                  </div>
                </template>
                <template v-else>
                  <div class="no-preview">
                    请设置调整值查看预览
                  </div>
                </template>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 批量调整 -->
        <div v-else-if="adjustMode === 'batch'">
          <h4 style="margin-bottom: 16px; color: #333; text-align: center;">批量调整对比 ({{ selectedRows.length }} 条记录)</h4>
          <el-row :gutter="24">
            <!-- 当前价格信息 -->
            <el-col :span="12">
              <div class="current-price-section">
                <h5 class="section-title">当前汇总</h5>
                <div class="price-item">
                  <span class="price-label">供应商报价总计:</span>
                  <span class="price-value">¥{{ totalSupplierPrice.toLocaleString() }}</span>
                </div>
                <div class="price-item">
                  <span class="price-label">销售价总计:</span>
                  <span class="price-value">¥{{ totalSalePrice.toLocaleString() }}</span>
                </div>
                <div class="price-item">
                  <span class="price-label">平均利润率:</span>
                  <span class="price-value" :class="getProfitRateClass(avgProfitRate)">
                    {{ avgProfitRate.toFixed(2) }}%
                  </span>
                </div>
                <div class="price-item">
                  <span class="price-label">总利润:</span>
                  <span class="price-value">¥{{ (totalSalePrice - totalSupplierPrice).toLocaleString() }}</span>
                </div>
              </div>
            </el-col>
            <!-- 调整后预览 -->
            <el-col :span="12">
              <div class="preview-price-section" :class="{ 'preview-active': adjustForm.value !== 0 }">
                <h5 class="section-title">调整后预览</h5>
                <template v-if="adjustForm.value !== 0">
                  <div class="price-item">
                    <span class="price-label">供应商报价总计:</span>
                    <span class="price-value">¥{{ totalSupplierPrice.toLocaleString() }}</span>
                  </div>
                  <div class="price-item">
                    <span class="price-label">销售价总计:</span>
                    <span class="price-value preview-value">¥{{ getBatchPreviewSalePrice().toLocaleString() }}</span>
                  </div>
                  <div class="price-item">
                    <span class="price-label">平均利润率:</span>
                    <span class="price-value preview-value" :class="getProfitRateClass(getBatchPreviewAvgProfitRate())">
                      {{ getBatchPreviewAvgProfitRate().toFixed(2) }}%
                    </span>
                  </div>
                  <div class="price-item">
                    <span class="price-label">利润变化:</span>
                    <span class="price-value preview-value" :style="{ color: getBatchPreviewProfitChange() >= 0 ? '#67c23a' : '#f56c6c' }">
                      {{ getBatchPreviewProfitChange() >= 0 ? '+' : '' }}¥{{ getBatchPreviewProfitChange().toLocaleString() }}
                    </span>
                  </div>
                </template>
                <template v-else>
                  <div class="no-preview">
                    请设置调整值查看预览
                  </div>
                </template>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <el-divider />

      <el-form :model="adjustForm" label-width="120px">
        <el-form-item label="调整方式">
          <el-radio-group v-model="adjustForm.type">
            <el-radio label="salePrice">{{ adjustForm.type === 'batch' ? '销售总价' :'销售价' }}</el-radio>
            <el-radio label="profitRate">按利润率</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="getAdjustLabel()">
          <el-input-number v-model="adjustForm.value" :min="0" :precision="2" />
          <span style="margin-left: 10px; color: #666">
            {{ adjustForm.type === 'profitRate' ? '%' : '元' }}
          </span>
        </el-form-item>
      </el-form>
      <div v-if="adjustForm.type === 'salePrice' && adjustMode === 'batch'" style="color: #f56c6c; margin-left:50px; margin-top: 10px; width: 400px;">
        ⚠️ 按销售总价批量调整时，销售总价的变化将等比例分摊到每个选中的物料上。
      </div>
      <!-- <div v-else-if="adjustForm.type === 'salePrice' && adjustMode === 'single'" style="color: #f56c6c; margin-left:50px; margin-top: 10px; width: 400px;">
        ⚠️ 按销售价调整时，将直接设置该物料的销售价。
      </div> -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseAdjustDialog">取消</el-button>
          <el-button type="primary" @click="handleQuickAdjustConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Upload, Download } from '@element-plus/icons-vue';

// 查询参数
const queryParams = reactive({
  customer: '',
  model: '',
  brand: '',
  category: '',
  rfqNo: '',
  pageNum: 1,
  pageSize: 20,
});

// 响应式数据
const loading = ref(false);
const total = ref(0);
const tableData = ref([]);
const selectedRows = ref([]);
const quickAdjustVisible = ref(false);
const currentAdjustRow = ref(null);

// 快速调整表单
const adjustForm = reactive({
  type: 'salePrice', // salePrice, profitRate
  value: 0,
  scope: 'current', // current, selected, all
});

// 调整模式标识
const adjustMode = ref(''); // 'single' 或 'batch'

// 模拟数据
const mockData = [
  {
    id: 1,
    customer: '深圳科技有限公司',
    productName: '电阻器 1K欧姆',
    model: 'RES-1K-001',
    brand: '村田',
    category: '电阻',
    quantity: 1000,
    supplierPrice: 0.15,
    deliveryTime: '7天',
    salePrice: 0.2,
    rfqNo: 'RFQ-2025-001',
    rfqTime: '2025-05-25 10:30:00',
    updateTime: '2025-05-26 10:30:00',
  },
  {
    id: 2,
    customer: '上海电子股份公司',
    productName: '电容器 100uF',
    model: 'CAP-100UF-002',
    brand: 'TDK',
    category: '电容',
    quantity: 500,
    supplierPrice: 0.8,
    deliveryTime: '10天',
    salePrice: 1.1,
    rfqNo: 'RFQ-2025-002',
    rfqTime: '2025-05-26 14:20:00',
    updateTime: '2025-05-26 10:30:00',
  },
  {
    id: 3,
    customer: '北京智能制造有限公司',
    productName: 'MCU芯片 STM32F103',
    model: 'STM32F103C8T6',
    brand: 'ST',
    category: '芯片',
    quantity: 200,
    supplierPrice: 12.5,
    deliveryTime: '15天',
    salePrice: 16.0,
    rfqNo: 'RFQ-2025-003',
    rfqTime: '2025-05-27 09:15:00',
    updateTime: '2025-05-26 10:30:00',
  },
];

// 计算属性
const selectedCount = computed(() => selectedRows.value.length);

const totalSupplierPrice = computed(() => {
  return selectedRows.value.reduce((sum, row) => sum + row.supplierPrice * row.quantity, 0);
});

const totalSalePrice = computed(() => {
  return selectedRows.value.reduce((sum, row) => sum + row.salePrice * row.quantity, 0);
});

const totalAmount = computed(() => {
  return selectedRows.value.reduce((sum, row) => sum + row.salePrice * row.quantity, 0);
});

const totalProfitRate = computed(() => {
  if (totalSupplierPrice.value === 0) return 0;
  return ((totalSalePrice.value - totalSupplierPrice.value) / totalSupplierPrice.value) * 100;
});

const avgProfitRate = computed(() => {
  if (selectedRows.value.length === 0) return 0;
  const totalRate = selectedRows.value.reduce((sum, row) => sum + row.profitRate, 0);
  return totalRate / selectedRows.value.length;
});

const hasSelection = computed(() => selectedRows.value.length > 0);

// 计算利润率和总价
const calculateRowData = (row) => {
  row.profitRate = row.supplierPrice > 0 ? ((row.salePrice - row.supplierPrice) / row.supplierPrice) * 100 : 0;
  row.totalPrice = row.salePrice * row.quantity;
};

// 获取利润率样式类
const getProfitRateClass = (rate) => {
  if (rate < 10) return 'profit-low';
  if (rate < 20) return 'profit-normal';
  return 'profit-high';
};

// 获取调整标签
const getAdjustLabel = () => {
  if (adjustForm.type === 'salePrice') {
    return adjustMode.value === 'batch' ? '销售总价' : '销售价';
  } else {
    return '利润率';
  }
};

// 预览计算方法
const getPreviewSalePrice = (row) => {
  const { type, value } = adjustForm;
  if (type === 'salePrice') {
    return value;
  } else if (type === 'profitRate') {
    return row.supplierPrice * (1 + value / 100);
  }
  return row.salePrice;
};

const getPreviewProfitRate = (row) => {
  const newSalePrice = getPreviewSalePrice(row);
  return row.supplierPrice > 0 ? ((newSalePrice - row.supplierPrice) / row.supplierPrice) * 100 : 0;
};

const getPreviewTotalPrice = (row) => {
  return getPreviewSalePrice(row) * row.quantity;
};

const getBatchPreviewSalePrice = () => {
  const { type, value } = adjustForm;
  if (type === 'salePrice') {
    // 按销售总价调整
    return value;
  } else if (type === 'profitRate') {
    // 按利润率调整，计算新的销售总价
    return selectedRows.value.reduce((sum, row) => {
      const newSalePrice = row.supplierPrice * (1 + value / 100);
      return sum + newSalePrice * row.quantity;
    }, 0);
  }
  return totalSalePrice.value;
};

const getBatchPreviewAvgProfitRate = () => {
  if (selectedRows.value.length === 0) return 0;
  const { type, value } = adjustForm;
  
  if (type === 'salePrice') {
    // 按销售总价调整时，需要计算每个物料调整后的利润率
    const newTotalSalePrice = value;
    const currentTotalSalePrice = totalSalePrice.value;
    
    if (currentTotalSalePrice === 0) return 0;
    
    // 等比例调整每个物料的销售价
    let totalProfitRate = 0;
    selectedRows.value.forEach(row => {
      const currentItemTotal = row.salePrice * row.quantity;
      const proportion = currentItemTotal / currentTotalSalePrice;
      const newItemTotal = newTotalSalePrice * proportion;
      const newSalePrice = newItemTotal / row.quantity;
      const newProfitRate = row.supplierPrice > 0 ? ((newSalePrice - row.supplierPrice) / row.supplierPrice) * 100 : 0;
      totalProfitRate += newProfitRate;
    });
    
    return totalProfitRate / selectedRows.value.length;
  } else if (type === 'profitRate') {
    // 按利润率调整时，所有物料都是相同的利润率
    return value;
  }
  
  return avgProfitRate.value;
};

const getBatchPreviewProfitChange = () => {
  const currentProfit = totalSalePrice.value - totalSupplierPrice.value;
  const newProfit = getBatchPreviewSalePrice() - totalSupplierPrice.value;
  return newProfit - currentProfit;
};

// 查询方法
const handleQuery = () => {
  loading.value = true;
  queryParams.pageNum = 1;

  // 模拟API调用
  setTimeout(() => {
    // 这里应该调用实际的API
    tableData.value = mockData.map((item) => {
      const row = { ...item };
      calculateRowData(row);
      return row;
    });
    total.value = mockData.length;
    loading.value = false;
  }, 300);
};

// 重置查询
const resetQuery = () => {
  queryParams.customer = '';
  queryParams.model = '';
  queryParams.brand = '';
  queryParams.category = '';
  queryParams.rfqNo = '';
  handleQuery();
};

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 销售价变化
const handleSalePriceChange = (row) => {
  calculateRowData(row);
  ElMessage.success('销售价已更新');
};

// 导入报价单
const handleImportQuote = (file) => {
  // 这里处理文件上传逻辑
  ElMessage.info('正在处理文件上传...');
  return false; // 阻止默认上传行为
};

// 下载模板
const handleExportTemplate = () => {
  ElMessage.info('正在下载模板文件...');
};

// 重置按钮
const handleReset = () => {
  ElMessageBox.confirm('确定要重置所有调整吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    tableData.value.forEach((row) => {
      // 重置为原始价格或其他逻辑
      calculateRowData(row);
    });
    ElMessage.success('已重置');
  });
};

// 确定按钮
const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要确认的数据');
    return;
  }

  ElMessageBox.confirm(`确定要确认选中的 ${selectedRows.value.length} 条数据吗？`, '确认调整', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info',
  }).then(() => {
    // 这里处理确认逻辑
    ElMessage.success('报价调整已确认');
  });
};

// 查看详情
const handleViewDetail = (row) => {
  ElMessage.info(`查看 ${row.productName} 的详细信息`);
};

// 单行调整
const handleSingleAdjust = (row) => {
  currentAdjustRow.value = row;
  adjustMode.value = 'single';
  adjustForm.scope = 'current';
  adjustForm.type = 'salePrice';
  adjustForm.value = 0;
  quickAdjustVisible.value = true;
};

// 批量调整
const handleBatchAdjust = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要调整的数据');
    return;
  }
  currentAdjustRow.value = null;
  adjustMode.value = 'batch';
  adjustForm.scope = 'selected';
  adjustForm.type = 'salePrice';
  adjustForm.value = 0;
  quickAdjustVisible.value = true;
};

// 快速调整（保留兼容性）
const handleQuickAdjust = (row) => {
  if (row) {
    handleSingleAdjust(row);
  } else {
    handleBatchAdjust();
  }
};

// 关闭调整弹窗
const handleCloseAdjustDialog = () => {
  quickAdjustVisible.value = false;
  adjustMode.value = '';
  currentAdjustRow.value = null;
};

// 快速调整确认
const handleQuickAdjustConfirm = () => {
  const { type, value } = adjustForm;
  let targetRows = [];

  if (adjustMode.value === 'single') {
    targetRows = [currentAdjustRow.value];
  } else if (adjustMode.value === 'batch') {
    targetRows = selectedRows.value;
  }

  if (targetRows.length === 0) {
    ElMessage.warning('没有可调整的数据');
    return;
  }

  if (adjustMode.value === 'batch' && type === 'salePrice') {
    // 批量调整销售总价 - 等比例分摊
    const newTotalSalePrice = value;
    const currentTotalSalePrice = totalSalePrice.value;
    
    if (currentTotalSalePrice === 0) {
      ElMessage.warning('当前销售总价为0，无法进行等比例调整');
      return;
    }
    
    targetRows.forEach((row) => {
      const currentItemTotal = row.salePrice * row.quantity;
      const proportion = currentItemTotal / currentTotalSalePrice;
      const newItemTotal = newTotalSalePrice * proportion;
      row.salePrice = newItemTotal / row.quantity;
      calculateRowData(row);
    });
  } else {
    // 单行调整或按利润率调整
    targetRows.forEach((row) => {
      if (type === 'salePrice') {
        row.salePrice = value;
      } else if (type === 'profitRate') {
        row.salePrice = row.supplierPrice * (1 + value / 100);
      }
      calculateRowData(row);
    });
  }

  quickAdjustVisible.value = false;
  adjustMode.value = '';
  ElMessage.success(`已调整 ${targetRows.length} 条数据`);
};

// 分页处理
const handleSizeChange = (val) => {
  queryParams.pageSize = val;
  handleQuery();
};

const handleCurrentChange = (val) => {
  queryParams.pageNum = val;
  handleQuery();
};

// 初始化
onMounted(() => {
  handleQuery();
});
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: #fff;
}

.left-buttons,
.right-buttons {
  display: flex;
  gap: 10px;
}

.statistics-container {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.table-container {
  background-color: #fff;
  border-radius: 6px;
  overflow: hidden;
}

.profit-low {
  color: #f56c6c;
  font-weight: bold;
}

.profit-normal {
  color: #e6a23c;
  font-weight: bold;
}

.profit-high {
  color: #67c23a;
  font-weight: bold;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.upload-demo {
  display: inline-block;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button-container {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .left-buttons,
  .right-buttons {
    justify-content: center;
  }

  .statistics-container :deep(.el-row) {
    flex-direction: column;
  }
}

/* 表格优化 */
.el-table {
  font-size: 14px;
}

.el-table .el-table__header th {
  background-color: #f8f9fa;
  color: #333;
  font-weight: 600;
}

.el-table .el-table__row:hover {
  background-color: #f0f9ff;
}

/* 输入框样式优化 */
.el-input-number {
  width: 100px;
}

/* 弹窗样式 */
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 价格对比区域样式 */
.price-comparison-container {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.current-price-section,
.preview-price-section {
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  background-color: #fff;
  min-height: 200px;
}

.current-price-section {
  border-color: #d0d7de;
}

.preview-price-section {
  background-color: #f8fafc;
  border-color: #c3d9ff;
}

.preview-price-section.preview-active {
  background-color: #f0f9ff;
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.section-title {
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  border-bottom: 1px solid #e4e7ed;
  color: #333;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f2f3f5;
}

.price-item:last-child {
  border-bottom: none;
}

.price-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.price-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.preview-value {
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

.no-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120px;
  color: #999;
  font-style: italic;
  background-color: #f9f9f9;
  border: 2px dashed #ddd;
  border-radius: 6px;
}

.placeholder-text {
  font-size: 14px;
}
</style>
