<template>
  <el-card class="rfq-list-container">
    <!-- 搜索区 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="客户">
          <el-input v-model="searchForm.customer" placeholder="请输入客户名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="物料型号">
          <el-input v-model="searchForm.materialModel" placeholder="请输入物料型号" clearable></el-input>
        </el-form-item>
        <el-form-item label="品牌">
          <el-input v-model="searchForm.brand" placeholder="请输入品牌" clearable></el-input>
        </el-form-item>
        <el-form-item label="物料分类">
          <el-select v-model="searchForm.materialCategory" placeholder="请选择物料分类" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="电子元器件" value="electronics"></el-option>
            <el-option label="机械配件" value="mechanical"></el-option>
            <el-option label="化工材料" value="chemical"></el-option>
            <el-option label="其他" value="other"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="询价单号">
          <el-input v-model="searchForm.rfqNumber" placeholder="请输入询价单号" clearable></el-input>
        </el-form-item>
        <el-form-item label="询价状态">
          <el-select v-model="searchForm.inquiryStatus" placeholder="请选择询价状态" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="待报价" value="pending"></el-option>
            <el-option label="报价中" value="quoting"></el-option>
            <el-option label="已完成" value="completed"></el-option>
            <el-option label="已过期" value="expired"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="询价时间">
          <el-date-picker
            v-model="searchForm.inquiryDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="截止时间">
          <el-date-picker
            v-model="searchForm.deadline"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleResetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区 -->
    <div class="table-content-container">
      <el-table 
        :data="filteredRfqData" 
        style="width: 100%" 
        border
        :expand-row-keys="expandedRows"
        @expand-change="handleExpandChange"
        row-key="id"
      >
        <el-table-column type="expand" width="50">
          <template #default="{ row }">
            <div class="inner-table-container">
              <el-table :data="row.supplierQuotes" border size="small">
                <el-table-column prop="supplierName" label="供应商" min-width="120"></el-table-column>
                <el-table-column prop="supplierPrice" label="供应商报价（¥）" min-width="120" align="right">
                  <template #default="scope">
                    {{ formatPrice(scope.row.supplierPrice) }}
                  </template>
                </el-table-column>
                <el-table-column prop="salesPrice" label="销售价（¥）" min-width="100" align="right">
                  <template #default="scope">
                    {{ formatPrice(scope.row.salesPrice) }}
                  </template>
                </el-table-column>
                <el-table-column prop="totalPrice" label="总价（¥）" min-width="100" align="right">
                  <template #default="scope">
                    {{ formatPrice(scope.row.totalPrice) }}
                  </template>
                </el-table-column>
                <el-table-column prop="minOrderQuantity" label="最小起订量" min-width="100" align="center"></el-table-column>
                <el-table-column prop="promisedDelivery" label="承诺交期" min-width="120" align="center"></el-table-column>
                <el-table-column prop="quoteTime" label="报价时间" min-width="140">
                  <template #default="scope">
                    {{ formatDateTime(scope.row.quoteTime) }}
                  </template>
                </el-table-column>
                <el-table-column prop="quoteStatus" label="报价状态" min-width="100" align="center">
                  <template #default="scope">
                    <el-tag :type="getQuoteStatusTagType(scope.row.quoteStatus)">
                      {{ formatQuoteStatus(scope.row.quoteStatus) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="remarks" label="备注" min-width="150" show-overflow-tooltip></el-table-column>
                <!-- <el-table-column label="操作" min-width="120" align="center">
                  <template #default="scope">
                    <el-button type="primary" link size="small" @click="handleSelectQuote(row, scope.row)">
                      选择
                    </el-button>
                    <el-button type="info" link size="small" @click="handleViewQuoteDetail(scope.row)">
                      详情
                    </el-button>
                  </template>
                </el-table-column> -->
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="customer" label="客户" min-width="120"></el-table-column>
        <el-table-column prop="materialName" label="物料名称" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="model" label="型号" min-width="120"></el-table-column>
        <el-table-column prop="inquiryStatus" label="询价状态" min-width="100" align="center">
          <template #default="scope">
            <el-tag :type="getInquiryStatusTagType(scope.row.inquiryStatus)">
              {{ formatInquiryStatus(scope.row.inquiryStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="brand" label="品牌" min-width="100"></el-table-column>
        <el-table-column prop="materialCategory" label="物料分类" min-width="120"></el-table-column>
        <el-table-column prop="quantity" label="数量" min-width="80" align="center"></el-table-column>
        <el-table-column prop="bestSupplierPrice" label="供应商报价（¥）" min-width="130" align="right">
          <template #default="scope">
            {{ formatPrice(scope.row.bestSupplierPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="salesPrice" label="销售价（¥）" min-width="110" align="right">
          <template #default="scope">
            {{ formatPrice(scope.row.salesPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalPrice" label="总价（¥）" min-width="110" align="right">
          <template #default="scope">
            {{ formatPrice(scope.row.totalPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="minOrderQuantity" label="最小起订量" min-width="110" align="center"></el-table-column>
        <el-table-column prop="deliveryTime" label="交期" min-width="100" align="center"></el-table-column>
        <el-table-column prop="deadline" label="截止时间" min-width="140">
          <template #default="scope">
            {{ formatDateTime(scope.row.deadline) }}
          </template>
        </el-table-column>
        <el-table-column prop="rfqNumber" label="询价单号" min-width="140"></el-table-column>
        <el-table-column prop="inquiryTime" label="询价时间" min-width="140">
          <template #default="scope">
            {{ formatDateTime(scope.row.inquiryTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="180" align="center" fixed="right">
          <template #default="scope">
            <!-- <el-button type="primary" link size="small" @click="handleViewDetail(scope.row)">详情</el-button> -->
            <el-button type="primary" link size="small" @click="handleViewQuoteHistory(scope.row)">查看报价历史</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <el-pagination
        class="pagination-container"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalFilteredItems"
        @size-change="handlePageSizeChange"
        @current-change="handleCurrentPageChange"
      >
      </el-pagination>
    </div>

    <!-- 报价历史弹出框 -->
    <el-dialog
      v-model="quoteHistoryDialogVisible"
      title="报价历史"
      width="80%"
      top="5vh"
      :before-close="handleCloseQuoteHistory"
    >
      <div class="quote-history-container">
        <!-- 物料信息 -->
        <div class="material-info">
          <h4>物料信息：{{ currentMaterial.materialName }} - {{ currentMaterial.model }} ({{ currentMaterial.brand }})</h4>
        </div>

        <!-- 搜索区 -->
        <div class="history-filter-container">
          <el-form :inline="true" :model="historySearchForm" class="history-search-form">
            <el-form-item label="客户">
              <el-input v-model="historySearchForm.customer" placeholder="请输入客户名称" clearable></el-input>
            </el-form-item>
            <el-form-item label="供应商">
              <el-input v-model="historySearchForm.supplier" placeholder="请输入供应商名称" clearable></el-input>
            </el-form-item>
            <el-form-item label="报价时间">
              <el-date-picker
                v-model="historySearchForm.quoteDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleHistorySearch">搜索</el-button>
              <el-button @click="handleResetHistorySearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 报价历史表格 -->
        <div class="history-table-container">
          <el-table :data="filteredQuoteHistory" border style="width: 100%">
            <el-table-column prop="customer" label="客户" min-width="150"></el-table-column>
            <el-table-column prop="supplier" label="供应商" min-width="150"></el-table-column>
            <el-table-column prop="quantity" label="数量" min-width="80" align="center"></el-table-column>
            <el-table-column prop="supplierPrice" label="供应商报价（¥）" min-width="130" align="right">
              <template #default="scope">
                {{ formatPrice(scope.row.supplierPrice) }}
              </template>
            </el-table-column>
            <el-table-column prop="salesPrice" label="销售价（¥）" min-width="110" align="right">
              <template #default="scope">
                {{ formatPrice(scope.row.salesPrice) }}
              </template>
            </el-table-column>
            <el-table-column prop="totalPrice" label="总价（¥）" min-width="110" align="right">
              <template #default="scope">
                {{ formatPrice(scope.row.totalPrice) }}
              </template>
            </el-table-column>
            <el-table-column prop="supplierRemarks" label="供应商备注" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column prop="quoteTime" label="报价时间" min-width="140">
              <template #default="scope">
                {{ formatDateTime(scope.row.quoteTime) }}
              </template>
            </el-table-column>
          </el-table>
          
          <el-pagination
            class="history-pagination"
            :current-page="historyCurrentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="historyPageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalHistoryItems"
            @size-change="handleHistoryPageSizeChange"
            @current-change="handleHistoryCurrentPageChange"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
  </el-card>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 搜索表单
const searchForm = reactive({
  customer: '',
  materialModel: '',
  brand: '',
  materialCategory: '',
  rfqNumber: '',
  inquiryStatus: '',
  inquiryDate: [],
  deadline: [],
});

// 展开行状态
const expandedRows = ref([]);

// 分页
const currentPage = ref(1);
const pageSize = ref(10);

// 报价历史弹出框相关
const quoteHistoryDialogVisible = ref(false);
const currentMaterial = ref({});
const historyCurrentPage = ref(1);
const historyPageSize = ref(10);

// 报价历史搜索表单
const historySearchForm = reactive({
  customer: '',
  supplier: '',
  quoteDate: [],
});

// 所有报价历史数据（模拟数据）
const allQuoteHistory = ref([
  {
    id: 1,
    materialId: 1,
    customer: 'ABC科技有限公司',
    supplier: '供应商A',
    quantity: 100,
    supplierPrice: 2800.00,
    salesPrice: 3200.00,
    totalPrice: 320000.00,
    supplierRemarks: '现货充足，可立即发货，质保一年',
    quoteTime: '2024-07-29 14:30:00'
  },
  {
    id: 2,
    materialId: 1,
    customer: 'ABC科技有限公司',
    supplier: '供应商B',
    quantity: 100,
    supplierPrice: 2900.00,
    salesPrice: 3300.00,
    totalPrice: 330000.00,
    supplierRemarks: '质量保证，一年质保，可提供技术支持',
    quoteTime: '2024-07-29 16:20:00'
  },
  {
    id: 3,
    materialId: 1,
    customer: 'DEF电子公司',
    supplier: '供应商A',
    quantity: 50,
    supplierPrice: 2850.00,
    salesPrice: 3250.00,
    totalPrice: 162500.00,
    supplierRemarks: '小批量采购，价格略有上调',
    quoteTime: '2024-07-25 10:15:00'
  },
  {
    id: 4,
    materialId: 2,
    customer: 'XYZ电子公司',
    supplier: '供应商C',
    quantity: 50,
    supplierPrice: 1200.00,
    salesPrice: 1400.00,
    totalPrice: 70000.00,
    supplierRemarks: '需要确认库存，预计3天内发货',
    quoteTime: '2024-07-30 11:00:00'
  },
  {
    id: 5,
    materialId: 2,
    customer: 'GHI科技公司',
    supplier: '供应商D',
    quantity: 30,
    supplierPrice: 1180.00,
    salesPrice: 1380.00,
    totalPrice: 41400.00,
    supplierRemarks: '原厂正品，可提供质保证书',
    quoteTime: '2024-07-28 15:45:00'
  },
  {
    id: 6,
    materialId: 3,
    customer: '精密机械制造厂',
    supplier: '供应商D',
    quantity: 200,
    supplierPrice: 85.00,
    salesPrice: 100.00,
    totalPrice: 20000.00,
    supplierRemarks: '批量采购优惠价格，包装完好',
    quoteTime: '2024-07-28 09:45:00'
  },
  {
    id: 7,
    materialId: 3,
    customer: '精密机械制造厂',
    supplier: '供应商E',
    quantity: 200,
    supplierPrice: 88.00,
    salesPrice: 105.00,
    totalPrice: 21000.00,
    supplierRemarks: '进口原装，质量可靠',
    quoteTime: '2024-07-28 15:30:00'
  },
  {
    id: 8,
    materialId: 3,
    customer: 'JKL工业公司',
    supplier: '供应商F',
    quantity: 100,
    supplierPrice: 90.00,
    salesPrice: 108.00,
    totalPrice: 10800.00,
    supplierRemarks: '现货供应，可立即交付',
    quoteTime: '2024-07-26 13:20:00'
  }
]);

// 过滤后的报价历史数据
const filteredQuoteHistoryList = computed(() => {
  let filtered = allQuoteHistory.value.filter(item => item.materialId === currentMaterial.value.id);
  
  if (historySearchForm.customer) {
    filtered = filtered.filter(item => item.customer.includes(historySearchForm.customer));
  }
  if (historySearchForm.supplier) {
    filtered = filtered.filter(item => item.supplier.includes(historySearchForm.supplier));
  }
  
  // 报价时间过滤
  if (historySearchForm.quoteDate && historySearchForm.quoteDate.length === 2) {
    const [startDate, endDate] = historySearchForm.quoteDate;
    filtered = filtered.filter(item => {
      const itemDate = new Date(item.quoteTime);
      const adjustedEndDate = new Date(endDate);
      adjustedEndDate.setHours(23, 59, 59, 999);
      return itemDate >= new Date(startDate) && itemDate <= adjustedEndDate;
    });
  }
  
  return filtered;
});

const totalHistoryItems = computed(() => filteredQuoteHistoryList.value.length);

const filteredQuoteHistory = computed(() => {
  const start = (historyCurrentPage.value - 1) * historyPageSize.value;
  const end = start + historyPageSize.value;
  return filteredQuoteHistoryList.value.slice(start, end);
});

// 模拟询价单数据
const allRfqData = ref([
  {
    id: 1,
    customer: 'ABC科技有限公司',
    materialName: '英特尔处理器芯片',
    model: 'i7-13700K',
    inquiryStatus: 'quoting',
    brand: 'Intel',
    materialCategory: 'electronics',
    quantity: 100,
    bestSupplierPrice: 2800.00,
    salesPrice: 3200.00,
    totalPrice: 320000.00,
    minOrderQuantity: 10,
    deliveryTime: '7-10天',
    deadline: '2024-08-15 18:00:00',
    rfqNumber: 'RFQ202407001',
    inquiryTime: '2024-07-28 09:30:00',
    supplierQuotes: [
      {
        supplierName: '供应商A',
        supplierPrice: 2800.00,
        salesPrice: 3200.00,
        totalPrice: 320000.00,
        minOrderQuantity: 10,
        promisedDelivery: '7天',
        quoteTime: '2024-07-29 14:30:00',
        quoteStatus: 'active',
        remarks: '现货充足，可立即发货'
      },
      {
        supplierName: '供应商B',
        supplierPrice: 2900.00,
        salesPrice: 3300.00,
        totalPrice: 330000.00,
        minOrderQuantity: 5,
        promisedDelivery: '10天',
        quoteTime: '2024-07-29 16:20:00',
        quoteStatus: 'active',
        remarks: '质量保证，一年质保'
      }
    ]
  },
  {
    id: 2,
    customer: 'XYZ电子公司',
    materialName: '三星内存条',
    model: 'DDR5-4800 32GB',
    inquiryStatus: 'pending',
    brand: 'Samsung',
    materialCategory: 'electronics',
    quantity: 50,
    bestSupplierPrice: 1200.00,
    salesPrice: 1400.00,
    totalPrice: 70000.00,
    minOrderQuantity: 2,
    deliveryTime: '5-7天',
    deadline: '2024-08-20 17:00:00',
    rfqNumber: 'RFQ202407002',
    inquiryTime: '2024-07-29 10:15:00',
    supplierQuotes: [
      {
        supplierName: '供应商C',
        supplierPrice: 1200.00,
        salesPrice: 1400.00,
        totalPrice: 70000.00,
        minOrderQuantity: 2,
        promisedDelivery: '5天',
        quoteTime: '2024-07-30 11:00:00',
        quoteStatus: 'pending',
        remarks: '需要确认库存'
      }
    ]
  },
  {
    id: 3,
    customer: '精密机械制造厂',
    materialName: '精密轴承',
    model: 'SKF-6205',
    inquiryStatus: 'completed',
    brand: 'SKF',
    materialCategory: 'mechanical',
    quantity: 200,
    bestSupplierPrice: 85.00,
    salesPrice: 100.00,
    totalPrice: 20000.00,
    minOrderQuantity: 20,
    deliveryTime: '3-5天',
    deadline: '2024-08-10 16:00:00',
    rfqNumber: 'RFQ202407003',
    inquiryTime: '2024-07-27 14:20:00',
    supplierQuotes: [
      {
        supplierName: '供应商D',
        supplierPrice: 85.00,
        salesPrice: 100.00,
        totalPrice: 20000.00,
        minOrderQuantity: 20,
        promisedDelivery: '3天',
        quoteTime: '2024-07-28 09:45:00',
        quoteStatus: 'selected',
        remarks: '已选择此报价'
      },
      {
        supplierName: '供应商E',
        supplierPrice: 88.00,
        salesPrice: 105.00,
        totalPrice: 21000.00,
        minOrderQuantity: 15,
        promisedDelivery: '5天',
        quoteTime: '2024-07-28 15:30:00',
        quoteStatus: 'rejected',
        remarks: '价格偏高'
      }
    ]
  }
]);

// 过滤数据
const filteredRfqDataList = computed(() => {
  let filtered = allRfqData.value;
  
  if (searchForm.customer) {
    filtered = filtered.filter(item => item.customer.includes(searchForm.customer));
  }
  if (searchForm.materialModel) {
    filtered = filtered.filter(item => item.model.includes(searchForm.materialModel));
  }
  if (searchForm.brand) {
    filtered = filtered.filter(item => item.brand.includes(searchForm.brand));
  }
  if (searchForm.materialCategory) {
    filtered = filtered.filter(item => item.materialCategory === searchForm.materialCategory);
  }
  if (searchForm.rfqNumber) {
    filtered = filtered.filter(item => item.rfqNumber.includes(searchForm.rfqNumber));
  }
  if (searchForm.inquiryStatus) {
    filtered = filtered.filter(item => item.inquiryStatus === searchForm.inquiryStatus);
  }
  
  // 询价时间过滤
  if (searchForm.inquiryDate && searchForm.inquiryDate.length === 2) {
    const [startDate, endDate] = searchForm.inquiryDate;
    filtered = filtered.filter(item => {
      const itemDate = new Date(item.inquiryTime);
      const adjustedEndDate = new Date(endDate);
      adjustedEndDate.setHours(23, 59, 59, 999);
      return itemDate >= new Date(startDate) && itemDate <= adjustedEndDate;
    });
  }
  
  // 截止时间过滤
  if (searchForm.deadline && searchForm.deadline.length === 2) {
    const [startDate, endDate] = searchForm.deadline;
    filtered = filtered.filter(item => {
      const itemDate = new Date(item.deadline);
      const adjustedEndDate = new Date(endDate);
      adjustedEndDate.setHours(23, 59, 59, 999);
      return itemDate >= new Date(startDate) && itemDate <= adjustedEndDate;
    });
  }
  
  return filtered;
});

const totalFilteredItems = computed(() => filteredRfqDataList.value.length);

const filteredRfqData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredRfqDataList.value.slice(start, end);
});

// 搜索和重置
const handleSearch = () => {
  currentPage.value = 1;
  console.log('Searching with:', searchForm);
};

const handleResetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = [];
    } else {
      searchForm[key] = '';
    }
  });
  currentPage.value = 1;
  console.log('Search form reset');
};

// 展开行处理
const handleExpandChange = (row, expandedRowsData) => {
  if (expandedRowsData.includes(row)) {
    expandedRows.value.push(row.id);
  } else {
    const index = expandedRows.value.indexOf(row.id);
    if (index > -1) {
      expandedRows.value.splice(index, 1);
    }
  }
};

// 格式化函数
const formatPrice = (price) => {
  if (price == null || price === '') return '-';
  return Number(price).toLocaleString('zh-CN', { minimumFractionDigits: 2 });
};

const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return '-';
  const date = new Date(dateTimeString);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

const formatInquiryStatus = (status) => {
  switch (status) {
    case 'pending':
      return '待报价';
    case 'quoting':
      return '报价中';
    case 'completed':
      return '已完成';
    case 'expired':
      return '已过期';
    default:
      return '未知';
  }
};

const getInquiryStatusTagType = (status) => {
  switch (status) {
    case 'pending':
      return 'warning';
    case 'quoting':
      return 'info';
    case 'completed':
      return 'success';
    case 'expired':
      return 'danger';
    default:
      return 'default';
  }
};

const formatQuoteStatus = (status) => {
  switch (status) {
    case 'pending':
      return '待确认';
    case 'active':
      return '有效';
    case 'selected':
      return '已选择';
    case 'rejected':
      return '已拒绝';
    default:
      return '未知';
  }
};

const getQuoteStatusTagType = (status) => {
  switch (status) {
    case 'pending':
      return 'warning';
    case 'active':
      return 'info';
    case 'selected':
      return 'success';
    case 'rejected':
      return 'danger';
    default:
      return 'default';
  }
};

// 操作处理函数
const handleViewDetail = (row) => {
  console.log('View detail:', row);
  ElMessage.info(`查看询价单 ${row.rfqNumber} 的详情`);
};

const handleViewQuoteHistory = (row) => {
  console.log('View quote history:', row);
  currentMaterial.value = row;
  quoteHistoryDialogVisible.value = true;
  // 重置搜索条件和分页
  handleResetHistorySearch();
};

const handleEdit = (row) => {
  console.log('Edit:', row);
  ElMessage.info(`编辑询价单 ${row.rfqNumber}`);
};

const handleComplete = (row) => {
  ElMessageBox.confirm(`确认完成询价单 ${row.rfqNumber} 吗？`, '确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    // 更新状态为已完成
    row.inquiryStatus = 'completed';
    ElMessage.success('询价单已完成');
  }).catch(() => {
    ElMessage.info('操作已取消');
  });
};

const handleSelectQuote = (materialRow, quoteRow) => {
  console.log('Select quote:', materialRow, quoteRow);
  ElMessage.success(`已选择供应商 ${quoteRow.supplierName} 的报价`);
  
  // 更新报价状态
  materialRow.supplierQuotes.forEach(quote => {
    quote.quoteStatus = quote === quoteRow ? 'selected' : 'rejected';
  });
  
  // 更新主表格的最优价格信息
  materialRow.bestSupplierPrice = quoteRow.supplierPrice;
  materialRow.salesPrice = quoteRow.salesPrice;
  materialRow.totalPrice = quoteRow.totalPrice;
  materialRow.minOrderQuantity = quoteRow.minOrderQuantity;
  materialRow.deliveryTime = quoteRow.promisedDelivery;
};

const handleViewQuoteDetail = (quoteRow) => {
  console.log('View quote detail:', quoteRow);
  ElMessage.info(`查看供应商 ${quoteRow.supplierName} 的报价详情`);
};

// 分页处理
const handlePageSizeChange = (newPageSize) => {
  pageSize.value = newPageSize;
  currentPage.value = 1;
};

const handleCurrentPageChange = (newPage) => {
  currentPage.value = newPage;
};

// 报价历史相关处理方法
const handleHistorySearch = () => {
  historyCurrentPage.value = 1;
  console.log('Searching history with:', historySearchForm);
};

const handleResetHistorySearch = () => {
  Object.keys(historySearchForm).forEach(key => {
    if (Array.isArray(historySearchForm[key])) {
      historySearchForm[key] = [];
    } else {
      historySearchForm[key] = '';
    }
  });
  historyCurrentPage.value = 1;
  console.log('History search form reset');
};

const handleCloseQuoteHistory = () => {
  quoteHistoryDialogVisible.value = false;
  currentMaterial.value = {};
};

const handleHistoryPageSizeChange = (newPageSize) => {
  historyPageSize.value = newPageSize;
  historyCurrentPage.value = 1;
};

const handleHistoryCurrentPageChange = (newPage) => {
  historyCurrentPage.value = newPage;
};

</script>

<style scoped>
.rfq-list-container {
  /* 主容器样式 */
}

.filter-container {
  margin-bottom: 20px;
  background-color: #f7f8fa;
  padding: 20px;
  border-radius: 4px;
}

.search-form .el-form-item {
  margin-bottom: 10px;
  margin-right: 15px;
}

.table-content-container {
  margin-top: 20px;
}

.inner-table-container {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 4px;
  margin: 0 20px;
}

.inner-table-title {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-table--small .el-table__cell) {
  padding: 6px 0;
}

/* 内嵌表格样式 */
:deep(.inner-table-container .el-table) {
  margin-top: 0;
}

:deep(.inner-table-container .el-table th) {
  background-color: #f5f7fa;
}

/* 报价历史弹出框样式 */
.quote-history-container {
  max-height: 70vh;
  overflow-y: auto;
}

.material-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.material-info h4 {
  margin: 0;
  color: #303133;
  font-weight: 600;
}

.history-filter-container {
  margin-bottom: 20px;
  background-color: #f7f8fa;
  padding: 20px;
  border-radius: 4px;
}

.history-search-form .el-form-item {
  margin-bottom: 10px;
  margin-right: 15px;
}

.history-table-container {
  margin-top: 20px;
}

.history-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 弹出框表格样式 */
:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.quote-history-container .el-table) {
  font-size: 13px;
}

:deep(.quote-history-container .el-table .el-table__cell) {
  padding: 8px 0;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .search-form .el-form-item {
    margin-bottom: 15px;
  }
  
  .history-search-form .el-form-item {
    margin-bottom: 15px;
  }
}
</style>