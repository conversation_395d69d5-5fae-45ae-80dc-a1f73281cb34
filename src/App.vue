<template>
  <div class="app-container">
    <!-- 顶部Header -->
    <header class="app-header">
      <div class="logo">
        <img src="./assets/logo.png" alt="Logo" class="logo-img" />
        <span>研选工场-平台管理系统</span>
      </div>
      <div class="user-info">
        <el-dropdown>
          <span class="user-dropdown">
            管理员 <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>个人信息</el-dropdown-item>
              <el-dropdown-item>修改密码</el-dropdown-item>
              <el-dropdown-item divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>

    <!-- 主体内容区 -->
    <div class="main-container">
      <!-- 左侧菜单 -->
      <div class="sidebar-container">
        <el-menu
          :default-active="activeMenu"
          class="primary-menu"
          background-color="#222222"
          text-color="#fff"
          :collapse="isCollapse"
          unique-opened
          router
          @select="handleSelect"
          mode="vertical"
          style="width: 100px;"
        >
          <el-menu-item index="/dashboard">
            <el-icon><HomeFilled /></el-icon>
            <template #title>首页</template>
          </el-menu-item>
          
          <el-menu-item
            index="/enterprise"
            :class="{'is-active': activeSubmenu === '/enterprise'}"
          >
            <el-icon><OfficeBuilding /></el-icon>
            <template #title>平台</template>
          </el-menu-item>
          
          <el-menu-item
            index="/commodity"
            :class="{'is-active': activeSubmenu === '/commodity'}"
          >
            <el-icon><ShoppingBag /></el-icon>
            <template #title>商品</template>
          </el-menu-item>
          
          <el-menu-item
            index="/trade"
            :class="{'is-active': activeSubmenu === '/trade'}"
          >
            <el-icon><SwitchButton /></el-icon>
            <template #title>交易</template>
          </el-menu-item>
          
          <el-menu-item
            index="/data"
            :class="{'is-active': activeSubmenu === '/data'}"
          >
            <el-icon><DataAnalysis /></el-icon>
            <template #title>数据</template>
          </el-menu-item>
          
          <el-menu-item
            index="/system"
            :class="{'is-active': activeSubmenu === '/system'}"
          >
            <el-icon><Tools /></el-icon>
            <template #title>系统</template>
          </el-menu-item>
          
          <el-menu-item
            index="/agent"
            :class="{'is-active': activeSubmenu === '/agent'}"
          >
            <el-icon><Cpu /></el-icon>
            <template #title>智能体</template>
          </el-menu-item>
        </el-menu>
      </div>
      
      <!-- 二级菜单区域 -->
      <div v-if="showSecondaryMenu" class="secondary-menu-container">
        <div v-if="activeSubmenu === '/product'" class="secondary-menu-content">
          <div class="secondary-menu-title">产品管理</div>
          <el-menu
            :default-active="activeMenu"
            class="secondary-vertical-menu"
            background-color="#fff"
            text-color="#333"
            router
          >
            <el-menu-item index="/product/list">产品列表</el-menu-item>
            <el-menu-item index="/product/category">产品分类</el-menu-item>
          </el-menu>
        </div>
        
        <div v-if="activeSubmenu === '/order'" class="secondary-menu-content">
          <div class="secondary-menu-title">交易管理</div>
          <el-menu
            :default-active="activeMenu"
            class="secondary-vertical-menu"
            background-color="#fff"
            text-color="#333"
            router
          >
            <el-menu-item index="/order/list">交易列表</el-menu-item>
            <el-menu-item index="/order/statistics">交易统计</el-menu-item>
          </el-menu>
        </div>

        <div v-if="activeSubmenu === '/enterprise'" class="secondary-menu-content">
          <el-menu
            :default-active="activeMenu"
            class="secondary-vertical-menu"
            background-color="#fff"
            text-color="#333"
            router
          >
            <el-menu-item index="/enterprise/settings">企业管理</el-menu-item>
            <el-menu-item index="/enterprise/agent">智能体管理</el-menu-item>
          </el-menu>
        </div>

        <div v-if="activeSubmenu === '/commodity'" class="secondary-menu-content">
          <el-menu
            :default-active="activeMenu"
            class="secondary-vertical-menu"
            background-color="#fff"
            text-color="#333"
            router
          >
            <el-menu-item index="/commodity/management">商品管理</el-menu-item>
            <el-menu-item index="/commodity/publish">发布商品</el-menu-item>
            <el-menu-item index="/commodity/category">分类管理</el-menu-item>
            <el-menu-item index="/commodity/specification">规格管理</el-menu-item>
            <el-menu-item index="/commodity/brand">品牌管理</el-menu-item>
            <el-menu-item index="/commodity/model">模型管理</el-menu-item>
          </el-menu>
        </div>

        <div v-if="activeSubmenu === '/trade'" class="secondary-menu-content">
          <el-menu
            :default-active="activeMenu"
            class="secondary-vertical-menu"
            background-color="#fff"
            text-color="#333"
            router
          >
            <el-sub-menu index="/trade/rfq">
              <template #title>询价管理</template>
              <el-menu-item index="/trade/rfq/adjust">报价调整</el-menu-item>
              <el-menu-item index="/trade/rfq/index">询价单</el-menu-item>
            </el-sub-menu>
            <el-sub-menu index="/trade/sale">
              <template #title>销售管理</template>
              <el-menu-item index="/trade/sale/so">销售单</el-menu-item>
              <el-menu-item index="/trade/sale/inv">对账单</el-menu-item>
              <el-menu-item index="/trade/sale/pay">付款单</el-menu-item>
            </el-sub-menu>
            <el-sub-menu index="/trade/buy">
              <template #title>采购管理</template>
              <el-menu-item index="/trade/buy/po">采购单</el-menu-item>
              <el-menu-item index="/trade/buy/inv">对账单</el-menu-item>
              <el-menu-item index="/trade/buy/pay">付款单</el-menu-item>
            </el-sub-menu>
            <el-sub-menu index="/trade/dn">
              <template #title>送货管理</template>
              <el-menu-item index="/trade/dn/index">送货单</el-menu-item>
              <el-menu-item index="/trade/dn/return">退货单</el-menu-item>
            </el-sub-menu>
          </el-menu>
        </div>

        <div v-if="activeSubmenu === '/copywriting'" class="secondary-menu-content">
          <el-menu
            :default-active="activeMenu"
            class="secondary-vertical-menu"
            background-color="#fff"
            text-color="#333"
            router
          >
            <el-menu-item index="/copywriting/management">文案管理</el-menu-item>
            <el-menu-item index="/copywriting/publish">发布文案</el-menu-item>
            <el-menu-item index="/copywriting/tags">标签管理</el-menu-item>
          </el-menu>
        </div>

        <div v-if="activeSubmenu === '/system'" class="secondary-menu-content">
          <el-menu
            :default-active="activeMenu"
            class="secondary-vertical-menu"
            background-color="#fff"
            text-color="#333"
            router
          >
            <el-menu-item index="/system/organization">组织架构</el-menu-item>
            <el-menu-item index="/system/resources">资源管理</el-menu-item>
            <el-menu-item index="/system/messages">消息管理</el-menu-item>
          </el-menu>
        </div>

        <div v-if="activeSubmenu === '/data'" class="secondary-menu-content">
          <el-menu
            :default-active="activeMenu"
            class="secondary-vertical-menu"
            background-color="#fff"
            text-color="#333"
            router
          >
            <el-sub-menu index="/data/supplier">
              <template #title>供应商</template>
              <el-menu-item index="/data/supplier/index">报价调整</el-menu-item>
            </el-sub-menu>
            <el-sub-menu index="/trade/part">
              <template #title>零部件</template>
              <el-menu-item index="/data/part/jobs">导入任务</el-menu-item>
              <el-menu-item index="/data/part/mapping">字段映射</el-menu-item>
              <el-menu-item index="/data/part/rawRecords">原始记录</el-menu-item>
              <el-menu-item index="/data/part/errors">错误与修复</el-menu-item>
            </el-sub-menu>
          </el-menu>
        </div>

        <div v-if="activeSubmenu === '/decoration'" class="secondary-menu-content">
          <el-menu
            :default-active="activeMenu"
            class="secondary-vertical-menu"
            background-color="#fff"
            text-color="#333"
            router
          >
            <el-menu-item index="/decoration/carousel">轮播管理</el-menu-item>
          </el-menu>
        </div>

        <div v-if="activeSubmenu === '/agent'" class="secondary-menu-content">
          <el-menu
            :default-active="activeMenu"
            class="secondary-vertical-menu"
            background-color="#fff"
            text-color="#333"
            router
          >
            <el-menu-item index="/agent/info">基本信息</el-menu-item>
            <el-menu-item index="/agent/logs">会话日志</el-menu-item>
            <el-menu-item index="/agent/knowledge">知识库</el-menu-item>
          </el-menu>
        </div>
      </div>
      
      <!-- Wrapper for breadcrumbs and main content -->
      <div class="main-content-area" :class="{'content-shifted': showSecondaryMenu}">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-container">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">活动管理</a></el-breadcrumb-item>
            <el-breadcrumb-item>活动列表</el-breadcrumb-item>
            <el-breadcrumb-item>活动详情</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <!-- 主内容区 -->
        <div class="content-container">
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
// const router = useRouter(); // Not directly used in this logic block, but often needed.
const isCollapse = ref(false) // For primary menu collapse functionality
const activeSubmenu = ref('') // Stores the parent path of the active submenu, e.g., '/product', or '' if none

// Define parent paths that have a corresponding secondary menu structure in the template
const definedSubmenuParentPaths = [
  '/product', '/order', '/enterprise', '/commodity', '/trade',
  '/data', '/copywriting', '/system', '/decoration', '/agent'
];

// Function to update activeSubmenu based on the current route path
const updateActiveSubmenuFromPath = (currentPath) => {
  let newActiveParent = '';
  // Check if currentPath starts with any of the defined parent paths
  for (const parentPath of definedSubmenuParentPaths) {
    if (currentPath.startsWith(parentPath)) {
      newActiveParent = parentPath;
      break;
    }
  }
  activeSubmenu.value = newActiveParent;
};

// Computed property for the :default-active prop of the primary el-menu
// This also ensures activeSubmenu is updated whenever the route (and thus activeMenu) might change
const activeMenu = computed(() => {
  const { meta, path } = route;
  updateActiveSubmenuFromPath(path); // Update activeSubmenu based on the current path

  if (meta.activeMenu) {
    return meta.activeMenu; // Use meta.activeMenu if defined (for highlighting)
  }
  return path; // Otherwise, use the full path for highlighting
});

// Computed property to control visibility of the secondary menu container
const showSecondaryMenu = computed(() => {
  // The secondary menu should show if activeSubmenu is set to one of the valid parent paths.
  // Since updateActiveSubmenuFromPath ensures activeSubmenu is either a valid parent path or '',
  // we just need to check if it's not empty.
  return activeSubmenu.value !== '';
});

// Handle primary menu selection (optional, e.g., for logging or other side effects)
// Submenu visibility is now driven by route changes and the computed properties above.
const handleSelect = (key, keyPath) => {
  console.log('Primary menu item selected. Key:', key, 'Path:', keyPath);
  // Navigation is handled by the :index and router props on el-menu.
  // Route changes will trigger updateActiveSubmenuFromPath via activeMenu computed or watch.
};

// Watch for route changes to ensure activeSubmenu is always in sync
watch(() => route.path, (newPath) => {
  updateActiveSubmenuFromPath(newPath);
});

// On component mount, initialize activeSubmenu based on the initial route
onMounted(() => {
  updateActiveSubmenuFromPath(route.path);
});
</script>

<style scoped>
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止整个应用溢出 */
  width: 100%; /* 确保不超出视口宽度 */
}

.app-header {
  height: 50px;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.logo-img {
  height: 32px;
  margin-right: 10px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #333;
}

.main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
  width: 100%; /* 确保不会超出父容器 */
}

.sidebar-container {
  display: flex;
  height: 100%;
  position: relative;
  z-index: 10;
}

.primary-menu {
  width: 120px;
  height: 100%;
  border-right: none;
}

.primary-menu:not(.el-menu--collapse) {
  width: 120px;
}

/* 二级菜单容器 */
.secondary-menu-container {
  flex: 0 0 130px;
  width: 130px;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  z-index: 9;
  transition: all 0.3s;
  overflow-y: auto;
}

.secondary-menu-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.secondary-vertical-menu {
  border-right: none;
  background-color: #fff;
}

.secondary-vertical-menu .el-menu-item {
  height: 45px;
  line-height: 45px;
  color: #333;
}

.secondary-vertical-menu .el-menu-item.is-active {
  background-color: #f0f6ff !important;
  color: #f94c30 !important;
  border-right: 3px solid #f94c30;
}

/* Add submenu styles */
:deep(.secondary-vertical-menu .el-sub-menu__title) {
  height: 45px;
  line-height: 45px;
  color: #333;
}

:deep(.secondary-vertical-menu .el-sub-menu__title:hover) {
  background-color: #f5f5f5;
}

:deep(.secondary-vertical-menu .el-menu-item) {
  padding-left: 40px !important; /* Indent submenu items */
}

:deep(.secondary-vertical-menu .el-sub-menu .el-menu-item) {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

/* 自定义Element Plus样式 */
:deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
  position: relative;
}

:deep(.el-menu-item.is-active) {
  background-color: #1e1e1e !important;
  color: #f94c30 !important;
}

/* 为产品和交易管理菜单添加右箭头指示器 */
:deep(.el-menu-item[index="/product"]),
:deep(.el-menu-item[index="/order"]),
:deep(.el-menu-item[index="/enterprise"]),
:deep(.el-menu-item[index="/commodity"]),
:deep(.el-menu-item[index="/trade"]),
:deep(.el-menu-item[index="/data"]),
:deep(.el-menu-item[index="/copywriting"]),
:deep(.el-menu-item[index="/system"]),
:deep(.el-menu-item[index="/decoration"]),
:deep(.el-menu-item[index="/agent"]) {
  position: relative;
}

:deep(.el-menu-item[index="/product"])::after,
:deep(.el-menu-item[index="/order"])::after,
:deep(.el-menu-item[index="/enterprise"])::after,
:deep(.el-menu-item[index="/commodity"])::after,
:deep(.el-menu-item[index="/trade"])::after,
:deep(.el-menu-item[index="/data"])::after,
:deep(.el-menu-item[index="/copywriting"])::after,
:deep(.el-menu-item[index="/system"])::after,
:deep(.el-menu-item[index="/decoration"])::after,
:deep(.el-menu-item[index="/agent"])::after {
  content: '›';
  position: absolute;
  right: 15px;
  font-size: 18px;
  transform: rotate(0deg);
  transition: transform 0.3s;
}

:deep(.el-menu-item[index="/product"].is-active)::after,
:deep(.el-menu-item[index="/order"].is-active)::after,
:deep(.el-menu-item[index="/enterprise"].is-active)::after,
:deep(.el-menu-item[index="/commodity"].is-active)::after,
:deep(.el-menu-item[index="/trade"].is-active)::after,
:deep(.el-menu-item[index="/data"].is-active)::after,
:deep(.el-menu-item[index="/copywriting"].is-active)::after,
:deep(.el-menu-item[index="/system"].is-active)::after,
:deep(.el-menu-item[index="/decoration"].is-active)::after,
:deep(.el-menu-item[index="/agent"].is-active)::after {
  transform: rotate(90deg);
}

/* 处理菜单图标 */
:deep(.el-menu-item .el-icon),
:deep(.el-sub-menu__title .el-icon) {
  margin-right: 10px;
  font-size: 18px;
}

/* 主内容区域 */
.main-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* 防止flex子元素溢出 */
  overflow: hidden;
}

.content-container {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.breadcrumb-container {
  padding: 10px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  transition: all 0.3s ease;
}
</style>